/* Mobile Responsive Styles for CTNL AI Work-Board */

/* Base mobile styles */
@media (max-width: 768px) {
  /* Container adjustments */
  .container {
    padding-left: 1rem;
    padding-right: 1rem;
  }

  /* Navigation adjustments */
  .nav {
    padding: 0.5rem 1rem;
  }

  .nav-link {
    padding: 0.5rem;
    font-size: 0.9rem;
  }

  /* Button adjustments */
  .btn {
    padding: 0.75rem 1rem;
    font-size: 0.9rem;
  }

  /* Card adjustments */
  .card {
    margin-bottom: 1rem;
    padding: 1rem;
  }

  /* Form adjustments */
  .form-group {
    margin-bottom: 1rem;
  }

  .form-input {
    padding: 0.75rem;
    font-size: 1rem;
  }

  /* Dashboard adjustments */
  .dashboard-grid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .dashboard-card {
    padding: 1rem;
  }

  /* AI interface adjustments */
  .ai-interface {
    padding: 1rem;
  }

  .ai-chat-container {
    height: 60vh;
  }

  /* Table adjustments */
  .table-responsive {
    overflow-x: auto;
  }

  .table {
    font-size: 0.9rem;
  }

  /* Modal adjustments */
  .modal-content {
    margin: 1rem;
    max-height: 90vh;
    overflow-y: auto;
  }

  /* Sidebar adjustments */
  .sidebar {
    width: 100%;
    position: fixed;
    top: 0;
    left: 0;
    z-index: 1000;
    transform: translateX(-100%);
    transition: transform 0.3s ease;
  }

  .sidebar.open {
    transform: translateX(0);
  }

  /* Text adjustments */
  .text-lg {
    font-size: 1.1rem;
  }

  .text-xl {
    font-size: 1.2rem;
  }

  .text-2xl {
    font-size: 1.4rem;
  }

  .text-3xl {
    font-size: 1.6rem;
  }

  /* Spacing adjustments */
  .p-6 {
    padding: 1rem;
  }

  .p-8 {
    padding: 1.5rem;
  }

  .m-6 {
    margin: 1rem;
  }

  .m-8 {
    margin: 1.5rem;
  }
}

/* Small mobile devices */
@media (max-width: 480px) {
  /* Even smaller adjustments */
  .container {
    padding-left: 0.5rem;
    padding-right: 0.5rem;
  }

  .card {
    padding: 0.75rem;
  }

  .btn {
    padding: 0.5rem 0.75rem;
    font-size: 0.85rem;
  }

  .form-input {
    padding: 0.5rem;
  }

  .ai-chat-container {
    height: 50vh;
  }

  .modal-content {
    margin: 0.5rem;
  }

  /* Hide non-essential elements on very small screens */
  .hide-on-small {
    display: none;
  }

  /* Stack elements vertically */
  .flex-row-mobile {
    flex-direction: column;
  }

  .grid-cols-mobile {
    grid-template-columns: 1fr;
  }
}

/* Landscape mobile */
@media (max-width: 768px) and (orientation: landscape) {
  .ai-chat-container {
    height: 40vh;
  }

  .modal-content {
    max-height: 80vh;
  }
}

/* Touch-friendly adjustments */
@media (pointer: coarse) {
  /* Larger touch targets */
  .btn,
  .nav-link,
  .form-input,
  .clickable {
    min-height: 44px;
    min-width: 44px;
  }

  /* Better spacing for touch */
  .touch-spacing {
    margin: 0.5rem;
  }
}

/* High DPI displays */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
  /* Crisp borders and shadows */
  .card,
  .btn,
  .form-input {
    border-width: 0.5px;
  }
}

/* Utility classes for mobile */
.mobile-only {
  display: none;
}

.desktop-only {
  display: block;
}

@media (max-width: 768px) {
  .mobile-only {
    display: block;
  }

  .desktop-only {
    display: none;
  }

  .mobile-hidden {
    display: none;
  }

  .mobile-full-width {
    width: 100%;
  }

  .mobile-text-center {
    text-align: center;
  }

  .mobile-p-2 {
    padding: 0.5rem;
  }

  .mobile-p-4 {
    padding: 1rem;
  }

  .mobile-m-2 {
    margin: 0.5rem;
  }

  .mobile-m-4 {
    margin: 1rem;
  }
}

/* Safe area adjustments for notched devices */
@supports (padding: max(0px)) {
  .safe-area-top {
    padding-top: max(1rem, env(safe-area-inset-top));
  }

  .safe-area-bottom {
    padding-bottom: max(1rem, env(safe-area-inset-bottom));
  }

  .safe-area-left {
    padding-left: max(1rem, env(safe-area-inset-left));
  }

  .safe-area-right {
    padding-right: max(1rem, env(safe-area-inset-right));
  }
}

/* Accessibility improvements for mobile */
@media (prefers-reduced-motion: reduce) {
  .sidebar {
    transition: none;
  }

  .animated {
    animation: none;
  }
}

/* Dark mode adjustments for mobile */
@media (prefers-color-scheme: dark) and (max-width: 768px) {
  .card {
    background: rgba(0, 0, 0, 0.9);
    border-color: rgba(255, 0, 0, 0.3);
  }

  .btn-primary {
    background: linear-gradient(135deg, #ff0000 0%, #cc0000 100%);
  }
}
