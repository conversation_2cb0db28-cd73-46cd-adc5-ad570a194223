/**
 * File Management Page Component
 * Features: Upload, organize, and manage files with preview capabilities
 */
import React, { useState, useEffect, useCallback, useRef } from 'react'
import { logger, <PERSON>rrorHandler, PerformanceMonitor } from '../utils/logger'
import type { User } from '../types'

interface FileItem {
  id: string
  name: string
  size: number
  type: string
  category: string
  url: string
  uploaded_by: string
  uploaded_at: string
  project_id?: string
  task_id?: string
}

interface FilesPageProps {
  user: User
  onNavigateBack?: () => void
}

interface FilesState {
  isLoading: boolean
  files: FileItem[]
  filteredFiles: FileItem[]
  isUploading: boolean
  uploadProgress: number
  selectedFiles: File[]
  showUploadModal: boolean
  showPreviewModal: boolean
  selectedFile: FileItem | null
  filters: {
    category: string
    type: string
    search: string
  }
  error: string | null
}

const fileCategories = [
  { value: 'all', label: 'All Files' },
  { value: 'image', label: 'Images' },
  { value: 'document', label: 'Documents' },
  { value: 'spreadsheet', label: 'Spreadsheets' },
  { value: 'presentation', label: 'Presentations' },
  { value: 'archive', label: 'Archives' },
  { value: 'video', label: 'Videos' },
  { value: 'audio', label: 'Audio' },
  { value: 'other', label: 'Other' }
]

export default function FilesPage({ user, onNavigateBack }: FilesPageProps) {
  const fileInputRef = useRef<HTMLInputElement>(null)
  
  const [state, setState] = useState<FilesState>({
    isLoading: true,
    files: [],
    filteredFiles: [],
    isUploading: false,
    uploadProgress: 0,
    selectedFiles: [],
    showUploadModal: false,
    showPreviewModal: false,
    selectedFile: null,
    filters: {
      category: 'all',
      type: 'all',
      search: ''
    },
    error: null
  })

  useEffect(() => {
    logger.componentMount('FilesPage')
    loadFilesData()
    
    return () => {
      logger.componentUnmount('FilesPage')
    }
  }, [])

  useEffect(() => {
    applyFilters()
  }, [state.files, state.filters])

  const loadFilesData = useCallback(async () => {
    try {
      setState(prev => ({ ...prev, isLoading: true, error: null }))
      logger.info('Loading files data', 'FilesPage')
      
      PerformanceMonitor.startTimer('files_load')

      const response = await fetch('http://localhost:8002/api/files', {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('authToken')}`,
          'Content-Type': 'application/json'
        }
      })

      const duration = PerformanceMonitor.endTimer('files_load')

      if (response.ok) {
        const data = await response.json()
        setState(prev => ({
          ...prev,
          isLoading: false,
          files: data.files || []
        }))

        logger.info(`Files loaded in ${duration.toFixed(2)}ms`, 'FilesPage', {
          fileCount: data.files?.length || 0
        })
      } else {
        // Mock data if API fails
        const mockFiles: FileItem[] = [
          {
            id: '1',
            name: 'project-proposal.pdf',
            size: 2048576,
            type: 'application/pdf',
            category: 'document',
            url: '/mock/project-proposal.pdf',
            uploaded_by: user.username,
            uploaded_at: new Date().toISOString()
          },
          {
            id: '2',
            name: 'team-photo.jpg',
            size: 1024000,
            type: 'image/jpeg',
            category: 'image',
            url: '/mock/team-photo.jpg',
            uploaded_by: user.username,
            uploaded_at: new Date(Date.now() - 86400000).toISOString()
          }
        ]

        setState(prev => ({
          ...prev,
          isLoading: false,
          files: mockFiles
        }))

        logger.info('Using mock files data', 'FilesPage')
      }
      
    } catch (error) {
      const handledError = ErrorHandler.handleApiError(error, 'FilesPage')
      setState(prev => ({ 
        ...prev, 
        isLoading: false, 
        error: handledError.message 
      }))
      logger.error('Failed to load files data', 'FilesPage', error)
    }
  }, [user.username])

  const applyFilters = useCallback(() => {
    let filtered = [...state.files]

    if (state.filters.category !== 'all') {
      filtered = filtered.filter(file => file.category === state.filters.category)
    }
    if (state.filters.search) {
      const searchLower = state.filters.search.toLowerCase()
      filtered = filtered.filter(file => 
        file.name.toLowerCase().includes(searchLower)
      )
    }

    setState(prev => ({ ...prev, filteredFiles: filtered }))
  }, [state.files, state.filters])

  const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(event.target.files || [])
    setState(prev => ({ ...prev, selectedFiles: files, showUploadModal: true }))
  }

  const handleUpload = async () => {
    if (state.selectedFiles.length === 0) return

    try {
      setState(prev => ({ ...prev, isUploading: true, uploadProgress: 0 }))
      
      for (let i = 0; i < state.selectedFiles.length; i++) {
        const file = state.selectedFiles[i]
        const formData = new FormData()
        formData.append('file', file)
        formData.append('category', getCategoryFromFile(file))

        const response = await fetch('http://localhost:8002/api/files/upload', {
          method: 'POST',
          headers: {
            'Authorization': `Bearer ${localStorage.getItem('authToken')}`
          },
          body: formData
        })

        if (response.ok) {
          const uploadedFile = await response.json()
          setState(prev => ({
            ...prev,
            files: [uploadedFile, ...prev.files]
          }))
        } else {
          // Mock upload behavior
          const mockFile: FileItem = {
            id: Date.now().toString() + i,
            name: file.name,
            size: file.size,
            type: file.type,
            category: getCategoryFromFile(file),
            url: URL.createObjectURL(file),
            uploaded_by: user.username,
            uploaded_at: new Date().toISOString()
          }
          
          setState(prev => ({
            ...prev,
            files: [mockFile, ...prev.files]
          }))
        }

        setState(prev => ({ 
          ...prev, 
          uploadProgress: ((i + 1) / state.selectedFiles.length) * 100 
        }))
      }

      setState(prev => ({
        ...prev,
        isUploading: false,
        uploadProgress: 0,
        selectedFiles: [],
        showUploadModal: false
      }))

      logger.info('Files uploaded successfully', 'FilesPage', {
        fileCount: state.selectedFiles.length
      })
      
    } catch (error) {
      logger.error('Failed to upload files', 'FilesPage', error)
      setState(prev => ({ ...prev, isUploading: false, uploadProgress: 0 }))
      alert('Failed to upload files. Please try again.')
    }
  }

  const getCategoryFromFile = (file: File): string => {
    const type = file.type.toLowerCase()
    if (type.startsWith('image/')) return 'image'
    if (type.includes('pdf') || type.includes('document') || type.includes('text')) return 'document'
    if (type.includes('spreadsheet') || type.includes('excel') || type.includes('csv')) return 'spreadsheet'
    if (type.includes('presentation') || type.includes('powerpoint')) return 'presentation'
    if (type.includes('zip') || type.includes('rar') || type.includes('archive')) return 'archive'
    if (type.startsWith('video/')) return 'video'
    if (type.startsWith('audio/')) return 'audio'
    return 'other'
  }

  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 Bytes'
    const k = 1024
    const sizes = ['Bytes', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  }

  const getFileIcon = (category: string): string => {
    switch (category) {
      case 'image': return '🖼️'
      case 'document': return '📄'
      case 'spreadsheet': return '📊'
      case 'presentation': return '📽️'
      case 'archive': return '📦'
      case 'video': return '🎥'
      case 'audio': return '🎵'
      default: return '📁'
    }
  }

  const handleDownload = async (file: FileItem) => {
    try {
      const response = await fetch(`http://localhost:8002/api/files/${file.id}/download`, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('authToken')}`
        }
      })

      if (response.ok) {
        const blob = await response.blob()
        const url = window.URL.createObjectURL(blob)
        const a = document.createElement('a')
        a.href = url
        a.download = file.name
        document.body.appendChild(a)
        a.click()
        window.URL.revokeObjectURL(url)
        document.body.removeChild(a)
        
        logger.info('File downloaded', 'FilesPage', { fileId: file.id, fileName: file.name })
      } else {
        // Mock download behavior
        const a = document.createElement('a')
        a.href = file.url
        a.download = file.name
        a.click()
        
        logger.info('Mock file download', 'FilesPage', { fileName: file.name })
      }
    } catch (error) {
      logger.error('Failed to download file', 'FilesPage', error)
      alert('Failed to download file. Please try again.')
    }
  }

  const handleDelete = async (file: FileItem) => {
    if (!confirm(`Are you sure you want to delete "${file.name}"?`)) return

    try {
      const response = await fetch(`http://localhost:8002/api/files/${file.id}`, {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('authToken')}`
        }
      })

      if (response.ok || response.status === 404) {
        setState(prev => ({
          ...prev,
          files: prev.files.filter(f => f.id !== file.id)
        }))
        
        logger.info('File deleted', 'FilesPage', { fileId: file.id, fileName: file.name })
      } else {
        throw new Error('Failed to delete file')
      }
    } catch (error) {
      logger.error('Failed to delete file', 'FilesPage', error)
      alert('Failed to delete file. Please try again.')
    }
  }

  if (state.error) {
    return (
      <div style={{
        minHeight: '100vh',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        backgroundColor: '#f9fafb',
        padding: '20px'
      }}>
        <div style={{
          maxWidth: '500px',
          width: '100%',
          backgroundColor: 'white',
          padding: '40px',
          borderRadius: '16px',
          boxShadow: '0 10px 15px -3px rgba(0, 0, 0, 0.1)',
          textAlign: 'center'
        }}>
          <div style={{ fontSize: '48px', marginBottom: '20px' }}>⚠️</div>
          <h2 style={{ color: '#ef4444', marginBottom: '16px' }}>Files Error</h2>
          <p style={{ color: '#6b7280', marginBottom: '24px' }}>{state.error}</p>
          <button
            onClick={loadFilesData}
            style={{
              padding: '12px 24px',
              backgroundColor: '#667eea',
              color: 'white',
              border: 'none',
              borderRadius: '8px',
              cursor: 'pointer',
              marginRight: '12px'
            }}
          >
            Try Again
          </button>
          <button
            onClick={onNavigateBack}
            style={{
              padding: '12px 24px',
              backgroundColor: '#6b7280',
              color: 'white',
              border: 'none',
              borderRadius: '8px',
              cursor: 'pointer'
            }}
          >
            Go Back
          </button>
        </div>
      </div>
    )
  }

  return (
    <div style={{ minHeight: '100vh', backgroundColor: '#f9fafb' }}>
      {/* Header */}
      <header style={{
        backgroundColor: 'white',
        boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1)',
        padding: '16px 0'
      }}>
        <div style={{
          maxWidth: '1400px',
          margin: '0 auto',
          padding: '0 24px',
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center'
        }}>
          <div style={{ display: 'flex', alignItems: 'center', gap: '16px' }}>
            <button
              onClick={onNavigateBack}
              style={{
                padding: '8px 12px',
                backgroundColor: '#f3f4f6',
                border: 'none',
                borderRadius: '6px',
                cursor: 'pointer',
                fontSize: '14px'
              }}
            >
              ← Back
            </button>
            <div>
              <h1 style={{ 
                margin: 0, 
                color: '#1f2937', 
                fontSize: '24px',
                fontWeight: 'bold'
              }}>
                📁 File Management
              </h1>
              <p style={{ 
                margin: '4px 0 0 0', 
                color: '#6b7280', 
                fontSize: '14px' 
              }}>
                Upload, organize, and manage your files
              </p>
            </div>
          </div>
          
          <div style={{ display: 'flex', gap: '12px' }}>
            <input
              ref={fileInputRef}
              type="file"
              multiple
              onChange={handleFileSelect}
              style={{ display: 'none' }}
            />
            <button
              onClick={() => fileInputRef.current?.click()}
              style={{
                padding: '12px 20px',
                backgroundColor: '#667eea',
                color: 'white',
                border: 'none',
                borderRadius: '8px',
                cursor: 'pointer',
                fontSize: '14px',
                fontWeight: '500',
                display: 'flex',
                alignItems: 'center',
                gap: '8px'
              }}
            >
              📤 Upload Files
            </button>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main style={{ maxWidth: '1400px', margin: '0 auto', padding: '24px' }}>
        {/* Filters */}
        <div style={{
          backgroundColor: 'white',
          borderRadius: '12px',
          padding: '24px',
          marginBottom: '24px',
          boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)'
        }}>
          <h2 style={{ 
            fontSize: '18px', 
            fontWeight: 'bold', 
            color: '#1f2937', 
            marginBottom: '20px' 
          }}>
            🔍 Filters & Search
          </h2>
          
          <div style={{
            display: 'grid',
            gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))',
            gap: '16px'
          }}>
            <div>
              <label style={{ 
                display: 'block', 
                fontSize: '14px', 
                fontWeight: '500', 
                color: '#374151', 
                marginBottom: '6px' 
              }}>
                Category
              </label>
              <select
                value={state.filters.category}
                onChange={(e) => setState(prev => ({
                  ...prev,
                  filters: { ...prev.filters, category: e.target.value }
                }))}
                style={{
                  width: '100%',
                  padding: '8px 12px',
                  border: '1px solid #d1d5db',
                  borderRadius: '6px',
                  fontSize: '14px',
                  backgroundColor: 'white'
                }}
              >
                {fileCategories.map(category => (
                  <option key={category.value} value={category.value}>
                    {category.label}
                  </option>
                ))}
              </select>
            </div>

            <div>
              <label style={{ 
                display: 'block', 
                fontSize: '14px', 
                fontWeight: '500', 
                color: '#374151', 
                marginBottom: '6px' 
              }}>
                Search
              </label>
              <input
                type="text"
                placeholder="Search files..."
                value={state.filters.search}
                onChange={(e) => setState(prev => ({
                  ...prev,
                  filters: { ...prev.filters, search: e.target.value }
                }))}
                style={{
                  width: '100%',
                  padding: '8px 12px',
                  border: '1px solid #d1d5db',
                  borderRadius: '6px',
                  fontSize: '14px'
                }}
              />
            </div>
          </div>
        </div>

        {/* Files Grid */}
        <div style={{
          backgroundColor: 'white',
          borderRadius: '12px',
          padding: '24px',
          boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)'
        }}>
          <div style={{
            display: 'flex',
            justifyContent: 'space-between',
            alignItems: 'center',
            marginBottom: '20px'
          }}>
            <h2 style={{ 
              fontSize: '18px', 
              fontWeight: 'bold', 
              color: '#1f2937',
              margin: 0
            }}>
              📁 Files ({state.filteredFiles.length})
            </h2>
            
            <button
              onClick={loadFilesData}
              disabled={state.isLoading}
              style={{
                padding: '8px 16px',
                backgroundColor: state.isLoading ? '#9ca3af' : '#f3f4f6',
                border: '1px solid #d1d5db',
                borderRadius: '6px',
                cursor: state.isLoading ? 'not-allowed' : 'pointer',
                fontSize: '14px',
                display: 'flex',
                alignItems: 'center',
                gap: '8px'
              }}
            >
              {state.isLoading ? (
                <div style={{
                  width: '16px',
                  height: '16px',
                  border: '2px solid transparent',
                  borderTop: '2px solid #6b7280',
                  borderRadius: '50%',
                  animation: 'spin 1s linear infinite'
                }} />
              ) : (
                '🔄'
              )}
              Refresh
            </button>
          </div>
          
          {state.isLoading ? (
            <div style={{
              display: 'flex',
              justifyContent: 'center',
              alignItems: 'center',
              height: '200px'
            }}>
              <div style={{
                width: '40px',
                height: '40px',
                border: '4px solid #e5e7eb',
                borderTop: '4px solid #667eea',
                borderRadius: '50%',
                animation: 'spin 1s linear infinite'
              }} />
            </div>
          ) : state.filteredFiles.length > 0 ? (
            <div style={{
              display: 'grid',
              gridTemplateColumns: 'repeat(auto-fill, minmax(280px, 1fr))',
              gap: '16px'
            }}>
              {state.filteredFiles.map((file) => (
                <div
                  key={file.id}
                  style={{
                    padding: '16px',
                    border: '1px solid #e5e7eb',
                    borderRadius: '8px',
                    backgroundColor: '#fafafa',
                    transition: 'all 0.2s'
                  }}
                  onMouseEnter={(e) => {
                    e.currentTarget.style.backgroundColor = '#f3f4f6'
                    e.currentTarget.style.borderColor = '#d1d5db'
                  }}
                  onMouseLeave={(e) => {
                    e.currentTarget.style.backgroundColor = '#fafafa'
                    e.currentTarget.style.borderColor = '#e5e7eb'
                  }}
                >
                  <div style={{
                    display: 'flex',
                    alignItems: 'center',
                    marginBottom: '12px'
                  }}>
                    <span style={{ fontSize: '24px', marginRight: '12px' }}>
                      {getFileIcon(file.category)}
                    </span>
                    <div style={{ flex: 1, minWidth: 0 }}>
                      <h3 style={{
                        margin: 0,
                        fontSize: '14px',
                        fontWeight: '600',
                        color: '#1f2937',
                        overflow: 'hidden',
                        textOverflow: 'ellipsis',
                        whiteSpace: 'nowrap'
                      }}>
                        {file.name}
                      </h3>
                      <p style={{
                        margin: '2px 0 0 0',
                        fontSize: '12px',
                        color: '#6b7280'
                      }}>
                        {formatFileSize(file.size)}
                      </p>
                    </div>
                  </div>
                  
                  <div style={{
                    display: 'flex',
                    justifyContent: 'space-between',
                    alignItems: 'center',
                    fontSize: '12px',
                    color: '#6b7280',
                    marginBottom: '12px'
                  }}>
                    <span>By: {file.uploaded_by}</span>
                    <span>{new Date(file.uploaded_at).toLocaleDateString()}</span>
                  </div>
                  
                  <div style={{
                    display: 'flex',
                    gap: '8px'
                  }}>
                    <button
                      onClick={() => setState(prev => ({ 
                        ...prev, 
                        selectedFile: file, 
                        showPreviewModal: true 
                      }))}
                      style={{
                        flex: 1,
                        padding: '6px 12px',
                        backgroundColor: '#3b82f6',
                        color: 'white',
                        border: 'none',
                        borderRadius: '4px',
                        cursor: 'pointer',
                        fontSize: '12px'
                      }}
                    >
                      👁️ View
                    </button>
                    <button
                      onClick={() => handleDownload(file)}
                      style={{
                        flex: 1,
                        padding: '6px 12px',
                        backgroundColor: '#10b981',
                        color: 'white',
                        border: 'none',
                        borderRadius: '4px',
                        cursor: 'pointer',
                        fontSize: '12px'
                      }}
                    >
                      📥 Download
                    </button>
                    {(file.uploaded_by === user.username || user.role === 'admin') && (
                      <button
                        onClick={() => handleDelete(file)}
                        style={{
                          padding: '6px 8px',
                          backgroundColor: '#ef4444',
                          color: 'white',
                          border: 'none',
                          borderRadius: '4px',
                          cursor: 'pointer',
                          fontSize: '12px'
                        }}
                      >
                        🗑️
                      </button>
                    )}
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div style={{
              textAlign: 'center',
              color: '#6b7280',
              fontStyle: 'italic',
              padding: '60px 0'
            }}>
              <div style={{ fontSize: '48px', marginBottom: '16px' }}>📁</div>
              <p>No files found matching your criteria</p>
              <button
                onClick={() => fileInputRef.current?.click()}
                style={{
                  marginTop: '16px',
                  padding: '12px 20px',
                  backgroundColor: '#667eea',
                  color: 'white',
                  border: 'none',
                  borderRadius: '8px',
                  cursor: 'pointer',
                  fontSize: '14px'
                }}
              >
                Upload Your First File
              </button>
            </div>
          )}
        </div>
      </main>

      {/* Upload Modal */}
      {state.showUploadModal && (
        <div style={{
          position: 'fixed',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          backgroundColor: 'rgba(0, 0, 0, 0.5)',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          zIndex: 1000
        }}>
          <div style={{
            backgroundColor: 'white',
            borderRadius: '12px',
            padding: '32px',
            maxWidth: '500px',
            width: '90%'
          }}>
            <h2 style={{
              fontSize: '20px',
              fontWeight: 'bold',
              color: '#1f2937',
              marginBottom: '24px'
            }}>
              📤 Upload Files
            </h2>
            
            <div style={{ marginBottom: '24px' }}>
              <h3 style={{ fontSize: '16px', fontWeight: '600', color: '#1f2937', marginBottom: '12px' }}>
                Selected Files ({state.selectedFiles.length})
              </h3>
              <div style={{ maxHeight: '200px', overflowY: 'auto' }}>
                {state.selectedFiles.map((file, index) => (
                  <div
                    key={index}
                    style={{
                      display: 'flex',
                      justifyContent: 'space-between',
                      alignItems: 'center',
                      padding: '8px 0',
                      borderBottom: '1px solid #e5e7eb'
                    }}
                  >
                    <span style={{ fontSize: '14px', color: '#1f2937' }}>{file.name}</span>
                    <span style={{ fontSize: '12px', color: '#6b7280' }}>
                      {formatFileSize(file.size)}
                    </span>
                  </div>
                ))}
              </div>
            </div>

            {state.isUploading && (
              <div style={{ marginBottom: '24px' }}>
                <div style={{
                  width: '100%',
                  height: '8px',
                  backgroundColor: '#e5e7eb',
                  borderRadius: '4px',
                  overflow: 'hidden'
                }}>
                  <div style={{
                    width: `${state.uploadProgress}%`,
                    height: '100%',
                    backgroundColor: '#10b981',
                    transition: 'width 0.3s ease'
                  }} />
                </div>
                <p style={{
                  fontSize: '14px',
                  color: '#6b7280',
                  margin: '8px 0 0 0',
                  textAlign: 'center'
                }}>
                  Uploading... {Math.round(state.uploadProgress)}%
                </p>
              </div>
            )}

            <div style={{
              display: 'flex',
              justifyContent: 'flex-end',
              gap: '12px'
            }}>
              <button
                onClick={() => setState(prev => ({ 
                  ...prev, 
                  showUploadModal: false, 
                  selectedFiles: [] 
                }))}
                disabled={state.isUploading}
                style={{
                  padding: '12px 20px',
                  backgroundColor: '#f3f4f6',
                  border: '1px solid #d1d5db',
                  borderRadius: '6px',
                  cursor: state.isUploading ? 'not-allowed' : 'pointer',
                  fontSize: '14px'
                }}
              >
                Cancel
              </button>
              <button
                onClick={handleUpload}
                disabled={state.selectedFiles.length === 0 || state.isUploading}
                style={{
                  padding: '12px 20px',
                  backgroundColor: (state.selectedFiles.length === 0 || state.isUploading) ? '#9ca3af' : '#667eea',
                  color: 'white',
                  border: 'none',
                  borderRadius: '6px',
                  cursor: (state.selectedFiles.length === 0 || state.isUploading) ? 'not-allowed' : 'pointer',
                  fontSize: '14px',
                  fontWeight: '500'
                }}
              >
                {state.isUploading ? 'Uploading...' : 'Upload Files'}
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Preview Modal */}
      {state.showPreviewModal && state.selectedFile && (
        <div style={{
          position: 'fixed',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          backgroundColor: 'rgba(0, 0, 0, 0.5)',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          zIndex: 1000
        }}>
          <div style={{
            backgroundColor: 'white',
            borderRadius: '12px',
            padding: '32px',
            maxWidth: '800px',
            width: '90%',
            maxHeight: '90vh',
            overflowY: 'auto'
          }}>
            <div style={{
              display: 'flex',
              justifyContent: 'space-between',
              alignItems: 'center',
              marginBottom: '24px'
            }}>
              <h2 style={{
                fontSize: '20px',
                fontWeight: 'bold',
                color: '#1f2937',
                margin: 0
              }}>
                {state.selectedFile.name}
              </h2>
              
              <button
                onClick={() => setState(prev => ({ ...prev, showPreviewModal: false, selectedFile: null }))}
                style={{
                  padding: '8px',
                  backgroundColor: '#f3f4f6',
                  border: 'none',
                  borderRadius: '6px',
                  cursor: 'pointer',
                  fontSize: '18px'
                }}
              >
                ✕
              </button>
            </div>
            
            <div style={{
              display: 'flex',
              justifyContent: 'center',
              alignItems: 'center',
              minHeight: '300px',
              backgroundColor: '#f9fafb',
              borderRadius: '8px',
              marginBottom: '24px'
            }}>
              {state.selectedFile.category === 'image' ? (
                <img
                  src={state.selectedFile.url}
                  alt={state.selectedFile.name}
                  style={{
                    maxWidth: '100%',
                    maxHeight: '400px',
                    borderRadius: '8px'
                  }}
                />
              ) : (
                <div style={{
                  textAlign: 'center',
                  color: '#6b7280'
                }}>
                  <div style={{ fontSize: '64px', marginBottom: '16px' }}>
                    {getFileIcon(state.selectedFile.category)}
                  </div>
                  <p>Preview not available for this file type</p>
                  <button
                    onClick={() => handleDownload(state.selectedFile!)}
                    style={{
                      marginTop: '16px',
                      padding: '12px 20px',
                      backgroundColor: '#667eea',
                      color: 'white',
                      border: 'none',
                      borderRadius: '8px',
                      cursor: 'pointer',
                      fontSize: '14px'
                    }}
                  >
                    📥 Download File
                  </button>
                </div>
              )}
            </div>
            
            <div style={{
              display: 'grid',
              gridTemplateColumns: 'repeat(auto-fit, minmax(150px, 1fr))',
              gap: '16px',
              fontSize: '14px',
              color: '#6b7280'
            }}>
              <div>
                <strong>Size:</strong> {formatFileSize(state.selectedFile.size)}
              </div>
              <div>
                <strong>Type:</strong> {state.selectedFile.type}
              </div>
              <div>
                <strong>Uploaded by:</strong> {state.selectedFile.uploaded_by}
              </div>
              <div>
                <strong>Date:</strong> {new Date(state.selectedFile.uploaded_at).toLocaleDateString()}
              </div>
            </div>
          </div>
        </div>
      )}

      {/* CSS Animation */}
      <style>{`
        @keyframes spin {
          0% { transform: rotate(0deg); }
          100% { transform: rotate(360deg); }
        }
      `}</style>
    </div>
  )
}
