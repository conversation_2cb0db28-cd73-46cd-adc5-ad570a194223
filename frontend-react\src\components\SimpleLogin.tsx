/**
 * Simple Login Component with comprehensive error handling
 */
import React, { useState, useEffect } from 'react'
import { logger, <PERSON>rror<PERSON><PERSON><PERSON>, PerformanceMonitor } from '../utils/logger'
import type { AuthResponse, LoginCredentials } from '../types'

interface LoginState {
  isLoading: boolean
  message: string
  messageType: 'info' | 'success' | 'error'
  loginAttempts: number
}

export default function SimpleLogin() {
  const [username, setUsername] = useState('')
  const [password, setPassword] = useState('')
  const [state, setState] = useState<LoginState>({
    isLoading: false,
    message: '',
    messageType: 'info',
    loginAttempts: 0
  })

  useEffect(() => {
    logger.componentMount('SimpleLogin')
    logger.info('Login component initialized', 'SimpleLogin')

    return () => {
      logger.componentUnmount('SimpleLogin')
    }
  }, [])

  const updateMessage = (message: string, type: 'info' | 'success' | 'error' = 'info') => {
    setState(prev => ({ ...prev, message, messageType: type }))
  }

  const testConnection = async () => {
    updateMessage('Testing backend connection...', 'info')
    logger.info('Testing backend connection', 'SimpleLogin')

    try {
      // Test 1: Health endpoint (simple GET)
      logger.debug('Testing health endpoint', 'SimpleLogin')
      const healthResponse = await fetch('http://localhost:8002/health', {
        method: 'GET',
        mode: 'cors',
        headers: {
          'Accept': 'application/json',
        },
      })

      if (healthResponse.ok) {
        const healthData = await healthResponse.json()
        logger.info('Health endpoint test successful', 'SimpleLogin', healthData)

        // Test 2: CORS test endpoint
        logger.debug('Testing CORS endpoint', 'SimpleLogin')
        const corsResponse = await fetch('http://localhost:8002/cors-test', {
          method: 'GET',
          mode: 'cors',
          headers: {
            'Accept': 'application/json',
          },
        })

        if (corsResponse.ok) {
          const corsData = await corsResponse.json()
          logger.info('CORS endpoint test successful', 'SimpleLogin', corsData)

          // Test 3: OPTIONS request to login endpoint
          logger.debug('Testing OPTIONS request to login endpoint', 'SimpleLogin')
          const optionsResponse = await fetch('http://localhost:8002/api/auth/login', {
            method: 'OPTIONS',
            mode: 'cors',
            headers: {
              'Origin': 'http://localhost:5173',
              'Access-Control-Request-Method': 'POST',
              'Access-Control-Request-Headers': 'Content-Type',
            },
          })

          if (optionsResponse.ok) {
            logger.info('OPTIONS request test successful', 'SimpleLogin')
            updateMessage(`✅ All connection tests passed! Backend is accessible.`, 'success')
          } else {
            updateMessage(`❌ OPTIONS request failed with status: ${optionsResponse.status}`, 'error')
            logger.warn('OPTIONS request test failed', 'SimpleLogin', { status: optionsResponse.status })
          }
        } else {
          updateMessage(`❌ CORS test failed with status: ${corsResponse.status}`, 'error')
          logger.warn('CORS test failed', 'SimpleLogin', { status: corsResponse.status })
        }
      } else {
        updateMessage(`❌ Health check failed with status: ${healthResponse.status}`, 'error')
        logger.warn('Health check test failed', 'SimpleLogin', { status: healthResponse.status })
      }
    } catch (error) {
      updateMessage(`❌ Connection test failed: ${error}`, 'error')
      logger.error('Connection test error', 'SimpleLogin', error)
    }
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    // Validation
    if (!username.trim() || !password.trim()) {
      updateMessage('Please enter both username and password', 'error')
      logger.warn('Login validation failed: empty credentials', 'SimpleLogin')
      return
    }

    setState(prev => ({
      ...prev,
      isLoading: true,
      loginAttempts: prev.loginAttempts + 1
    }))
    updateMessage('Signing in...', 'info')

    const credentials: LoginCredentials = { username: username.trim(), password }

    logger.authLogin(username)
    logger.apiRequest('POST', '/api/auth/login', { username })

    try {
      // Start performance monitoring
      PerformanceMonitor.startTimer('login_request')

      logger.debug('Making login request', 'SimpleLogin', {
        url: 'http://localhost:8002/api/auth/login',
        method: 'POST',
        credentials
      })

      const response = await fetch('http://localhost:8002/api/auth/login', {
        method: 'POST',
        mode: 'cors', // Explicitly set CORS mode
        credentials: 'include', // Include credentials for CORS
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json',
        },
        body: JSON.stringify(credentials),
      })

      const duration = PerformanceMonitor.endTimer('login_request')
      logger.apiResponse('POST', '/api/auth/login', response.status)

      if (response.ok) {
        const data: AuthResponse = await response.json()

        logger.authLoginSuccess(username)
        logger.info(`Login successful in ${duration.toFixed(2)}ms`, 'SimpleLogin', {
          user: data.user.username,
          role: data.user.role,
          department: data.user.department
        })

        updateMessage(`✅ Login successful! Welcome ${data.user.username}`, 'success')

        // Store auth data
        localStorage.setItem('authToken', data.access_token)
        localStorage.setItem('user', JSON.stringify(data.user))

      } else {
        const errorData = await response.json().catch(() => ({ message: 'Unknown error' }))
        const errorMessage = errorData.message || `HTTP ${response.status}: ${response.statusText}`

        logger.authLoginFailed(username, errorMessage)
        updateMessage(`❌ Login failed: ${errorMessage}`, 'error')
      }

    } catch (error) {
      const handledError = ErrorHandler.handleApiError(error, 'SimpleLogin')
      logger.authLoginFailed(username, handledError.message)
      updateMessage(`❌ ${handledError.message}`, 'error')
    } finally {
      setState(prev => ({ ...prev, isLoading: false }))
    }
  }

  return (
    <div style={{ 
      minHeight: '100vh', 
      display: 'flex', 
      alignItems: 'center', 
      justifyContent: 'center',
      backgroundColor: '#f5f5f5',
      padding: '20px'
    }}>
      <div style={{
        maxWidth: '400px',
        width: '100%',
        backgroundColor: 'white',
        padding: '40px',
        borderRadius: '8px',
        boxShadow: '0 2px 10px rgba(0,0,0,0.1)'
      }}>
        <h2 style={{ textAlign: 'center', marginBottom: '30px', color: '#333' }}>
          Sign In
        </h2>
        
        <form onSubmit={handleSubmit}>
          <div style={{ marginBottom: '20px' }}>
            <label style={{ display: 'block', marginBottom: '5px', color: '#555' }}>
              Username
            </label>
            <input
              type="text"
              value={username}
              onChange={(e) => setUsername(e.target.value)}
              required
              style={{
                width: '100%',
                padding: '12px',
                border: '1px solid #ddd',
                borderRadius: '4px',
                fontSize: '16px',
                boxSizing: 'border-box'
              }}
              placeholder="Enter your username"
            />
          </div>
          
          <div style={{ marginBottom: '20px' }}>
            <label style={{ display: 'block', marginBottom: '5px', color: '#555' }}>
              Password
            </label>
            <input
              type="password"
              value={password}
              onChange={(e) => setPassword(e.target.value)}
              required
              style={{
                width: '100%',
                padding: '12px',
                border: '1px solid #ddd',
                borderRadius: '4px',
                fontSize: '16px',
                boxSizing: 'border-box'
              }}
              placeholder="Enter your password"
            />
          </div>

          <div style={{ display: 'flex', gap: '10px', marginBottom: '10px' }}>
            <button
              type="button"
              onClick={testConnection}
              disabled={state.isLoading}
              style={{
                flex: 1,
                padding: '10px',
                backgroundColor: state.isLoading ? '#6c757d' : '#28a745',
                color: 'white',
                border: 'none',
                borderRadius: '4px',
                fontSize: '14px',
                cursor: state.isLoading ? 'not-allowed' : 'pointer',
                opacity: state.isLoading ? 0.7 : 1
              }}
            >
              🔗 Test Connection
            </button>
          </div>

          <button
            type="submit"
            disabled={state.isLoading}
            style={{
              width: '100%',
              padding: '12px',
              backgroundColor: state.isLoading ? '#6c757d' : '#ff0000',
              color: 'white',
              border: 'none',
              borderRadius: '4px',
              fontSize: '16px',
              cursor: state.isLoading ? 'not-allowed' : 'pointer',
              marginBottom: '20px',
              opacity: state.isLoading ? 0.7 : 1
            }}
          >
            {state.isLoading ? 'Signing in...' : 'Sign In'}
          </button>
        </form>
        
        {state.message && (
          <div style={{
            padding: '10px',
            borderRadius: '4px',
            backgroundColor: state.message.includes('✅') ? '#d4edda' : '#f8d7da',
            color: state.message.includes('✅') ? '#155724' : '#721c24',
            border: `1px solid ${state.message.includes('✅') ? '#c3e6cb' : '#f5c6cb'}`,
            fontSize: '14px'
          }}>
            {state.message}
          </div>
        )}
        
        <div style={{ marginTop: '20px', textAlign: 'center', fontSize: '14px', color: '#666' }}>
          <p>Test credentials:</p>
          <p>Username: <EMAIL></p>
          <p>Password: (your password)</p>
        </div>
      </div>
    </div>
  )
}
