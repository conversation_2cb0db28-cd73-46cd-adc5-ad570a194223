"""
Security utilities for authentication and authorization
"""
from datetime import datetime, timedelta
from typing import Optional, Dict, Any
from jose import J<PERSON><PERSON><PERSON><PERSON>, jwt
from passlib.context import Crypt<PERSON>ontext
from fastapi import HTTPException, status, Depends
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
import secrets
import string
from .config import settings, UserRoles

# Password hashing
pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")

# JWT token security
security = HTTPBearer()


def verify_password(plain_password: str, hashed_password: str) -> bool:
    """Verify a password against its hash"""
    return pwd_context.verify(plain_password, hashed_password)


def get_password_hash(password: str) -> str:
    """Generate password hash"""
    return pwd_context.hash(password)


def generate_random_password(length: int = 12) -> str:
    """Generate a random password"""
    alphabet = string.ascii_letters + string.digits + "!@#$%^&*"
    return ''.join(secrets.choice(alphabet) for _ in range(length))


def create_access_token(data: Dict[str, Any], expires_delta: Optional[timedelta] = None) -> str:
    """Create JWT access token"""
    to_encode = data.copy()
    
    if expires_delta:
        expire = datetime.utcnow() + expires_delta
    else:
        expire = datetime.utcnow() + timedelta(minutes=settings.JWT_EXPIRE_MINUTES)
    
    to_encode.update({"exp": expire, "type": "access"})
    
    encoded_jwt = jwt.encode(
        to_encode, 
        settings.JWT_SECRET, 
        algorithm=settings.JWT_ALGORITHM
    )
    
    return encoded_jwt


def create_refresh_token(data: Dict[str, Any]) -> str:
    """Create JWT refresh token"""
    to_encode = data.copy()
    expire = datetime.utcnow() + timedelta(days=settings.REFRESH_TOKEN_EXPIRE_DAYS)
    
    to_encode.update({"exp": expire, "type": "refresh"})
    
    encoded_jwt = jwt.encode(
        to_encode,
        settings.JWT_SECRET,
        algorithm=settings.JWT_ALGORITHM
    )
    
    return encoded_jwt


def verify_token(token: str) -> Dict[str, Any]:
    """Verify and decode JWT token"""
    try:
        payload = jwt.decode(
            token,
            settings.JWT_SECRET,
            algorithms=[settings.JWT_ALGORITHM]
        )
        return payload
    except JWTError:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Could not validate credentials",
            headers={"WWW-Authenticate": "Bearer"},
        )


async def get_current_user_token(credentials: HTTPAuthorizationCredentials = Depends(security)) -> Dict[str, Any]:
    """Get current user from JWT token"""
    token = credentials.credentials
    payload = verify_token(token)
    
    if payload.get("type") != "access":
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid token type"
        )
    
    user_id = payload.get("sub")
    if user_id is None:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Could not validate credentials"
        )
    
    return payload


class RoleChecker:
    """Role-based access control"""
    
    def __init__(self, allowed_roles: list):
        self.allowed_roles = allowed_roles
    
    def __call__(self, current_user: Dict[str, Any] = Depends(get_current_user_token)):
        user_role = current_user.get("role")
        
        if user_role not in self.allowed_roles:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Operation not permitted"
            )
        
        return current_user


# Role-based dependency functions
def require_admin(current_user: Dict[str, Any] = Depends(get_current_user_token)):
    """Require admin role"""
    if current_user.get("role") != UserRoles.ADMIN:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Admin access required"
        )
    return current_user


def require_manager_or_admin(current_user: Dict[str, Any] = Depends(get_current_user_token)):
    """Require manager or admin role"""
    allowed_roles = [UserRoles.MANAGER, UserRoles.ADMIN]
    if current_user.get("role") not in allowed_roles:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Manager or Admin access required"
        )
    return current_user


def require_accountant_or_admin(current_user: Dict[str, Any] = Depends(get_current_user_token)):
    """Require accountant or admin role"""
    allowed_roles = [UserRoles.ACCOUNTANT, UserRoles.ADMIN]
    if current_user.get("role") not in allowed_roles:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Accountant or Admin access required"
        )
    return current_user


def require_hr_or_admin(current_user: Dict[str, Any] = Depends(get_current_user_token)):
    """Require HR or admin role"""
    allowed_roles = [UserRoles.HR, UserRoles.ADMIN]
    if current_user.get("role") not in allowed_roles:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="HR or Admin access required"
        )
    return current_user


def check_department_access(current_user: Dict[str, Any], target_department: str) -> bool:
    """Check if user has access to specific department data"""
    user_role = current_user.get("role")
    user_department = current_user.get("department")
    
    # Admin has access to all departments
    if user_role == UserRoles.ADMIN:
        return True
    
    # Managers can access their own department
    if user_role == UserRoles.MANAGER and user_department == target_department:
        return True
    
    # HR can access all departments for HR-related tasks
    if user_role == UserRoles.HR:
        return True
    
    # Accountants can access all departments for financial tasks
    if user_role == UserRoles.ACCOUNTANT:
        return True
    
    # Staff can only access their own department
    if user_department == target_department:
        return True
    
    return False


def check_resource_ownership(current_user: Dict[str, Any], resource_user_id: str) -> bool:
    """Check if user owns the resource or has permission to access it"""
    user_id = current_user.get("sub")
    user_role = current_user.get("role")
    
    # Admin has access to all resources
    if user_role == UserRoles.ADMIN:
        return True
    
    # User owns the resource
    if user_id == resource_user_id:
        return True
    
    # Managers can access their team members' resources
    if user_role == UserRoles.MANAGER:
        # This would need to be checked against team membership
        return True
    
    return False


def generate_api_key() -> str:
    """Generate a secure API key"""
    return secrets.token_urlsafe(32)


def hash_api_key(api_key: str) -> str:
    """Hash an API key for storage"""
    return get_password_hash(api_key)


def verify_api_key(api_key: str, hashed_key: str) -> bool:
    """Verify an API key against its hash"""
    return verify_password(api_key, hashed_key)


class PermissionChecker:
    """Advanced permission checking system"""
    
    @staticmethod
    def can_approve_procurement(current_user: Dict[str, Any], request_amount: float) -> bool:
        """Check if user can approve procurement requests"""
        role = current_user.get("role")
        
        if role == UserRoles.ADMIN:
            return True
        
        if role == UserRoles.MANAGER and request_amount <= 10000:
            return True
        
        if role == UserRoles.ACCOUNTANT and request_amount <= 50000:
            return True
        
        return False
    
    @staticmethod
    def can_approve_leave(current_user: Dict[str, Any], leave_user_department: str) -> bool:
        """Check if user can approve leave requests"""
        role = current_user.get("role")
        department = current_user.get("department")
        
        if role == UserRoles.ADMIN:
            return True
        
        if role == UserRoles.HR:
            return True
        
        if role == UserRoles.MANAGER and department == leave_user_department:
            return True
        
        return False
    
    @staticmethod
    def can_manage_project(current_user: Dict[str, Any], project_department: str) -> bool:
        """Check if user can manage projects"""
        role = current_user.get("role")
        department = current_user.get("department")
        
        if role == UserRoles.ADMIN:
            return True
        
        if role == UserRoles.MANAGER and department == project_department:
            return True
        
        return False
