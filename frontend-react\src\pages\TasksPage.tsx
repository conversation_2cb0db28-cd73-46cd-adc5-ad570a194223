/**
 * Tasks Management Page Component
 * Features: Create, edit, assign, filter, and track tasks
 */
import React, { useState, useEffect, useCallback } from 'react'
import { logger, <PERSON>rror<PERSON><PERSON><PERSON>, PerformanceMonitor } from '../utils/logger'
import type { Task, User, Project } from '../types'

interface TasksPageProps {
  user: User
  onNavigateBack?: () => void
}

interface TasksState {
  isLoading: boolean
  tasks: Task[]
  filteredTasks: Task[]
  projects: Project[]
  users: User[]
  showCreateModal: boolean
  showEditModal: boolean
  selectedTask: Task | null
  filters: {
    status: string
    priority: string
    assignee: string
    project: string
    search: string
  }
  sortBy: string
  sortOrder: 'asc' | 'desc'
  error: string | null
}

interface NewTask {
  title: string
  description: string
  priority: 'low' | 'medium' | 'high'
  status: 'pending' | 'in-progress' | 'completed'
  assignee_id: string
  project_id: string
  due_date: string
}

export default function TasksPage({ user, onNavigateBack }: TasksPageProps) {
  const [state, setState] = useState<TasksState>({
    isLoading: true,
    tasks: [],
    filteredTasks: [],
    projects: [],
    users: [],
    showCreateModal: false,
    showEditModal: false,
    selectedTask: null,
    filters: {
      status: 'all',
      priority: 'all',
      assignee: 'all',
      project: 'all',
      search: ''
    },
    sortBy: 'created_at',
    sortOrder: 'desc',
    error: null
  })

  const [newTask, setNewTask] = useState<NewTask>({
    title: '',
    description: '',
    priority: 'medium',
    status: 'pending',
    assignee_id: '',
    project_id: '',
    due_date: ''
  })

  useEffect(() => {
    logger.componentMount('TasksPage')
    loadTasksData()
    
    return () => {
      logger.componentUnmount('TasksPage')
    }
  }, [])

  useEffect(() => {
    // Apply filters and sorting
    applyFiltersAndSort()
  }, [state.tasks, state.filters, state.sortBy, state.sortOrder])

  const loadTasksData = useCallback(async () => {
    try {
      setState(prev => ({ ...prev, isLoading: true, error: null }))
      logger.info('Loading tasks data', 'TasksPage')
      
      PerformanceMonitor.startTimer('tasks_load')

      // Load tasks, projects, and users in parallel
      const [tasksResponse, projectsResponse, usersResponse] = await Promise.all([
        fetch('http://localhost:8002/api/tasks', {
          headers: {
            'Authorization': `Bearer ${localStorage.getItem('authToken')}`,
            'Content-Type': 'application/json'
          }
        }),
        fetch('http://localhost:8002/api/projects', {
          headers: {
            'Authorization': `Bearer ${localStorage.getItem('authToken')}`,
            'Content-Type': 'application/json'
          }
        }),
        fetch('http://localhost:8002/api/users', {
          headers: {
            'Authorization': `Bearer ${localStorage.getItem('authToken')}`,
            'Content-Type': 'application/json'
          }
        })
      ])

      const duration = PerformanceMonitor.endTimer('tasks_load')

      const tasks = tasksResponse.ok ? await tasksResponse.json() : { tasks: [] }
      const projects = projectsResponse.ok ? await projectsResponse.json() : { projects: [] }
      const users = usersResponse.ok ? await usersResponse.json() : { users: [] }

      setState(prev => ({
        ...prev,
        isLoading: false,
        tasks: tasks.tasks || [],
        projects: projects.projects || [],
        users: users.users || []
      }))

      logger.info(`Tasks loaded in ${duration.toFixed(2)}ms`, 'TasksPage', {
        taskCount: tasks.tasks?.length || 0
      })
      
    } catch (error) {
      const handledError = ErrorHandler.handleApiError(error, 'TasksPage')
      setState(prev => ({ 
        ...prev, 
        isLoading: false, 
        error: handledError.message 
      }))
      logger.error('Failed to load tasks data', 'TasksPage', error)
    }
  }, [])

  const applyFiltersAndSort = useCallback(() => {
    let filtered = [...state.tasks]

    // Apply filters
    if (state.filters.status !== 'all') {
      filtered = filtered.filter(task => task.status === state.filters.status)
    }
    if (state.filters.priority !== 'all') {
      filtered = filtered.filter(task => task.priority === state.filters.priority)
    }
    if (state.filters.assignee !== 'all') {
      filtered = filtered.filter(task => task.assignee_id === state.filters.assignee)
    }
    if (state.filters.project !== 'all') {
      filtered = filtered.filter(task => task.project_id === state.filters.project)
    }
    if (state.filters.search) {
      const searchLower = state.filters.search.toLowerCase()
      filtered = filtered.filter(task => 
        task.title.toLowerCase().includes(searchLower) ||
        task.description?.toLowerCase().includes(searchLower)
      )
    }

    // Apply sorting
    filtered.sort((a, b) => {
      let aValue = a[state.sortBy as keyof Task] || ''
      let bValue = b[state.sortBy as keyof Task] || ''
      
      if (typeof aValue === 'string') aValue = aValue.toLowerCase()
      if (typeof bValue === 'string') bValue = bValue.toLowerCase()
      
      if (state.sortOrder === 'asc') {
        return aValue < bValue ? -1 : aValue > bValue ? 1 : 0
      } else {
        return aValue > bValue ? -1 : aValue < bValue ? 1 : 0
      }
    })

    setState(prev => ({ ...prev, filteredTasks: filtered }))
  }, [state.tasks, state.filters, state.sortBy, state.sortOrder])

  const handleCreateTask = async () => {
    if (!newTask.title.trim()) {
      alert('Please enter a task title')
      return
    }

    try {
      setState(prev => ({ ...prev, isLoading: true }))
      
      const response = await fetch('http://localhost:8002/api/tasks', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('authToken')}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(newTask)
      })

      if (response.ok) {
        const createdTask = await response.json()
        setState(prev => ({
          ...prev,
          tasks: [createdTask, ...prev.tasks],
          showCreateModal: false,
          isLoading: false
        }))
        
        // Reset form
        setNewTask({
          title: '',
          description: '',
          priority: 'medium',
          status: 'pending',
          assignee_id: '',
          project_id: '',
          due_date: ''
        })
        
        logger.info('Task created successfully', 'TasksPage', { taskId: createdTask.id })
      } else {
        throw new Error('Failed to create task')
      }
    } catch (error) {
      logger.error('Failed to create task', 'TasksPage', error)
      setState(prev => ({ ...prev, isLoading: false }))
      alert('Failed to create task. Please try again.')
    }
  }

  const handleUpdateTaskStatus = async (taskId: string, newStatus: string) => {
    try {
      const response = await fetch(`http://localhost:8002/api/tasks/${taskId}`, {
        method: 'PATCH',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('authToken')}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ status: newStatus })
      })

      if (response.ok) {
        setState(prev => ({
          ...prev,
          tasks: prev.tasks.map(task => 
            task.id === taskId ? { ...task, status: newStatus } : task
          )
        }))
        logger.info('Task status updated', 'TasksPage', { taskId, newStatus })
      } else {
        throw new Error('Failed to update task status')
      }
    } catch (error) {
      logger.error('Failed to update task status', 'TasksPage', error)
      alert('Failed to update task status. Please try again.')
    }
  }

  const getPriorityColor = (priority: string): string => {
    switch (priority) {
      case 'high': return '#ef4444'
      case 'medium': return '#f59e0b'
      case 'low': return '#10b981'
      default: return '#6b7280'
    }
  }

  const getStatusColor = (status: string): string => {
    switch (status) {
      case 'completed': return '#10b981'
      case 'in-progress': return '#ff0000'
      case 'pending': return '#f59e0b'
      default: return '#6b7280'
    }
  }

  const getUserName = (userId: string): string => {
    const user = state.users.find(u => u.id === userId)
    return user ? user.username : 'Unassigned'
  }

  const getProjectName = (projectId: string): string => {
    const project = state.projects.find(p => p.id === projectId)
    return project ? project.name : 'No Project'
  }

  if (state.error) {
    return (
      <div style={{
        minHeight: '100vh',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        backgroundColor: '#f9fafb',
        padding: '20px'
      }}>
        <div style={{
          maxWidth: '500px',
          width: '100%',
          backgroundColor: '#1a1a1a',
          padding: '40px',
          borderRadius: '16px',
          boxShadow: '0 10px 15px -3px rgba(0, 0, 0, 0.1)',
          textAlign: 'center'
        }}>
          <div style={{ fontSize: '48px', marginBottom: '20px' }}>⚠️</div>
          <h2 style={{ color: '#ef4444', marginBottom: '16px' }}>Tasks Error</h2>
          <p style={{ color: '#6b7280', marginBottom: '24px' }}>{state.error}</p>
          <button
            onClick={loadTasksData}
            style={{
              padding: '12px 24px',
              backgroundColor: '#ff0000',
              color: '#DC143C',
              border: 'none',
              borderRadius: '8px',
              cursor: 'pointer',
              marginRight: '12px'
            }}
          >
            Try Again
          </button>
          <button
            onClick={onNavigateBack}
            style={{
              padding: '12px 24px',
              backgroundColor: '#6b7280',
              color: '#8B0000',
              border: 'none',
              borderRadius: '8px',
              cursor: 'pointer'
            }}
          >
            Go Back
          </button>
        </div>
      </div>
    )
  }

  return (
    <div style={{
      minHeight: '100vh',
      background: 'linear-gradient(135deg, #0f0f0f 0%, #1a0a0a 50%, #0f0f0f 100%)'
    }}>
      {/* Header */}
      <header style={{
        backgroundColor: '#1a1a1a',
        boxShadow: '0 4px 6px rgba(0, 0, 0, 0.3), 0 0 20px rgba(239, 68, 68, 0.1)',
        padding: '12px 0',
        border: '1px solid rgba(239, 68, 68, 0.1)'
      }}>
        <div style={{
          maxWidth: '1200px',
          margin: '0 auto',
          padding: '0 16px',
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center'
        }}>
          <div style={{ display: 'flex', alignItems: 'center', gap: '16px' }}>
            <button
              onClick={onNavigateBack}
              style={{
                padding: '8px 12px',
                backgroundColor: '#f3f4f6',
                border: 'none',
                borderRadius: '6px',
                cursor: 'pointer',
                fontSize: '14px'
              }}
            >
              ← Back
            </button>
            <div>
              <div style={{ display: 'flex', alignItems: 'center', gap: '12px' }}>
                <img
                  src="/logo.svg"
                  alt="CTNL WORK-BOARD"
                  style={{ height: '32px', width: 'auto' }}
                />
                <h1 style={{
                  margin: 0,
                  background: 'linear-gradient(135deg, #8B0000, #DC143C)',
                  WebkitBackgroundClip: 'text',
                  WebkitTextFillColor: 'transparent',
                  fontSize: '28px',
                  fontWeight: 'bold',
                  textShadow: '0 0 20px rgba(139, 0, 0, 0.5)'
                }}>
                  🤖 CTNL AI Task Management
                </h1>
              </div>
              <p style={{
                margin: '6px 0 0 0',
                color: '#9ca3af',
                fontSize: '16px',
                opacity: 0.8
              }}>
                Manage and track your tasks
              </p>
            </div>
          </div>
          
          <button
            onClick={() => setState(prev => ({ ...prev, showCreateModal: true }))}
            style={{
              padding: '12px 20px',
              backgroundColor: '#ff0000',
              color: '#DC143C',
              border: 'none',
              borderRadius: '8px',
              cursor: 'pointer',
              fontSize: '14px',
              fontWeight: '500',
              display: 'flex',
              alignItems: 'center',
              gap: '8px'
            }}
          >
            ➕ New Task
          </button>
        </div>
      </header>

      {/* Main Content */}
      <main style={{ maxWidth: '1200px', margin: '0 auto', padding: '16px' }}>
        {/* Filters and Search */}
        <div style={{
          backgroundColor: '#2a2a2a',
          borderRadius: '8px',
          padding: '16px',
          marginBottom: '16px',
          boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)'
        }}>
          <h2 style={{ 
            fontSize: '18px', 
            fontWeight: 'bold', 
            color: '#1f2937', 
            marginBottom: '20px' 
          }}>
            🔍 Filters & Search
          </h2>
          
          <div style={{
            display: 'grid',
            gridTemplateColumns: 'repeat(auto-fit, minmax(150px, 1fr))',
            gap: '12px',
            marginBottom: '16px'
          }}>
            <div>
              <label style={{ 
                display: 'block', 
                fontSize: '14px', 
                fontWeight: '500', 
                color: '#374151', 
                marginBottom: '6px' 
              }}>
                Status
              </label>
              <select
                value={state.filters.status}
                onChange={(e) => setState(prev => ({
                  ...prev,
                  filters: { ...prev.filters, status: e.target.value }
                }))}
                style={{
                  width: '100%',
                  padding: '8px 12px',
                  border: '1px solid #d1d5db',
                  borderRadius: '6px',
                  fontSize: '14px',
                  backgroundColor: '#2a2a2a',
                  color: '#8B0000'
                }}
              >
                <option value="all">All Status</option>
                <option value="pending">Pending</option>
                <option value="in-progress">In Progress</option>
                <option value="completed">Completed</option>
              </select>
            </div>

            <div>
              <label style={{ 
                display: 'block', 
                fontSize: '14px', 
                fontWeight: '500', 
                color: '#374151', 
                marginBottom: '6px' 
              }}>
                Priority
              </label>
              <select
                value={state.filters.priority}
                onChange={(e) => setState(prev => ({
                  ...prev,
                  filters: { ...prev.filters, priority: e.target.value }
                }))}
                style={{
                  width: '100%',
                  padding: '8px 12px',
                  border: '1px solid #d1d5db',
                  borderRadius: '6px',
                  fontSize: '14px',
                  backgroundColor: '#2a2a2a',
                  color: '#8B0000'
                }}
              >
                <option value="all">All Priority</option>
                <option value="high">High</option>
                <option value="medium">Medium</option>
                <option value="low">Low</option>
              </select>
            </div>

            <div>
              <label style={{ 
                display: 'block', 
                fontSize: '14px', 
                fontWeight: '500', 
                color: '#374151', 
                marginBottom: '6px' 
              }}>
                Assignee
              </label>
              <select
                value={state.filters.assignee}
                onChange={(e) => setState(prev => ({
                  ...prev,
                  filters: { ...prev.filters, assignee: e.target.value }
                }))}
                style={{
                  width: '100%',
                  padding: '8px 12px',
                  border: '1px solid #d1d5db',
                  borderRadius: '6px',
                  fontSize: '14px',
                  backgroundColor: '#2a2a2a',
                  color: '#8B0000'
                }}
              >
                <option value="all">All Assignees</option>
                {state.users.map(user => (
                  <option key={user.id} value={user.id}>{user.username}</option>
                ))}
              </select>
            </div>

            <div>
              <label style={{ 
                display: 'block', 
                fontSize: '14px', 
                fontWeight: '500', 
                color: '#374151', 
                marginBottom: '6px' 
              }}>
                Search
              </label>
              <input
                type="text"
                placeholder="Search tasks..."
                value={state.filters.search}
                onChange={(e) => setState(prev => ({
                  ...prev,
                  filters: { ...prev.filters, search: e.target.value }
                }))}
                style={{
                  width: '100%',
                  padding: '8px 12px',
                  border: '1px solid #d1d5db',
                  borderRadius: '6px',
                  fontSize: '14px'
                }}
              />
            </div>
          </div>

          <div style={{ display: 'flex', gap: '16px', alignItems: 'center' }}>
            <div>
              <label style={{ 
                display: 'block', 
                fontSize: '14px', 
                fontWeight: '500', 
                color: '#374151', 
                marginBottom: '6px' 
              }}>
                Sort By
              </label>
              <select
                value={state.sortBy}
                onChange={(e) => setState(prev => ({ ...prev, sortBy: e.target.value }))}
                style={{
                  padding: '8px 12px',
                  border: '1px solid #d1d5db',
                  borderRadius: '6px',
                  fontSize: '14px',
                  backgroundColor: '#2a2a2a',
                  color: '#8B0000'
                }}
              >
                <option value="created_at">Created Date</option>
                <option value="title">Title</option>
                <option value="priority">Priority</option>
                <option value="status">Status</option>
                <option value="due_date">Due Date</option>
              </select>
            </div>

            <div>
              <label style={{ 
                display: 'block', 
                fontSize: '14px', 
                fontWeight: '500', 
                color: '#374151', 
                marginBottom: '6px' 
              }}>
                Order
              </label>
              <select
                value={state.sortOrder}
                onChange={(e) => setState(prev => ({ 
                  ...prev, 
                  sortOrder: e.target.value as 'asc' | 'desc' 
                }))}
                style={{
                  padding: '8px 12px',
                  border: '1px solid #d1d5db',
                  borderRadius: '6px',
                  fontSize: '14px',
                  backgroundColor: '#2a2a2a',
                  color: '#8B0000'
                }}
              >
                <option value="desc">Descending</option>
                <option value="asc">Ascending</option>
              </select>
            </div>

            <div style={{ marginTop: '20px' }}>
              <button
                onClick={() => setState(prev => ({
                  ...prev,
                  filters: {
                    status: 'all',
                    priority: 'all',
                    assignee: 'all',
                    project: 'all',
                    search: ''
                  }
                }))}
                style={{
                  padding: '8px 16px',
                  backgroundColor: '#f3f4f6',
                  border: '1px solid #d1d5db',
                  borderRadius: '6px',
                  cursor: 'pointer',
                  fontSize: '14px'
                }}
              >
                Clear Filters
              </button>
            </div>
          </div>
        </div>

        {/* Tasks List */}
        <div style={{
          backgroundColor: '#2a2a2a',
          borderRadius: '12px',
          padding: '24px',
          boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)'
        }}>
          <div style={{
            display: 'flex',
            justifyContent: 'space-between',
            alignItems: 'center',
            marginBottom: '20px'
          }}>
            <h2 style={{ 
              fontSize: '18px', 
              fontWeight: 'bold', 
              color: '#1f2937',
              margin: 0
            }}>
              📋 Tasks ({state.filteredTasks.length})
            </h2>
            
            <button
              onClick={loadTasksData}
              disabled={state.isLoading}
              style={{
                padding: '8px 16px',
                backgroundColor: state.isLoading ? '#9ca3af' : '#f3f4f6',
                border: '1px solid #d1d5db',
                borderRadius: '6px',
                cursor: state.isLoading ? 'not-allowed' : 'pointer',
                fontSize: '14px',
                display: 'flex',
                alignItems: 'center',
                gap: '8px'
              }}
            >
              {state.isLoading ? (
                <div style={{
                  width: '16px',
                  height: '16px',
                  border: '2px solid transparent',
                  borderTop: '2px solid #6b7280',
                  borderRadius: '50%',
                  animation: 'spin 1s linear infinite'
                }} />
              ) : (
                '🔄'
              )}
              Refresh
            </button>
          </div>
          
          {state.isLoading ? (
            <div style={{
              display: 'flex',
              justifyContent: 'center',
              alignItems: 'center',
              height: '200px'
            }}>
              <div style={{
                width: '40px',
                height: '40px',
                border: '4px solid #e5e7eb',
                borderTop: '4px solid #ff0000',
                borderRadius: '50%',
                animation: 'spin 1s linear infinite'
              }} />
            </div>
          ) : state.filteredTasks.length > 0 ? (
            <div style={{ display: 'flex', flexDirection: 'column', gap: '12px' }}>
              {state.filteredTasks.map((task) => (
                <div
                  key={task.id}
                  style={{
                    padding: '20px',
                    border: '1px solid #e5e7eb',
                    borderRadius: '8px',
                    backgroundColor: '#fafafa',
                    transition: 'all 0.2s'
                  }}
                  onMouseEnter={(e) => {
                    e.currentTarget.style.backgroundColor = '#f3f4f6'
                    e.currentTarget.style.borderColor = '#d1d5db'
                  }}
                  onMouseLeave={(e) => {
                    e.currentTarget.style.backgroundColor = '#fafafa'
                    e.currentTarget.style.borderColor = '#e5e7eb'
                  }}
                >
                  <div style={{
                    display: 'flex',
                    justifyContent: 'space-between',
                    alignItems: 'flex-start',
                    marginBottom: '12px'
                  }}>
                    <div style={{ flex: 1 }}>
                      <h3 style={{
                        margin: '0 0 8px 0',
                        fontSize: '16px',
                        fontWeight: '600',
                        color: '#1f2937'
                      }}>
                        {task.title}
                      </h3>
                      {task.description && (
                        <p style={{
                          margin: '0 0 12px 0',
                          fontSize: '14px',
                          color: '#6b7280',
                          lineHeight: '1.5'
                        }}>
                          {task.description}
                        </p>
                      )}
                      
                      <div style={{
                        display: 'flex',
                        gap: '16px',
                        fontSize: '12px',
                        color: '#6b7280'
                      }}>
                        <span>Assignee: {getUserName(task.assignee_id || '')}</span>
                        <span>Project: {getProjectName(task.project_id || '')}</span>
                        {task.due_date && (
                          <span>Due: {new Date(task.due_date).toLocaleDateString()}</span>
                        )}
                      </div>
                    </div>
                    
                    <div style={{
                      display: 'flex',
                      flexDirection: 'column',
                      gap: '8px',
                      alignItems: 'flex-end'
                    }}>
                      <div style={{ display: 'flex', gap: '8px' }}>
                        <span style={{
                          padding: '4px 8px',
                          borderRadius: '12px',
                          fontSize: '12px',
                          fontWeight: '500',
                          backgroundColor: getPriorityColor(task.priority) + '20',
                          color: getPriorityColor(task.priority)
                        }}>
                          {task.priority}
                        </span>
                        
                        <select
                          value={task.status}
                          onChange={(e) => handleUpdateTaskStatus(task.id, e.target.value)}
                          style={{
                            padding: '4px 8px',
                            borderRadius: '6px',
                            fontSize: '12px',
                            fontWeight: '500',
                            border: '1px solid #d1d5db',
                            backgroundColor: getStatusColor(task.status) + '20',
                            color: getStatusColor(task.status)
                          }}
                        >
                          <option value="pending">Pending</option>
                          <option value="in-progress">In Progress</option>
                          <option value="completed">Completed</option>
                        </select>
                      </div>
                      
                      <button
                        onClick={() => setState(prev => ({ 
                          ...prev, 
                          selectedTask: task, 
                          showEditModal: true 
                        }))}
                        style={{
                          padding: '6px 12px',
                          backgroundColor: '#ff0000',
                          color: '#DC143C',
                          border: 'none',
                          borderRadius: '6px',
                          cursor: 'pointer',
                          fontSize: '12px'
                        }}
                      >
                        Edit
                      </button>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div style={{
              textAlign: 'center',
              color: '#6b7280',
              fontStyle: 'italic',
              padding: '60px 0'
            }}>
              <div style={{ fontSize: '48px', marginBottom: '16px' }}>📋</div>
              <p>No tasks found matching your criteria</p>
              <button
                onClick={() => setState(prev => ({ ...prev, showCreateModal: true }))}
                style={{
                  marginTop: '16px',
                  padding: '12px 20px',
                  backgroundColor: '#ff0000',
                  color: '#DC143C',
                  border: 'none',
                  borderRadius: '8px',
                  cursor: 'pointer',
                  fontSize: '14px'
                }}
              >
                Create Your First Task
              </button>
            </div>
          )}
        </div>
      </main>

      {/* Create Task Modal */}
      {state.showCreateModal && (
        <div style={{
          position: 'fixed',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          backgroundColor: 'rgba(0, 0, 0, 0.5)',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          zIndex: 1000
        }}>
          <div style={{
            backgroundColor: '#2a2a2a',
            borderRadius: '12px',
            padding: '32px',
            maxWidth: '500px',
            width: '90%',
            maxHeight: '90vh',
            overflowY: 'auto'
          }}>
            <h2 style={{
              fontSize: '20px',
              fontWeight: 'bold',
              color: '#1f2937',
              marginBottom: '24px'
            }}>
              ➕ Create New Task
            </h2>
            
            <div style={{ display: 'flex', flexDirection: 'column', gap: '16px' }}>
              <div>
                <label style={{
                  display: 'block',
                  fontSize: '14px',
                  fontWeight: '500',
                  color: '#374151',
                  marginBottom: '6px'
                }}>
                  Title *
                </label>
                <input
                  type="text"
                  value={newTask.title}
                  onChange={(e) => setNewTask(prev => ({ ...prev, title: e.target.value }))}
                  placeholder="Enter task title"
                  style={{
                    width: '100%',
                    padding: '12px',
                    border: '1px solid #d1d5db',
                    borderRadius: '6px',
                    fontSize: '14px',
                    boxSizing: 'border-box'
                  }}
                />
              </div>

              <div>
                <label style={{
                  display: 'block',
                  fontSize: '14px',
                  fontWeight: '500',
                  color: '#374151',
                  marginBottom: '6px'
                }}>
                  Description
                </label>
                <textarea
                  value={newTask.description}
                  onChange={(e) => setNewTask(prev => ({ ...prev, description: e.target.value }))}
                  placeholder="Enter task description"
                  rows={4}
                  style={{
                    width: '100%',
                    padding: '12px',
                    border: '1px solid #d1d5db',
                    borderRadius: '6px',
                    fontSize: '14px',
                    resize: 'vertical',
                    boxSizing: 'border-box'
                  }}
                />
              </div>

              <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '16px' }}>
                <div>
                  <label style={{
                    display: 'block',
                    fontSize: '14px',
                    fontWeight: '500',
                    color: '#374151',
                    marginBottom: '6px'
                  }}>
                    Priority
                  </label>
                  <select
                    value={newTask.priority}
                    onChange={(e) => setNewTask(prev => ({ 
                      ...prev, 
                      priority: e.target.value as 'low' | 'medium' | 'high' 
                    }))}
                    style={{
                      width: '100%',
                      padding: '12px',
                      border: '1px solid #d1d5db',
                      borderRadius: '6px',
                      fontSize: '14px',
                      backgroundColor: '#2a2a2a',
                      color: '#8B0000'
                    }}
                  >
                    <option value="low">Low</option>
                    <option value="medium">Medium</option>
                    <option value="high">High</option>
                  </select>
                </div>

                <div>
                  <label style={{
                    display: 'block',
                    fontSize: '14px',
                    fontWeight: '500',
                    color: '#374151',
                    marginBottom: '6px'
                  }}>
                    Status
                  </label>
                  <select
                    value={newTask.status}
                    onChange={(e) => setNewTask(prev => ({ 
                      ...prev, 
                      status: e.target.value as 'pending' | 'in-progress' | 'completed' 
                    }))}
                    style={{
                      width: '100%',
                      padding: '12px',
                      border: '1px solid #d1d5db',
                      borderRadius: '6px',
                      fontSize: '14px',
                      backgroundColor: '#2a2a2a',
                      color: '#8B0000'
                    }}
                  >
                    <option value="pending">Pending</option>
                    <option value="in-progress">In Progress</option>
                    <option value="completed">Completed</option>
                  </select>
                </div>
              </div>

              <div>
                <label style={{
                  display: 'block',
                  fontSize: '14px',
                  fontWeight: '500',
                  color: '#374151',
                  marginBottom: '6px'
                }}>
                  Assignee
                </label>
                <select
                  value={newTask.assignee_id}
                  onChange={(e) => setNewTask(prev => ({ ...prev, assignee_id: e.target.value }))}
                  style={{
                    width: '100%',
                    padding: '12px',
                    border: '1px solid #d1d5db',
                    borderRadius: '6px',
                    fontSize: '14px',
                    backgroundColor: '#2a2a2a',
                    color: '#8B0000'
                  }}
                >
                  <option value="">Select assignee</option>
                  {state.users.map(user => (
                    <option key={user.id} value={user.id}>{user.username}</option>
                  ))}
                </select>
              </div>

              <div>
                <label style={{
                  display: 'block',
                  fontSize: '14px',
                  fontWeight: '500',
                  color: '#374151',
                  marginBottom: '6px'
                }}>
                  Project
                </label>
                <select
                  value={newTask.project_id}
                  onChange={(e) => setNewTask(prev => ({ ...prev, project_id: e.target.value }))}
                  style={{
                    width: '100%',
                    padding: '12px',
                    border: '1px solid #d1d5db',
                    borderRadius: '6px',
                    fontSize: '14px',
                    backgroundColor: '#2a2a2a',
                    color: '#8B0000'
                  }}
                >
                  <option value="">Select project</option>
                  {state.projects.map(project => (
                    <option key={project.id} value={project.id}>{project.name}</option>
                  ))}
                </select>
              </div>

              <div>
                <label style={{
                  display: 'block',
                  fontSize: '14px',
                  fontWeight: '500',
                  color: '#374151',
                  marginBottom: '6px'
                }}>
                  Due Date
                </label>
                <input
                  type="date"
                  value={newTask.due_date}
                  onChange={(e) => setNewTask(prev => ({ ...prev, due_date: e.target.value }))}
                  style={{
                    width: '100%',
                    padding: '12px',
                    border: '1px solid #d1d5db',
                    borderRadius: '6px',
                    fontSize: '14px',
                    boxSizing: 'border-box'
                  }}
                />
              </div>
            </div>

            <div style={{
              display: 'flex',
              justifyContent: 'flex-end',
              gap: '12px',
              marginTop: '24px'
            }}>
              <button
                onClick={() => setState(prev => ({ ...prev, showCreateModal: false }))}
                style={{
                  padding: '12px 20px',
                  backgroundColor: '#f3f4f6',
                  border: '1px solid #d1d5db',
                  borderRadius: '6px',
                  cursor: 'pointer',
                  fontSize: '14px'
                }}
              >
                Cancel
              </button>
              <button
                onClick={handleCreateTask}
                disabled={!newTask.title.trim() || state.isLoading}
                style={{
                  padding: '12px 20px',
                  backgroundColor: !newTask.title.trim() || state.isLoading ? '#9ca3af' : '#ff0000',
                  color: '#DC143C',
                  border: 'none',
                  borderRadius: '6px',
                  cursor: !newTask.title.trim() || state.isLoading ? 'not-allowed' : 'pointer',
                  fontSize: '14px',
                  fontWeight: '500'
                }}
              >
                {state.isLoading ? 'Creating...' : 'Create Task'}
              </button>
            </div>
          </div>
        </div>
      )}

      {/* CSS Animation */}
      <style>{`
        @keyframes spin {
          0% { transform: rotate(0deg); }
          100% { transform: rotate(360deg); }
        }
      `}</style>
    </div>
  )
}
