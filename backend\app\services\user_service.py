"""
User service layer for business logic
"""
from typing import Optional, List, Dict, Any
from datetime import datetime, timedelta
from bson import ObjectId
from fastapi import HTTPException, status
from motor.motor_asyncio import AsyncIOMotorDatabase

from ..core.database import get_database, get_next_sequence, create_audit_log
from ..core.security import get_password_hash, verify_password, create_access_token, create_refresh_token
from ..core.config import settings, Collections, UserRoles
from ..models.user import User, Department, UserSession, APIKey
from ..schemas.user import (
    UserCreate, UserUpdate, UserLogin, PasswordChange, 
    UserPreferencesUpdate, DepartmentCreate, DepartmentUpdate,
    APIKeyCreate
)
import secrets
import uuid


class UserService:
    """User service for managing user operations"""
    
    def __init__(self, db: AsyncIOMotorDatabase):
        self.db = db
        self.users_collection = db[Collections.USERS]
        self.departments_collection = db[Collections.DEPARTMENTS]
        self.sessions_collection = db["user_sessions"]
        self.api_keys_collection = db["api_keys"]
    
    async def create_user(self, user_data: UserCreate, created_by: str = None) -> Dict[str, Any]:
        """Create a new user"""
        # Check if user already exists
        existing_user = await self.users_collection.find_one({
            "$or": [
                {"email": user_data.email},
                {"username": user_data.username},
                {"employee_id": user_data.employee_id}
            ]
        })
        
        if existing_user:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="User with this email, username, or employee ID already exists"
            )
        
        # Validate department exists
        department = await self.departments_collection.find_one({"name": user_data.department})
        if not department:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Department does not exist"
            )
        
        # Validate manager exists if provided
        manager_id = None
        if user_data.manager_id:
            manager = await self.users_collection.find_one({"_id": ObjectId(user_data.manager_id)})
            if not manager:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="Manager does not exist"
                )
            manager_id = ObjectId(user_data.manager_id)
        
        # Create user document
        user_doc = {
            "employee_id": user_data.employee_id,
            "email": user_data.email,
            "username": user_data.username,
            "hashed_password": get_password_hash(user_data.password),
            "profile": user_data.profile.dict(),
            "role": user_data.role,
            "department": user_data.department,
            "position": user_data.position,
            "manager_id": manager_id,
            "direct_reports": [],
            "is_active": True,
            "is_verified": False,
            "is_online": False,
            "preferences": {
                "theme": "light",
                "language": "en",
                "timezone": "UTC",
                "notifications": {
                    "email": True,
                    "push": True,
                    "sms": False,
                    "task_assignments": True,
                    "project_updates": True,
                    "approval_requests": True,
                    "system_alerts": True
                },
                "dashboard_layout": {}
            },
            "security": {
                "two_factor_enabled": False,
                "two_factor_secret": None,
                "backup_codes": [],
                "last_password_change": datetime.utcnow(),
                "failed_login_attempts": 0,
                "account_locked_until": None,
                "password_reset_token": None,
                "password_reset_expires": None
            },
            "activity": {
                "last_login": None,
                "last_active": None,
                "login_count": 0,
                "current_session_id": None,
                "ip_addresses": [],
                "devices": []
            },
            "avatar_url": None,
            "salary": user_data.salary,
            "hire_date": user_data.hire_date,
            "contract_type": user_data.contract_type,
            "work_location": user_data.work_location,
            "created_at": datetime.utcnow(),
            "updated_at": datetime.utcnow(),
            "created_by": ObjectId(created_by) if created_by else None
        }
        
        # Insert user
        result = await self.users_collection.insert_one(user_doc)
        
        # Update manager's direct reports if manager exists
        if manager_id:
            await self.users_collection.update_one(
                {"_id": manager_id},
                {"$push": {"direct_reports": result.inserted_id}}
            )
        
        # Create audit log
        if created_by:
            await create_audit_log(
                user_id=created_by,
                action="create_user",
                resource_type="user",
                resource_id=str(result.inserted_id),
                details={"employee_id": user_data.employee_id, "role": user_data.role}
            )
        
        # Get created user
        created_user = await self.users_collection.find_one({"_id": result.inserted_id})
        created_user["id"] = str(created_user["_id"])
        del created_user["_id"]
        del created_user["hashed_password"]
        
        return created_user
    
    async def authenticate_user(self, login_data: UserLogin, ip_address: str = None, user_agent: str = None) -> Dict[str, Any]:
        """Authenticate user and create session"""
        # Find user by username or email
        user = await self.users_collection.find_one({
            "$or": [
                {"username": login_data.username},
                {"email": login_data.username}
            ]
        })
        
        if not user:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Invalid credentials"
            )
        
        # Check if account is locked
        if user.get("security", {}).get("account_locked_until"):
            if user["security"]["account_locked_until"] > datetime.utcnow():
                raise HTTPException(
                    status_code=status.HTTP_423_LOCKED,
                    detail="Account is temporarily locked"
                )
        
        # Verify password
        if not verify_password(login_data.password, user["hashed_password"]):
            # Increment failed login attempts
            await self.users_collection.update_one(
                {"_id": user["_id"]},
                {
                    "$inc": {"security.failed_login_attempts": 1},
                    "$set": {"updated_at": datetime.utcnow()}
                }
            )
            
            # Lock account after 5 failed attempts
            failed_attempts = user.get("security", {}).get("failed_login_attempts", 0) + 1
            if failed_attempts >= 5:
                lock_until = datetime.utcnow() + timedelta(minutes=30)
                await self.users_collection.update_one(
                    {"_id": user["_id"]},
                    {"$set": {"security.account_locked_until": lock_until}}
                )
            
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Invalid credentials"
            )
        
        # Check if user is active
        if not user.get("is_active", True):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Account is deactivated"
            )
        
        # Create tokens
        token_data = {
            "sub": str(user["_id"]),
            "username": user["username"],
            "email": user["email"],
            "role": user["role"],
            "department": user["department"]
        }
        
        access_token = create_access_token(token_data)
        refresh_token = create_refresh_token(token_data)
        
        # Create session
        session_id = str(uuid.uuid4())
        session_doc = {
            "user_id": user["_id"],
            "session_id": session_id,
            "refresh_token": refresh_token,
            "ip_address": ip_address,
            "user_agent": user_agent,
            "device_info": {},
            "location": {},
            "created_at": datetime.utcnow(),
            "last_accessed": datetime.utcnow(),
            "expires_at": datetime.utcnow() + timedelta(days=settings.REFRESH_TOKEN_EXPIRE_DAYS),
            "is_active": True
        }
        
        await self.sessions_collection.insert_one(session_doc)
        
        # Update user activity
        await self.users_collection.update_one(
            {"_id": user["_id"]},
            {
                "$set": {
                    "activity.last_login": datetime.utcnow(),
                    "activity.last_active": datetime.utcnow(),
                    "activity.current_session_id": session_id,
                    "is_online": True,
                    "security.failed_login_attempts": 0,
                    "security.account_locked_until": None,
                    "updated_at": datetime.utcnow()
                },
                "$inc": {"activity.login_count": 1},
                "$addToSet": {"activity.ip_addresses": ip_address} if ip_address else {}
            }
        )
        
        # Prepare user response
        user_response = {
            "id": str(user["_id"]),
            "employee_id": user["employee_id"],
            "email": user["email"],
            "username": user["username"],
            "profile": user["profile"],
            "role": user["role"],
            "department": user["department"],
            "position": user.get("position"),
            "manager_id": str(user["manager_id"]) if user.get("manager_id") else None,
            "is_active": user["is_active"],
            "is_verified": user.get("is_verified", False),
            "is_online": True,
            "avatar_url": user.get("avatar_url"),
            "contract_type": user.get("contract_type", "full_time"),
            "work_location": user.get("work_location", "office"),
            "created_at": user["created_at"],
            "updated_at": user["updated_at"],
            "last_login": datetime.utcnow()
        }
        
        return {
            "access_token": access_token,
            "refresh_token": refresh_token,
            "token_type": "bearer",
            "expires_in": settings.JWT_EXPIRE_MINUTES * 60,
            "user": user_response
        }
    
    async def get_user_by_id(self, user_id: str) -> Optional[Dict[str, Any]]:
        """Get user by ID"""
        user = await self.users_collection.find_one({"_id": ObjectId(user_id)})
        if user:
            user["id"] = str(user["_id"])
            del user["_id"]
            del user["hashed_password"]
            
            # Convert ObjectIds to strings
            if user.get("manager_id"):
                user["manager_id"] = str(user["manager_id"])
            if user.get("direct_reports"):
                user["direct_reports"] = [str(report_id) for report_id in user["direct_reports"]]
            if user.get("created_by"):
                user["created_by"] = str(user["created_by"])
        
        return user
    
    async def update_user(self, user_id: str, update_data: UserUpdate, updated_by: str = None) -> Dict[str, Any]:
        """Update user information"""
        user = await self.users_collection.find_one({"_id": ObjectId(user_id)})
        if not user:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="User not found"
            )
        
        update_doc = {"updated_at": datetime.utcnow()}
        
        if update_data.profile:
            update_doc["profile"] = update_data.profile.dict()
        
        if update_data.department:
            # Validate department exists
            department = await self.departments_collection.find_one({"name": update_data.department})
            if not department:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="Department does not exist"
                )
            update_doc["department"] = update_data.department
        
        if update_data.position is not None:
            update_doc["position"] = update_data.position
        
        if update_data.manager_id:
            manager = await self.users_collection.find_one({"_id": ObjectId(update_data.manager_id)})
            if not manager:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="Manager does not exist"
                )
            update_doc["manager_id"] = ObjectId(update_data.manager_id)
        
        if update_data.salary is not None:
            update_doc["salary"] = update_data.salary
        
        if update_data.contract_type:
            update_doc["contract_type"] = update_data.contract_type
        
        if update_data.work_location:
            update_doc["work_location"] = update_data.work_location
        
        if update_data.is_active is not None:
            update_doc["is_active"] = update_data.is_active
        
        # Update user
        await self.users_collection.update_one(
            {"_id": ObjectId(user_id)},
            {"$set": update_doc}
        )
        
        # Create audit log
        if updated_by:
            await create_audit_log(
                user_id=updated_by,
                action="update_user",
                resource_type="user",
                resource_id=user_id,
                details=update_doc
            )
        
        return await self.get_user_by_id(user_id)
