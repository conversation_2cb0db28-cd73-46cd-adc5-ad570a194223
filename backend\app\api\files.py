"""
File Management API routes
"""
from fastapi import APIRouter, Depends, HTTPException, status, UploadFile, File, Form
from fastapi.responses import StreamingResponse
from motor.motor_asyncio import AsyncIOMotorDatabase
from typing import Dict, Any, List, Optional
from datetime import datetime
from bson import ObjectId
import os
import uuid
import mimetypes
from pathlib import Path

from ..core.database import get_database, create_audit_log
from ..core.security import get_current_user_token
from ..core.config import settings, Collections

router = APIRouter()

# File storage configuration
UPLOAD_DIR = Path("uploads")
UPLOAD_DIR.mkdir(exist_ok=True)

# Allowed file types
ALLOWED_EXTENSIONS = {
    'image': ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp'],
    'document': ['.pdf', '.doc', '.docx', '.txt', '.rtf', '.odt'],
    'spreadsheet': ['.xls', '.xlsx', '.csv', '.ods'],
    'presentation': ['.ppt', '.pptx', '.odp'],
    'archive': ['.zip', '.rar', '.7z', '.tar', '.gz'],
    'video': ['.mp4', '.avi', '.mov', '.wmv', '.flv', '.webm'],
    'audio': ['.mp3', '.wav', '.flac', '.aac', '.ogg']
}

MAX_FILE_SIZE = 50 * 1024 * 1024  # 50MB


def get_file_category(filename: str) -> str:
    """Determine file category based on extension"""
    ext = Path(filename).suffix.lower()
    
    for category, extensions in ALLOWED_EXTENSIONS.items():
        if ext in extensions:
            return category
    
    return 'other'


def is_allowed_file(filename: str) -> bool:
    """Check if file type is allowed"""
    ext = Path(filename).suffix.lower()
    all_extensions = []
    for extensions in ALLOWED_EXTENSIONS.values():
        all_extensions.extend(extensions)
    
    return ext in all_extensions


@router.post("/upload")
async def upload_file(
    file: UploadFile = File(...),
    description: Optional[str] = Form(None),
    tags: Optional[str] = Form(None),
    project_id: Optional[str] = Form(None),
    task_id: Optional[str] = Form(None),
    is_public: bool = Form(False),
    current_user: Dict[str, Any] = Depends(get_current_user_token),
    db: AsyncIOMotorDatabase = Depends(get_database)
):
    """Upload a file"""
    
    user_id = current_user.get("sub")
    user_department = current_user.get("department")
    
    try:
        # Validate file
        if not file.filename:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="No file provided"
            )
        
        if not is_allowed_file(file.filename):
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="File type not allowed"
            )
        
        # Check file size
        content = await file.read()
        if len(content) > MAX_FILE_SIZE:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"File too large. Maximum size is {MAX_FILE_SIZE // (1024*1024)}MB"
            )
        
        # Generate unique filename
        file_ext = Path(file.filename).suffix
        unique_filename = f"{uuid.uuid4()}{file_ext}"
        file_path = UPLOAD_DIR / unique_filename
        
        # Save file to disk
        with open(file_path, "wb") as f:
            f.write(content)
        
        # Get file info
        file_size = len(content)
        mime_type = mimetypes.guess_type(file.filename)[0] or 'application/octet-stream'
        file_category = get_file_category(file.filename)
        
        # Parse tags
        tag_list = []
        if tags:
            tag_list = [tag.strip() for tag in tags.split(',') if tag.strip()]
        
        # Get user info
        user = await db[Collections.USERS].find_one({"_id": ObjectId(user_id)})
        uploader_name = f"{user['profile']['first_name']} {user['profile']['last_name']}"
        
        # Create file document
        file_doc = {
            "original_filename": file.filename,
            "stored_filename": unique_filename,
            "file_path": str(file_path),
            "file_size": file_size,
            "mime_type": mime_type,
            "file_category": file_category,
            "description": description,
            "tags": tag_list,
            "uploader_id": ObjectId(user_id),
            "uploader_name": uploader_name,
            "department": user_department,
            "project_id": ObjectId(project_id) if project_id else None,
            "task_id": ObjectId(task_id) if task_id else None,
            "is_public": is_public,
            "download_count": 0,
            "last_accessed": None,
            "is_deleted": False,
            "created_at": datetime.utcnow(),
            "updated_at": datetime.utcnow()
        }
        
        result = await db[Collections.FILES].insert_one(file_doc)
        
        # Create audit log
        await create_audit_log(
            user_id=user_id,
            action="upload_file",
            resource_type="file",
            resource_id=str(result.inserted_id),
            details={
                "filename": file.filename,
                "size": file_size,
                "category": file_category
            }
        )
        
        return {
            "id": str(result.inserted_id),
            "filename": file.filename,
            "size": file_size,
            "category": file_category,
            "mime_type": mime_type,
            "message": "File uploaded successfully"
        }
        
    except HTTPException:
        raise
    except Exception as e:
        # Clean up file if database operation failed
        if 'file_path' in locals() and file_path.exists():
            file_path.unlink()
        
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to upload file"
        )


@router.get("/")
async def get_files(
    page: int = 1,
    size: int = 20,
    category: Optional[str] = None,
    project_id: Optional[str] = None,
    task_id: Optional[str] = None,
    search: Optional[str] = None,
    current_user: Dict[str, Any] = Depends(get_current_user_token),
    db: AsyncIOMotorDatabase = Depends(get_database)
):
    """Get files with pagination and filtering"""
    
    user_id = current_user.get("sub")
    user_role = current_user.get("role")
    user_department = current_user.get("department")
    
    try:
        # Build filter query
        filter_query = {"is_deleted": False}
        
        # Role-based filtering
        if user_role not in ["admin", "hr"]:
            filter_query["$or"] = [
                {"is_public": True},
                {"uploader_id": ObjectId(user_id)},
                {"department": user_department}
            ]
        
        if category:
            filter_query["file_category"] = category
        
        if project_id:
            filter_query["project_id"] = ObjectId(project_id)
        
        if task_id:
            filter_query["task_id"] = ObjectId(task_id)
        
        if search:
            filter_query["$or"] = [
                {"original_filename": {"$regex": search, "$options": "i"}},
                {"description": {"$regex": search, "$options": "i"}},
                {"tags": {"$in": [{"$regex": search, "$options": "i"}]}}
            ]
        
        # Get total count
        total = await db[Collections.FILES].count_documents(filter_query)
        
        # Calculate pagination
        skip = (page - 1) * size
        pages = (total + size - 1) // size
        
        # Get files
        files = await db[Collections.FILES].find(filter_query).sort("created_at", -1).skip(skip).limit(size).to_list(length=None)
        
        # Format response
        formatted_files = []
        for file_doc in files:
            formatted_files.append({
                "id": str(file_doc["_id"]),
                "filename": file_doc["original_filename"],
                "size": file_doc["file_size"],
                "mime_type": file_doc["mime_type"],
                "category": file_doc["file_category"],
                "description": file_doc.get("description"),
                "tags": file_doc.get("tags", []),
                "uploader_name": file_doc["uploader_name"],
                "department": file_doc["department"],
                "project_id": str(file_doc["project_id"]) if file_doc.get("project_id") else None,
                "task_id": str(file_doc["task_id"]) if file_doc.get("task_id") else None,
                "is_public": file_doc.get("is_public", False),
                "download_count": file_doc.get("download_count", 0),
                "created_at": file_doc["created_at"],
                "updated_at": file_doc["updated_at"]
            })
        
        return {
            "files": formatted_files,
            "total": total,
            "page": page,
            "size": size,
            "pages": pages
        }
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve files"
        )


@router.get("/{file_id}")
async def get_file_info(
    file_id: str,
    current_user: Dict[str, Any] = Depends(get_current_user_token),
    db: AsyncIOMotorDatabase = Depends(get_database)
):
    """Get file information"""
    
    user_id = current_user.get("sub")
    user_role = current_user.get("role")
    user_department = current_user.get("department")
    
    try:
        file_doc = await db[Collections.FILES].find_one({
            "_id": ObjectId(file_id),
            "is_deleted": False
        })
        
        if not file_doc:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="File not found"
            )
        
        # Check access permissions
        has_access = (
            user_role in ["admin", "hr"] or
            file_doc.get("is_public", False) or
            file_doc.get("uploader_id") == ObjectId(user_id) or
            file_doc.get("department") == user_department
        )
        
        if not has_access:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Access denied"
            )
        
        return {
            "id": str(file_doc["_id"]),
            "filename": file_doc["original_filename"],
            "size": file_doc["file_size"],
            "mime_type": file_doc["mime_type"],
            "category": file_doc["file_category"],
            "description": file_doc.get("description"),
            "tags": file_doc.get("tags", []),
            "uploader_id": str(file_doc["uploader_id"]),
            "uploader_name": file_doc["uploader_name"],
            "department": file_doc["department"],
            "project_id": str(file_doc["project_id"]) if file_doc.get("project_id") else None,
            "task_id": str(file_doc["task_id"]) if file_doc.get("task_id") else None,
            "is_public": file_doc.get("is_public", False),
            "download_count": file_doc.get("download_count", 0),
            "last_accessed": file_doc.get("last_accessed"),
            "created_at": file_doc["created_at"],
            "updated_at": file_doc["updated_at"]
        }
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve file information"
        )


@router.get("/{file_id}/download")
async def download_file(
    file_id: str,
    current_user: Dict[str, Any] = Depends(get_current_user_token),
    db: AsyncIOMotorDatabase = Depends(get_database)
):
    """Download a file"""
    
    user_id = current_user.get("sub")
    user_role = current_user.get("role")
    user_department = current_user.get("department")
    
    try:
        file_doc = await db[Collections.FILES].find_one({
            "_id": ObjectId(file_id),
            "is_deleted": False
        })
        
        if not file_doc:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="File not found"
            )
        
        # Check access permissions
        has_access = (
            user_role in ["admin", "hr"] or
            file_doc.get("is_public", False) or
            file_doc.get("uploader_id") == ObjectId(user_id) or
            file_doc.get("department") == user_department
        )
        
        if not has_access:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Access denied"
            )
        
        # Check if file exists on disk
        file_path = Path(file_doc["file_path"])
        if not file_path.exists():
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="File not found on disk"
            )
        
        # Update download count and last accessed
        await db[Collections.FILES].update_one(
            {"_id": ObjectId(file_id)},
            {
                "$inc": {"download_count": 1},
                "$set": {"last_accessed": datetime.utcnow()}
            }
        )
        
        # Create audit log
        await create_audit_log(
            user_id=user_id,
            action="download_file",
            resource_type="file",
            resource_id=file_id,
            details={"filename": file_doc["original_filename"]}
        )
        
        # Stream file
        def file_generator():
            with open(file_path, "rb") as f:
                while chunk := f.read(8192):
                    yield chunk
        
        return StreamingResponse(
            file_generator(),
            media_type=file_doc["mime_type"],
            headers={
                "Content-Disposition": f"attachment; filename={file_doc['original_filename']}"
            }
        )
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to download file"
        )


@router.delete("/{file_id}")
async def delete_file(
    file_id: str,
    current_user: Dict[str, Any] = Depends(get_current_user_token),
    db: AsyncIOMotorDatabase = Depends(get_database)
):
    """Delete a file (soft delete)"""
    
    user_id = current_user.get("sub")
    user_role = current_user.get("role")
    
    try:
        file_doc = await db[Collections.FILES].find_one({
            "_id": ObjectId(file_id),
            "is_deleted": False
        })
        
        if not file_doc:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="File not found"
            )
        
        # Check permissions
        can_delete = (
            user_role in ["admin"] or
            file_doc.get("uploader_id") == ObjectId(user_id)
        )
        
        if not can_delete:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Permission denied"
            )
        
        # Soft delete file
        await db[Collections.FILES].update_one(
            {"_id": ObjectId(file_id)},
            {
                "$set": {
                    "is_deleted": True,
                    "deleted_at": datetime.utcnow(),
                    "deleted_by": ObjectId(user_id),
                    "updated_at": datetime.utcnow()
                }
            }
        )
        
        # Create audit log
        await create_audit_log(
            user_id=user_id,
            action="delete_file",
            resource_type="file",
            resource_id=file_id,
            details={"filename": file_doc["original_filename"]}
        )
        
        return {"message": "File deleted successfully"}
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to delete file"
        )


@router.get("/categories/stats")
async def get_file_stats(
    current_user: Dict[str, Any] = Depends(get_current_user_token),
    db: AsyncIOMotorDatabase = Depends(get_database)
):
    """Get file statistics by category"""
    
    user_id = current_user.get("sub")
    user_role = current_user.get("role")
    user_department = current_user.get("department")
    
    try:
        # Build filter query
        filter_query = {"is_deleted": False}
        
        if user_role not in ["admin", "hr"]:
            filter_query["$or"] = [
                {"is_public": True},
                {"uploader_id": ObjectId(user_id)},
                {"department": user_department}
            ]
        
        # Get statistics
        pipeline = [
            {"$match": filter_query},
            {"$group": {
                "_id": "$file_category",
                "count": {"$sum": 1},
                "total_size": {"$sum": "$file_size"}
            }},
            {"$sort": {"count": -1}}
        ]
        
        stats = await db[Collections.FILES].aggregate(pipeline).to_list(length=None)
        
        # Format response
        formatted_stats = []
        total_files = 0
        total_size = 0
        
        for stat in stats:
            count = stat["count"]
            size = stat["total_size"]
            total_files += count
            total_size += size
            
            formatted_stats.append({
                "category": stat["_id"],
                "count": count,
                "size": size,
                "size_mb": round(size / (1024 * 1024), 2)
            })
        
        return {
            "categories": formatted_stats,
            "total_files": total_files,
            "total_size": total_size,
            "total_size_mb": round(total_size / (1024 * 1024), 2)
        }
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve file statistics"
        )
