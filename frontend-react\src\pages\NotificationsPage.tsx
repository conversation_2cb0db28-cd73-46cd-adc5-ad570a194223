/**
 * Notifications Management Page Component
 * Features: Real-time notifications, filtering, and management
 */
import React, { useState, useEffect, useCallback } from 'react'
import { logger, <PERSON>rror<PERSON><PERSON><PERSON>, PerformanceMonitor } from '../utils/logger'
import type { User, Notification } from '../types'

interface NotificationsPageProps {
  user: User
  onNavigateBack?: () => void
}

interface NotificationsState {
  isLoading: boolean
  notifications: Notification[]
  filteredNotifications: Notification[]
  filters: {
    type: string
    read: string
    search: string
  }
  selectedNotifications: string[]
  error: string | null
}

const notificationTypes = [
  { value: 'all', label: 'All Types' },
  { value: 'info', label: 'Information' },
  { value: 'success', label: 'Success' },
  { value: 'warning', label: 'Warning' },
  { value: 'error', label: 'Error' }
]

export default function NotificationsPage({ user, onNavigateBack }: NotificationsPageProps) {
  const [state, setState] = useState<NotificationsState>({
    isLoading: true,
    notifications: [],
    filteredNotifications: [],
    filters: {
      type: 'all',
      read: 'all',
      search: ''
    },
    selectedNotifications: [],
    error: null
  })

  useEffect(() => {
    logger.componentMount('NotificationsPage')
    loadNotificationsData()
    
    // Set up real-time updates
    const interval = setInterval(loadNotificationsData, 30000) // Refresh every 30 seconds
    
    return () => {
      clearInterval(interval)
      logger.componentUnmount('NotificationsPage')
    }
  }, [])

  useEffect(() => {
    applyFilters()
  }, [state.notifications, state.filters])

  const loadNotificationsData = useCallback(async () => {
    try {
      setState(prev => ({ ...prev, isLoading: true, error: null }))
      logger.info('Loading notifications data', 'NotificationsPage')
      
      PerformanceMonitor.startTimer('notifications_load')

      const response = await fetch('http://localhost:8002/api/notifications', {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('authToken')}`,
          'Content-Type': 'application/json'
        }
      })

      const duration = PerformanceMonitor.endTimer('notifications_load')

      if (response.ok) {
        const data = await response.json()
        setState(prev => ({
          ...prev,
          isLoading: false,
          notifications: data.notifications || []
        }))

        logger.info(`Notifications loaded in ${duration.toFixed(2)}ms`, 'NotificationsPage', {
          notificationCount: data.notifications?.length || 0
        })
      } else {
        // Mock data if API fails
        const mockNotifications: Notification[] = [
          {
            id: '1',
            title: 'New Task Assigned',
            message: 'You have been assigned a new task: "Update project documentation"',
            type: 'info',
            read: false,
            created_at: new Date().toISOString()
          },
          {
            id: '2',
            title: 'Project Completed',
            message: 'The "Website Redesign" project has been successfully completed!',
            type: 'success',
            read: false,
            created_at: new Date(Date.now() - 3600000).toISOString()
          },
          {
            id: '3',
            title: 'Deadline Approaching',
            message: 'The task "Mobile App Development" is due in 2 days.',
            type: 'warning',
            read: true,
            created_at: new Date(Date.now() - 86400000).toISOString()
          },
          {
            id: '4',
            title: 'System Maintenance',
            message: 'Scheduled system maintenance will occur tonight from 2:00 AM to 4:00 AM.',
            type: 'info',
            read: true,
            created_at: new Date(Date.now() - 172800000).toISOString()
          }
        ]

        setState(prev => ({
          ...prev,
          isLoading: false,
          notifications: mockNotifications
        }))

        logger.info('Using mock notifications data', 'NotificationsPage')
      }
      
    } catch (error) {
      const handledError = ErrorHandler.handleApiError(error, 'NotificationsPage')
      setState(prev => ({ 
        ...prev, 
        isLoading: false, 
        error: handledError.message 
      }))
      logger.error('Failed to load notifications data', 'NotificationsPage', error)
    }
  }, [])

  const applyFilters = useCallback(() => {
    let filtered = [...state.notifications]

    if (state.filters.type !== 'all') {
      filtered = filtered.filter(notification => notification.type === state.filters.type)
    }
    if (state.filters.read !== 'all') {
      const isRead = state.filters.read === 'read'
      filtered = filtered.filter(notification => notification.read === isRead)
    }
    if (state.filters.search) {
      const searchLower = state.filters.search.toLowerCase()
      filtered = filtered.filter(notification => 
        notification.title.toLowerCase().includes(searchLower) ||
        notification.message.toLowerCase().includes(searchLower)
      )
    }

    // Sort by creation date (newest first)
    filtered.sort((a, b) => new Date(b.created_at).getTime() - new Date(a.created_at).getTime())

    setState(prev => ({ ...prev, filteredNotifications: filtered }))
  }, [state.notifications, state.filters])

  const handleMarkAsRead = async (notificationIds: string[]) => {
    try {
      const response = await fetch('http://localhost:8002/api/notifications/mark-read', {
        method: 'PATCH',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('authToken')}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ notification_ids: notificationIds })
      })

      if (response.ok || response.status === 404) {
        setState(prev => ({
          ...prev,
          notifications: prev.notifications.map(notification => 
            notificationIds.includes(notification.id) 
              ? { ...notification, read: true }
              : notification
          ),
          selectedNotifications: []
        }))
        
        logger.info('Notifications marked as read', 'NotificationsPage', { count: notificationIds.length })
      } else {
        // Mock behavior if API fails
        setState(prev => ({
          ...prev,
          notifications: prev.notifications.map(notification => 
            notificationIds.includes(notification.id) 
              ? { ...notification, read: true }
              : notification
          ),
          selectedNotifications: []
        }))
        
        logger.info('Mock mark as read', 'NotificationsPage', { count: notificationIds.length })
      }
    } catch (error) {
      logger.error('Failed to mark notifications as read', 'NotificationsPage', error)
      alert('Failed to mark notifications as read. Please try again.')
    }
  }

  const handleMarkAsUnread = async (notificationIds: string[]) => {
    try {
      const response = await fetch('http://localhost:8002/api/notifications/mark-unread', {
        method: 'PATCH',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('authToken')}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ notification_ids: notificationIds })
      })

      if (response.ok || response.status === 404) {
        setState(prev => ({
          ...prev,
          notifications: prev.notifications.map(notification => 
            notificationIds.includes(notification.id) 
              ? { ...notification, read: false }
              : notification
          ),
          selectedNotifications: []
        }))
        
        logger.info('Notifications marked as unread', 'NotificationsPage', { count: notificationIds.length })
      } else {
        // Mock behavior if API fails
        setState(prev => ({
          ...prev,
          notifications: prev.notifications.map(notification => 
            notificationIds.includes(notification.id) 
              ? { ...notification, read: false }
              : notification
          ),
          selectedNotifications: []
        }))
        
        logger.info('Mock mark as unread', 'NotificationsPage', { count: notificationIds.length })
      }
    } catch (error) {
      logger.error('Failed to mark notifications as unread', 'NotificationsPage', error)
      alert('Failed to mark notifications as unread. Please try again.')
    }
  }

  const handleDelete = async (notificationIds: string[]) => {
    if (!confirm(`Are you sure you want to delete ${notificationIds.length} notification(s)?`)) return

    try {
      const response = await fetch('http://localhost:8002/api/notifications', {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('authToken')}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ notification_ids: notificationIds })
      })

      if (response.ok || response.status === 404) {
        setState(prev => ({
          ...prev,
          notifications: prev.notifications.filter(notification => 
            !notificationIds.includes(notification.id)
          ),
          selectedNotifications: []
        }))
        
        logger.info('Notifications deleted', 'NotificationsPage', { count: notificationIds.length })
      } else {
        throw new Error('Failed to delete notifications')
      }
    } catch (error) {
      logger.error('Failed to delete notifications', 'NotificationsPage', error)
      alert('Failed to delete notifications. Please try again.')
    }
  }

  const handleSelectNotification = (notificationId: string) => {
    setState(prev => ({
      ...prev,
      selectedNotifications: prev.selectedNotifications.includes(notificationId)
        ? prev.selectedNotifications.filter(id => id !== notificationId)
        : [...prev.selectedNotifications, notificationId]
    }))
  }

  const handleSelectAll = () => {
    setState(prev => ({
      ...prev,
      selectedNotifications: prev.selectedNotifications.length === prev.filteredNotifications.length
        ? []
        : prev.filteredNotifications.map(n => n.id)
    }))
  }

  const getTypeColor = (type: string): string => {
    switch (type) {
      case 'success': return '#10b981'
      case 'warning': return '#f59e0b'
      case 'error': return '#ef4444'
      case 'info': return '#3b82f6'
      default: return '#6b7280'
    }
  }

  const getTypeIcon = (type: string): string => {
    switch (type) {
      case 'success': return '✅'
      case 'warning': return '⚠️'
      case 'error': return '❌'
      case 'info': return 'ℹ️'
      default: return '📢'
    }
  }

  const formatTimeAgo = (dateString: string): string => {
    const now = new Date()
    const date = new Date(dateString)
    const diffMs = now.getTime() - date.getTime()
    const diffMins = Math.floor(diffMs / 60000)
    const diffHours = Math.floor(diffMs / 3600000)
    const diffDays = Math.floor(diffMs / 86400000)

    if (diffMins < 1) return 'Just now'
    if (diffMins < 60) return `${diffMins}m ago`
    if (diffHours < 24) return `${diffHours}h ago`
    if (diffDays < 7) return `${diffDays}d ago`
    return date.toLocaleDateString()
  }

  if (state.error) {
    return (
      <div style={{
        minHeight: '100vh',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        backgroundColor: '#f9fafb',
        padding: '20px'
      }}>
        <div style={{
          maxWidth: '500px',
          width: '100%',
          backgroundColor: 'white',
          padding: '40px',
          borderRadius: '16px',
          boxShadow: '0 10px 15px -3px rgba(0, 0, 0, 0.1)',
          textAlign: 'center'
        }}>
          <div style={{ fontSize: '48px', marginBottom: '20px' }}>⚠️</div>
          <h2 style={{ color: '#ef4444', marginBottom: '16px' }}>Notifications Error</h2>
          <p style={{ color: '#6b7280', marginBottom: '24px' }}>{state.error}</p>
          <button
            onClick={loadNotificationsData}
            style={{
              padding: '12px 24px',
              backgroundColor: '#667eea',
              color: 'white',
              border: 'none',
              borderRadius: '8px',
              cursor: 'pointer',
              marginRight: '12px'
            }}
          >
            Try Again
          </button>
          <button
            onClick={onNavigateBack}
            style={{
              padding: '12px 24px',
              backgroundColor: '#6b7280',
              color: 'white',
              border: 'none',
              borderRadius: '8px',
              cursor: 'pointer'
            }}
          >
            Go Back
          </button>
        </div>
      </div>
    )
  }

  const unreadCount = state.notifications.filter(n => !n.read).length

  return (
    <div style={{ minHeight: '100vh', backgroundColor: '#f9fafb' }}>
      {/* Header */}
      <header style={{
        backgroundColor: 'white',
        boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1)',
        padding: '16px 0'
      }}>
        <div style={{
          maxWidth: '1400px',
          margin: '0 auto',
          padding: '0 24px',
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center'
        }}>
          <div style={{ display: 'flex', alignItems: 'center', gap: '16px' }}>
            <button
              onClick={onNavigateBack}
              style={{
                padding: '8px 12px',
                backgroundColor: '#f3f4f6',
                border: 'none',
                borderRadius: '6px',
                cursor: 'pointer',
                fontSize: '14px'
              }}
            >
              ← Back
            </button>
            <div>
              <h1 style={{ 
                margin: 0, 
                color: '#1f2937', 
                fontSize: '24px',
                fontWeight: 'bold'
              }}>
                🔔 Notifications
              </h1>
              <p style={{ 
                margin: '4px 0 0 0', 
                color: '#6b7280', 
                fontSize: '14px' 
              }}>
                {unreadCount > 0 ? `${unreadCount} unread notification${unreadCount > 1 ? 's' : ''}` : 'All caught up!'}
              </p>
            </div>
          </div>
          
          <div style={{ display: 'flex', gap: '12px' }}>
            {state.selectedNotifications.length > 0 && (
              <>
                <button
                  onClick={() => handleMarkAsRead(state.selectedNotifications)}
                  style={{
                    padding: '8px 16px',
                    backgroundColor: '#10b981',
                    color: 'white',
                    border: 'none',
                    borderRadius: '6px',
                    cursor: 'pointer',
                    fontSize: '14px'
                  }}
                >
                  ✅ Mark Read
                </button>
                <button
                  onClick={() => handleMarkAsUnread(state.selectedNotifications)}
                  style={{
                    padding: '8px 16px',
                    backgroundColor: '#f59e0b',
                    color: 'white',
                    border: 'none',
                    borderRadius: '6px',
                    cursor: 'pointer',
                    fontSize: '14px'
                  }}
                >
                  📧 Mark Unread
                </button>
                <button
                  onClick={() => handleDelete(state.selectedNotifications)}
                  style={{
                    padding: '8px 16px',
                    backgroundColor: '#ef4444',
                    color: 'white',
                    border: 'none',
                    borderRadius: '6px',
                    cursor: 'pointer',
                    fontSize: '14px'
                  }}
                >
                  🗑️ Delete
                </button>
              </>
            )}
            
            <button
              onClick={loadNotificationsData}
              disabled={state.isLoading}
              style={{
                padding: '8px 16px',
                backgroundColor: state.isLoading ? '#9ca3af' : '#667eea',
                color: 'white',
                border: 'none',
                borderRadius: '6px',
                cursor: state.isLoading ? 'not-allowed' : 'pointer',
                fontSize: '14px'
              }}
            >
              🔄 Refresh
            </button>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main style={{ maxWidth: '1400px', margin: '0 auto', padding: '24px' }}>
        {/* Filters */}
        <div style={{
          backgroundColor: 'white',
          borderRadius: '12px',
          padding: '24px',
          marginBottom: '24px',
          boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)'
        }}>
          <h2 style={{ 
            fontSize: '18px', 
            fontWeight: 'bold', 
            color: '#1f2937', 
            marginBottom: '20px' 
          }}>
            🔍 Filters & Search
          </h2>
          
          <div style={{
            display: 'grid',
            gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))',
            gap: '16px'
          }}>
            <div>
              <label style={{ 
                display: 'block', 
                fontSize: '14px', 
                fontWeight: '500', 
                color: '#374151', 
                marginBottom: '6px' 
              }}>
                Type
              </label>
              <select
                value={state.filters.type}
                onChange={(e) => setState(prev => ({
                  ...prev,
                  filters: { ...prev.filters, type: e.target.value }
                }))}
                style={{
                  width: '100%',
                  padding: '8px 12px',
                  border: '1px solid #d1d5db',
                  borderRadius: '6px',
                  fontSize: '14px',
                  backgroundColor: 'white'
                }}
              >
                {notificationTypes.map(type => (
                  <option key={type.value} value={type.value}>
                    {type.label}
                  </option>
                ))}
              </select>
            </div>

            <div>
              <label style={{ 
                display: 'block', 
                fontSize: '14px', 
                fontWeight: '500', 
                color: '#374151', 
                marginBottom: '6px' 
              }}>
                Status
              </label>
              <select
                value={state.filters.read}
                onChange={(e) => setState(prev => ({
                  ...prev,
                  filters: { ...prev.filters, read: e.target.value }
                }))}
                style={{
                  width: '100%',
                  padding: '8px 12px',
                  border: '1px solid #d1d5db',
                  borderRadius: '6px',
                  fontSize: '14px',
                  backgroundColor: 'white'
                }}
              >
                <option value="all">All Status</option>
                <option value="unread">Unread</option>
                <option value="read">Read</option>
              </select>
            </div>

            <div>
              <label style={{ 
                display: 'block', 
                fontSize: '14px', 
                fontWeight: '500', 
                color: '#374151', 
                marginBottom: '6px' 
              }}>
                Search
              </label>
              <input
                type="text"
                placeholder="Search notifications..."
                value={state.filters.search}
                onChange={(e) => setState(prev => ({
                  ...prev,
                  filters: { ...prev.filters, search: e.target.value }
                }))}
                style={{
                  width: '100%',
                  padding: '8px 12px',
                  border: '1px solid #d1d5db',
                  borderRadius: '6px',
                  fontSize: '14px'
                }}
              />
            </div>
          </div>
        </div>

        {/* Notifications List */}
        <div style={{
          backgroundColor: 'white',
          borderRadius: '12px',
          padding: '24px',
          boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)'
        }}>
          <div style={{
            display: 'flex',
            justifyContent: 'space-between',
            alignItems: 'center',
            marginBottom: '20px'
          }}>
            <h2 style={{ 
              fontSize: '18px', 
              fontWeight: 'bold', 
              color: '#1f2937',
              margin: 0
            }}>
              🔔 Notifications ({state.filteredNotifications.length})
            </h2>
            
            <div style={{ display: 'flex', alignItems: 'center', gap: '12px' }}>
              <label style={{
                display: 'flex',
                alignItems: 'center',
                gap: '8px',
                fontSize: '14px',
                color: '#374151',
                cursor: 'pointer'
              }}>
                <input
                  type="checkbox"
                  checked={state.selectedNotifications.length === state.filteredNotifications.length && state.filteredNotifications.length > 0}
                  onChange={handleSelectAll}
                  style={{ cursor: 'pointer' }}
                />
                Select All
              </label>
            </div>
          </div>
          
          {state.isLoading ? (
            <div style={{
              display: 'flex',
              justifyContent: 'center',
              alignItems: 'center',
              height: '200px'
            }}>
              <div style={{
                width: '40px',
                height: '40px',
                border: '4px solid #e5e7eb',
                borderTop: '4px solid #667eea',
                borderRadius: '50%',
                animation: 'spin 1s linear infinite'
              }} />
            </div>
          ) : state.filteredNotifications.length > 0 ? (
            <div style={{ display: 'flex', flexDirection: 'column', gap: '12px' }}>
              {state.filteredNotifications.map((notification) => (
                <div
                  key={notification.id}
                  style={{
                    padding: '20px',
                    border: '1px solid #e5e7eb',
                    borderRadius: '8px',
                    backgroundColor: notification.read ? '#fafafa' : '#f0f9ff',
                    borderLeft: `4px solid ${getTypeColor(notification.type)}`,
                    transition: 'all 0.2s'
                  }}
                  onMouseEnter={(e) => {
                    e.currentTarget.style.backgroundColor = notification.read ? '#f3f4f6' : '#e0f2fe'
                  }}
                  onMouseLeave={(e) => {
                    e.currentTarget.style.backgroundColor = notification.read ? '#fafafa' : '#f0f9ff'
                  }}
                >
                  <div style={{
                    display: 'flex',
                    alignItems: 'flex-start',
                    gap: '16px'
                  }}>
                    <input
                      type="checkbox"
                      checked={state.selectedNotifications.includes(notification.id)}
                      onChange={() => handleSelectNotification(notification.id)}
                      style={{ cursor: 'pointer', marginTop: '4px' }}
                    />
                    
                    <div style={{
                      fontSize: '24px',
                      marginTop: '2px'
                    }}>
                      {getTypeIcon(notification.type)}
                    </div>
                    
                    <div style={{ flex: 1 }}>
                      <div style={{
                        display: 'flex',
                        justifyContent: 'space-between',
                        alignItems: 'flex-start',
                        marginBottom: '8px'
                      }}>
                        <h3 style={{
                          margin: 0,
                          fontSize: '16px',
                          fontWeight: notification.read ? '500' : '600',
                          color: '#1f2937'
                        }}>
                          {notification.title}
                        </h3>
                        
                        <div style={{ display: 'flex', alignItems: 'center', gap: '12px' }}>
                          <span style={{
                            fontSize: '12px',
                            color: '#6b7280'
                          }}>
                            {formatTimeAgo(notification.created_at)}
                          </span>
                          
                          {!notification.read && (
                            <div style={{
                              width: '8px',
                              height: '8px',
                              backgroundColor: getTypeColor(notification.type),
                              borderRadius: '50%'
                            }} />
                          )}
                        </div>
                      </div>
                      
                      <p style={{
                        margin: 0,
                        fontSize: '14px',
                        color: '#6b7280',
                        lineHeight: '1.5'
                      }}>
                        {notification.message}
                      </p>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div style={{
              textAlign: 'center',
              color: '#6b7280',
              fontStyle: 'italic',
              padding: '60px 0'
            }}>
              <div style={{ fontSize: '48px', marginBottom: '16px' }}>🔔</div>
              <p>No notifications found matching your criteria</p>
            </div>
          )}
        </div>
      </main>

      {/* CSS Animation */}
      <style>{`
        @keyframes spin {
          0% { transform: rotate(0deg); }
          100% { transform: rotate(360deg); }
        }
      `}</style>
    </div>
  )
}
