"""
WebSocket API endpoint
"""
import json
import logging
from fastapi import APIRouter, WebSocket, WebSocketDisconnect, Query
from typing import Optional

from ..services.simple_websocket_service import websocket_manager
from ..core.database import get_database
from ..core.config import Collections
from bson import ObjectId

router = APIRouter()
logger = logging.getLogger(__name__)

@router.websocket("/ws")
async def websocket_endpoint(
    websocket: WebSocket,
    userId: Optional[str] = Query(None),
    role: Optional[str] = Query(None),
    department: Optional[str] = Query(None)
):
    """WebSocket endpoint for real-time communication"""
    
    # Validate required parameters
    if not userId:
        await websocket.close(code=4000, reason="Missing userId parameter")
        return
    
    # Authenticate user (basic validation)
    try:
        db = await get_database()
        user = await db[Collections.USERS].find_one({"_id": ObjectId(userId)})
        if not user:
            await websocket.close(code=4001, reason="User not found")
            return
        
        # Prepare user data
        user_data = {
            'user_id': userId,
            'username': user.get('username'),
            'email': user.get('email'),
            'role': role or user.get('role'),
            'department': department or user.get('department')
        }
        
        # Connect user
        await websocket_manager.connect(websocket, userId, user_data)
        
        try:
            # Listen for messages
            while True:
                # Receive message from client
                data = await websocket.receive_text()
                
                try:
                    # Parse JSON message
                    message = json.loads(data)
                    
                    # Handle the message
                    await websocket_manager.handle_message(userId, message)
                    
                except json.JSONDecodeError:
                    logger.error(f"Invalid JSON received from user {userId}: {data}")
                    await websocket_manager.send_personal_message(userId, {
                        'type': 'error',
                        'message': 'Invalid JSON format'
                    })
                except Exception as e:
                    logger.error(f"Error handling message from user {userId}: {e}")
                    await websocket_manager.send_personal_message(userId, {
                        'type': 'error',
                        'message': 'Error processing message'
                    })
        
        except WebSocketDisconnect:
            logger.info(f"WebSocket disconnected for user {userId}")
        except Exception as e:
            logger.error(f"WebSocket error for user {userId}: {e}")
        finally:
            # Clean up connection
            await websocket_manager.disconnect(userId)
            
    except Exception as e:
        logger.error(f"WebSocket connection error: {e}")
        try:
            await websocket.close(code=4002, reason="Authentication failed")
        except:
            pass
