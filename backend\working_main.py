#!/usr/bin/env python3
"""
CTNL WORK-BOARD - AI Work Management Backend Server
AI-powered work management system with MongoDB and advanced features
"""

import asyncio
import logging
from contextlib import asynccontextmanager
from fastapi import FastAPI, HTTPException, Request
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
from motor.motor_asyncio import AsyncIOMotorClient
from pydantic import BaseModel
from passlib.context import Crypt<PERSON>ontext
from datetime import datetime
import uvicorn
import sys
import os
import socketio

# Add the app directory to the path
sys.path.append(os.path.join(os.path.dirname(__file__), 'app'))

from app.core.config import settings

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Password hashing
pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")

# Global database connection
db_client = None
database = None

class LoginRequest(BaseModel):
    username: str
    password: str

class RegisterRequest(BaseModel):
    username: str
    email: str
    password: str
    role: str
    department: str

class LoginResponse(BaseModel):
    access_token: str
    refresh_token: str
    token_type: str
    user: dict

class RegisterResponse(BaseModel):
    message: str
    user: dict

async def connect_database():
    """Connect to MongoDB"""
    global db_client, database
    try:
        logger.info("Connecting to MongoDB...")
        db_client = AsyncIOMotorClient(settings.MONGODB_URL)
        await db_client.admin.command('ping')
        database = db_client[settings.DATABASE_NAME]
        logger.info("✅ Connected to MongoDB successfully!")
        return True
    except Exception as e:
        logger.error(f"❌ MongoDB connection failed: {e}")
        return False

async def close_database():
    """Close MongoDB connection"""
    global db_client
    if db_client:
        db_client.close()
        logger.info("Database connection closed")

@asynccontextmanager
async def lifespan(app: FastAPI):
    """Application lifespan manager"""
    logger.info("🚀 Starting CTNL WORK-BOARD AI Management System...")
    
    # Connect to database
    await connect_database()
    
    logger.info("✅ Application startup complete")
    yield
    
    # Cleanup
    logger.info("🔄 Shutting down application...")
    await close_database()
    logger.info("✅ Application shutdown complete")

# Create FastAPI app
app = FastAPI(
    title="CTNL WORK-BOARD - AI Work Management System",
    description="AI-Powered Work Management and Productivity Platform",
    version="1.0.0",
    lifespan=lifespan
)

# CORS middleware - Enhanced for browser compatibility
app.add_middleware(
    CORSMiddleware,
    allow_origins=[
        "http://localhost:3000",
        "http://localhost:3001",
        "http://localhost:5173",  # Vite React frontend
        "http://127.0.0.1:5173"   # Alternative localhost format
    ],
    allow_credentials=True,
    allow_methods=["GET", "POST", "PUT", "DELETE", "OPTIONS", "HEAD", "PATCH"],
    allow_headers=[
        "Accept",
        "Accept-Language",
        "Content-Language",
        "Content-Type",
        "Authorization",
        "X-Requested-With",
        "Origin",
        "Access-Control-Request-Method",
        "Access-Control-Request-Headers"
    ],
    expose_headers=["*"],
    max_age=3600,
)

# Add WebSocket routes
from app.api.websocket import router as websocket_router
app.include_router(websocket_router, tags=["WebSocket"])

@app.get("/health")
async def health_check():
    """Health check endpoint"""
    return {
        "status": "healthy",
        "timestamp": datetime.utcnow().isoformat(),
        "version": "1.0.0",
        "database": "connected" if database is not None else "disconnected"
    }

@app.options("/api/auth/login")
async def login_options():
    """Handle preflight OPTIONS request for login endpoint"""
    print("🔧 OPTIONS request received for /api/auth/login")
    return {"message": "CORS preflight OK"}

@app.get("/cors-test")
async def cors_test():
    """Simple CORS test endpoint"""
    return {"message": "CORS test successful", "timestamp": datetime.utcnow().isoformat()}

@app.post("/api/auth/login", response_model=LoginResponse)
async def login(login_data: LoginRequest, request: Request):
    """User login endpoint"""
    try:
        if database is None:
            raise HTTPException(
                status_code=503,
                detail="Database service unavailable"
            )
        
        logger.info(f"Login attempt for user: {login_data.username}")
        
        # Find user
        users_collection = database.users
        user = await users_collection.find_one({
            "$or": [
                {"username": login_data.username},
                {"email": login_data.username}
            ]
        })
        
        if not user:
            logger.warning(f"User not found: {login_data.username}")
            raise HTTPException(
                status_code=401,
                detail="Invalid credentials"
            )
        
        # Verify password
        if not pwd_context.verify(login_data.password, user["password"]):
            logger.warning(f"Invalid password for user: {login_data.username}")
            raise HTTPException(
                status_code=401,
                detail="Invalid credentials"
            )
        
        logger.info(f"✅ Login successful for user: {login_data.username}")
        
        # Create response
        profile = user.get("profile", {})
        user_data = {
            "id": str(user["_id"]),
            "employee_id": user.get("employee_id", "EMP001"),
            "username": user["username"],
            "email": user["email"],
            "role": user["role"],
            "profile": {
                "first_name": profile.get("first_name", "User"),
                "last_name": profile.get("last_name", "Name"),
                "phone": profile.get("phone", ""),
                "address": profile.get("address", ""),
                "bio": profile.get("bio", ""),
                "avatar_url": profile.get("avatar_url", ""),
                "skills": profile.get("skills", []),
                "certifications": profile.get("certifications", []),
                "languages": profile.get("languages", ["English"])
            },
            "department": user.get("department", "General"),
            "permissions": user.get("permissions", ["read"]),
            "is_active": user.get("is_active", True),
            "is_verified": user.get("is_verified", True),
            "created_at": user.get("created_at", "2025-01-01T00:00:00Z"),
            "updated_at": user.get("updated_at", "2025-01-01T00:00:00Z")
        }
        
        return LoginResponse(
            access_token="dummy_token_for_demo",
            refresh_token="dummy_refresh_token_for_demo",
            token_type="bearer",
            user=user_data
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Login error: {e}")
        raise HTTPException(
            status_code=500,
            detail="Internal server error"
        )

@app.options("/api/auth/register")
async def register_options():
    """Handle preflight OPTIONS request for register endpoint"""
    print("🔧 OPTIONS request received for /api/auth/register")
    return {"message": "CORS preflight OK"}

@app.post("/api/auth/register", response_model=RegisterResponse)
async def register(register_data: RegisterRequest, request: Request):
    """User registration endpoint"""
    try:
        if database is None:
            raise HTTPException(
                status_code=500,
                detail="Database connection not available"
            )

        logger.info(f"🔐 Registration attempt for user: {register_data.username}")

        # Check if user already exists
        users_collection = database.users
        existing_user = await users_collection.find_one({
            "$or": [
                {"username": register_data.username},
                {"email": register_data.email}
            ]
        })

        if existing_user:
            logger.warning(f"❌ Registration failed - user already exists: {register_data.username}")
            raise HTTPException(
                status_code=400,
                detail="User with this username or email already exists"
            )

        # Hash the password
        hashed_password = pwd_context.hash(register_data.password)

        # Create new user document
        user_doc = {
            "username": register_data.username,
            "email": register_data.email,
            "password": hashed_password,
            "role": register_data.role,
            "department": register_data.department,
            "created_at": datetime.utcnow(),
            "is_active": True
        }

        # Insert user into database
        result = await users_collection.insert_one(user_doc)

        if result.inserted_id:
            logger.info(f"✅ User registered successfully: {register_data.username}")

            # Return user data (without password)
            user_data = {
                "id": str(result.inserted_id),
                "username": register_data.username,
                "email": register_data.email,
                "role": register_data.role,
                "department": register_data.department
            }

            return RegisterResponse(
                message="User registered successfully",
                user=user_data
            )
        else:
            raise HTTPException(
                status_code=500,
                detail="Failed to create user"
            )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Registration error: {e}")
        raise HTTPException(
            status_code=500,
            detail="Internal server error"
        )

@app.get("/api/auth/me")
async def get_current_user():
    """Get current user info"""
    return {"message": "User info endpoint - requires authentication"}

# Tasks endpoints
@app.get("/api/tasks")
async def get_tasks():
    """Get all tasks"""
    return {
        "tasks": [
            {
                "id": "1",
                "title": "Sample Task 1",
                "description": "This is a sample task",
                "status": "pending",
                "priority": "medium",
                "created_at": datetime.utcnow().isoformat(),
                "updated_at": datetime.utcnow().isoformat()
            }
        ]
    }

@app.post("/api/tasks")
async def create_task():
    """Create a new task"""
    return {
        "id": "new-task",
        "title": "New Task",
        "status": "pending",
        "created_at": datetime.utcnow().isoformat()
    }

@app.patch("/api/tasks/{task_id}")
async def update_task(task_id: str):
    """Update a task"""
    return {
        "id": task_id,
        "status": "updated",
        "updated_at": datetime.utcnow().isoformat()
    }

# Projects endpoints
@app.get("/api/projects")
async def get_projects():
    """Get all projects"""
    return {
        "projects": [
            {
                "id": "1",
                "name": "Sample Project",
                "status": "active",
                "created_at": datetime.utcnow().isoformat()
            }
        ]
    }

# Users endpoints
@app.get("/api/users")
async def get_users():
    """Get all users"""
    return {
        "users": [
            {
                "id": "1",
                "username": "demo",
                "email": "<EMAIL>",
                "role": "employee"
            }
        ]
    }

# Dashboard endpoints
@app.get("/api/dashboard/stats")
async def get_dashboard_stats():
    """Get enhanced dashboard statistics"""
    return {
        "total_tasks": 24,
        "completed_tasks": 18,
        "pending_tasks": 4,
        "in_progress_tasks": 2,
        "total_projects": 6,
        "active_projects": 4,
        "completed_projects": 2,
        "total_users": 12,
        "active_users": 8,
        "today_hours": 6.5,
        "week_hours": 32.5,
        "productivity_score": 85,
        "ai_suggestions": 3,
        "overdue_tasks": 1,
        "upcoming_deadlines": 5,
        "department_stats": {
            "development": {"tasks": 12, "completion_rate": 90},
            "design": {"tasks": 6, "completion_rate": 85},
            "management": {"tasks": 4, "completion_rate": 95},
            "qa": {"tasks": 2, "completion_rate": 100}
        }
    }

@app.get("/api/dashboard/activity")
async def get_dashboard_activity():
    """Get recent dashboard activity with AI insights"""
    return {
        "activities": [
            {
                "id": "1",
                "type": "task_completed",
                "message": "Frontend optimization task completed",
                "user": "John Doe",
                "timestamp": datetime.utcnow().isoformat(),
                "priority": "high"
            },
            {
                "id": "2",
                "type": "ai_suggestion",
                "message": "AI suggested task prioritization optimization",
                "user": "AI Assistant",
                "timestamp": datetime.utcnow().isoformat(),
                "priority": "medium"
            },
            {
                "id": "3",
                "type": "project_milestone",
                "message": "Project Alpha reached 75% completion",
                "user": "Project Manager",
                "timestamp": datetime.utcnow().isoformat(),
                "priority": "high"
            },
            {
                "id": "4",
                "type": "user_login",
                "message": "Sarah Wilson logged in",
                "user": "Sarah Wilson",
                "timestamp": datetime.utcnow().isoformat(),
                "priority": "low"
            },
            {
                "id": "5",
                "type": "deadline_alert",
                "message": "3 tasks due tomorrow",
                "user": "System",
                "timestamp": datetime.utcnow().isoformat(),
                "priority": "high"
            }
        ],
        "summary": {
            "total_activities": 5,
            "high_priority": 3,
            "ai_activities": 1,
            "last_updated": datetime.utcnow().isoformat()
        }
    }

# Notifications endpoints
@app.get("/api/notifications")
async def get_notifications():
    """Get all notifications"""
    return {
        "notifications": [
            {
                "id": "1",
                "title": "Welcome!",
                "message": "Welcome to the system",
                "type": "info",
                "read": False,
                "created_at": datetime.utcnow().isoformat()
            }
        ]
    }

@app.patch("/api/notifications/mark-read")
async def mark_notifications_read():
    """Mark notifications as read"""
    return {"message": "Notifications marked as read"}

@app.patch("/api/notifications/mark-unread")
async def mark_notifications_unread():
    """Mark notifications as unread"""
    return {"message": "Notifications marked as unread"}

# AI Assistant endpoints
@app.get("/api/ai/conversations")
async def get_ai_conversations():
    """Get AI conversations"""
    return {
        "conversations": [
            {
                "id": "1",
                "title": "Project Planning Assistant",
                "updated_at": datetime.utcnow().isoformat(),
                "message_count": 5,
                "status": "active"
            },
            {
                "id": "2",
                "title": "Task Optimization",
                "updated_at": datetime.utcnow().isoformat(),
                "message_count": 3,
                "status": "active"
            }
        ]
    }

@app.post("/api/ai/chat")
async def ai_chat(request: dict):
    """AI Chat endpoint for intelligent assistance"""
    try:
        message = request.get("message", "")
        conversation_id = request.get("conversation_id", "default")

        # AI Response Logic with GitHub Integration
        ai_responses = {
            "create task": {
                "response": "I can help you create a new task! What would you like to name it and what's the priority level?",
                "suggestions": ["High Priority", "Medium Priority", "Low Priority", "Create GitHub Issue"],
                "action": "task_creation"
            },
            "github": {
                "response": "I can help you with GitHub integration! I can connect your repositories, create issues from tasks, and track your development progress.",
                "suggestions": ["Connect GitHub", "View Repositories", "Create Issue", "Repository Stats"],
                "action": "github_integration"
            },
            "connect github": {
                "response": "To connect your GitHub account, I'll need your access token. This will allow me to create issues, track repositories, and sync your development work with CTNL WORK-BOARD.",
                "suggestions": ["Get GitHub Token", "Repository Access", "Issue Management"],
                "action": "github_connect"
            },
            "repository": {
                "response": "Here are your connected repositories: ctnl-work-board (TypeScript), project-alpha (Python). Would you like to create an issue or check repository stats?",
                "data": {
                    "total_repos": 2,
                    "languages": ["TypeScript", "Python"],
                    "total_issues": 4
                },
                "suggestions": ["Create Issue", "Repository Stats", "Recent Commits"]
            },
            "project status": {
                "response": "Here's your current project status: 3 active projects, 2 completed this month. GitHub integration shows 4 open issues across repositories.",
                "data": {
                    "active_projects": 3,
                    "completed_projects": 2,
                    "overdue_tasks": 1,
                    "github_issues": 4
                }
            },
            "productivity": {
                "response": "Based on your work patterns, you're most productive between 9-11 AM. Your GitHub activity shows consistent commits during these hours.",
                "insights": ["Peak hours: 9-11 AM", "Average daily tasks: 8", "Completion rate: 85%", "GitHub commits: 15 this week"]
            }
        }

        # Simple keyword matching for demo
        response_key = None
        for key in ai_responses.keys():
            if key.lower() in message.lower():
                response_key = key
                break

        if response_key:
            ai_response = ai_responses[response_key]
        else:
            ai_response = {
                "response": f"I understand you're asking about: '{message}'. I can help with task management, project planning, productivity insights, and work optimization. What specific area would you like assistance with?",
                "suggestions": ["Create Task", "Project Status", "Productivity Tips", "Schedule Optimization"]
            }

        return {
            "conversation_id": conversation_id,
            "message": message,
            "ai_response": ai_response["response"],
            "suggestions": ai_response.get("suggestions", []),
            "data": ai_response.get("data", {}),
            "insights": ai_response.get("insights", []),
            "timestamp": datetime.utcnow().isoformat()
        }

    except Exception as e:
        logger.error(f"AI Chat error: {str(e)}")
        return {
            "error": "AI service temporarily unavailable",
            "fallback_response": "I'm currently experiencing technical difficulties. Please try again in a moment.",
            "timestamp": datetime.utcnow().isoformat()
        }

@app.post("/api/ai/generate-task")
async def ai_generate_task(request: dict):
    """AI-powered task generation"""
    try:
        description = request.get("description", "")
        priority = request.get("priority", "medium")

        # AI task generation logic
        task_templates = {
            "meeting": {
                "title": "Team Meeting - {topic}",
                "estimated_time": "1 hour",
                "category": "collaboration",
                "subtasks": ["Prepare agenda", "Send invites", "Book room", "Follow up"]
            },
            "development": {
                "title": "Development Task - {feature}",
                "estimated_time": "4 hours",
                "category": "development",
                "subtasks": ["Plan implementation", "Write code", "Test functionality", "Code review"]
            },
            "analysis": {
                "title": "Analysis - {subject}",
                "estimated_time": "2 hours",
                "category": "research",
                "subtasks": ["Gather data", "Analyze trends", "Create report", "Present findings"]
            }
        }

        # Simple keyword detection
        task_type = "general"
        for key in task_templates.keys():
            if key in description.lower():
                task_type = key
                break

        if task_type in task_templates:
            template = task_templates[task_type]
            generated_task = {
                "id": f"ai-task-{datetime.utcnow().timestamp()}",
                "title": template["title"].format(topic=description, feature=description, subject=description),
                "description": f"AI-generated task based on: {description}",
                "priority": priority,
                "estimated_time": template["estimated_time"],
                "category": template["category"],
                "subtasks": template["subtasks"],
                "status": "pending",
                "created_by": "AI Assistant",
                "created_at": datetime.utcnow().isoformat()
            }
        else:
            generated_task = {
                "id": f"ai-task-{datetime.utcnow().timestamp()}",
                "title": f"Task: {description}",
                "description": f"AI-generated task: {description}",
                "priority": priority,
                "estimated_time": "2 hours",
                "category": "general",
                "status": "pending",
                "created_by": "AI Assistant",
                "created_at": datetime.utcnow().isoformat()
            }

        return {
            "success": True,
            "task": generated_task,
            "ai_insights": [
                f"Estimated completion time: {generated_task.get('estimated_time', '2 hours')}",
                f"Recommended priority: {priority}",
                "Consider breaking down into smaller subtasks for better tracking"
            ]
        }

    except Exception as e:
        logger.error(f"AI Task Generation error: {str(e)}")
        return {
            "success": False,
            "error": "Task generation failed",
            "fallback": "Please create the task manually"
        }

@app.get("/api/ai/insights")
async def get_ai_insights():
    """Get AI-powered productivity insights"""
    return {
        "insights": [
            {
                "type": "productivity",
                "title": "Peak Performance Hours",
                "description": "You're most productive between 9-11 AM",
                "recommendation": "Schedule important tasks during morning hours",
                "confidence": 0.85
            },
            {
                "type": "workload",
                "title": "Task Distribution",
                "description": "You have 3 high-priority tasks pending",
                "recommendation": "Consider delegating or rescheduling some tasks",
                "confidence": 0.92
            },
            {
                "type": "efficiency",
                "title": "Completion Rate",
                "description": "85% task completion rate this week",
                "recommendation": "Great job! Maintain current workflow",
                "confidence": 0.95
            }
        ],
        "generated_at": datetime.utcnow().isoformat()
    }

# GitHub Integration endpoints
@app.post("/api/github/connect")
async def connect_github(request: dict):
    """Connect GitHub account for repository integration"""
    try:
        github_token = request.get("access_token", "")
        user_id = request.get("user_id", "")

        # In a real implementation, you would:
        # 1. Validate the GitHub token
        # 2. Store the token securely
        # 3. Fetch user's GitHub repositories

        return {
            "success": True,
            "message": "GitHub account connected successfully",
            "repositories": [
                {
                    "id": "1",
                    "name": "ctnl-work-board",
                    "full_name": "user/ctnl-work-board",
                    "private": False,
                    "description": "CTNL WORK-BOARD AI Work Management System",
                    "language": "TypeScript",
                    "stars": 0,
                    "forks": 0,
                    "updated_at": datetime.utcnow().isoformat()
                },
                {
                    "id": "2",
                    "name": "project-alpha",
                    "full_name": "user/project-alpha",
                    "private": True,
                    "description": "Internal project repository",
                    "language": "Python",
                    "stars": 5,
                    "forks": 2,
                    "updated_at": datetime.utcnow().isoformat()
                }
            ],
            "user_info": {
                "login": "developer",
                "name": "CTNL Developer",
                "email": "<EMAIL>",
                "avatar_url": "https://github.com/identicons/developer.png"
            }
        }

    except Exception as e:
        logger.error(f"GitHub connection error: {str(e)}")
        return {
            "success": False,
            "error": "Failed to connect GitHub account",
            "message": "Please check your access token and try again"
        }

@app.get("/api/github/repositories")
async def get_github_repositories():
    """Get user's GitHub repositories"""
    return {
        "repositories": [
            {
                "id": "1",
                "name": "ctnl-work-board",
                "full_name": "user/ctnl-work-board",
                "private": False,
                "description": "CTNL WORK-BOARD AI Work Management System",
                "language": "TypeScript",
                "stars": 0,
                "forks": 0,
                "issues": 3,
                "pull_requests": 1,
                "last_commit": {
                    "message": "Add AI assistant functionality",
                    "author": "CTNL Developer",
                    "date": datetime.utcnow().isoformat()
                }
            }
        ],
        "total_repositories": 1,
        "total_stars": 0,
        "total_forks": 0
    }

@app.post("/api/github/create-issue")
async def create_github_issue(request: dict):
    """Create GitHub issue from task"""
    try:
        task_title = request.get("title", "")
        task_description = request.get("description", "")
        repository = request.get("repository", "")
        labels = request.get("labels", [])

        # In a real implementation, you would use GitHub API
        issue_number = 42  # Mock issue number

        return {
            "success": True,
            "issue": {
                "number": issue_number,
                "title": task_title,
                "body": task_description,
                "state": "open",
                "labels": labels,
                "url": f"https://github.com/{repository}/issues/{issue_number}",
                "created_at": datetime.utcnow().isoformat()
            },
            "message": f"GitHub issue #{issue_number} created successfully"
        }

    except Exception as e:
        logger.error(f"GitHub issue creation error: {str(e)}")
        return {
            "success": False,
            "error": "Failed to create GitHub issue",
            "message": "Please check your repository access and try again"
        }

# Manager endpoints
@app.get("/api/manager/department-logs")
async def get_department_logs(department: str = "Sales"):
    """Get department clock logs"""
    return {
        "logs": [
            {
                "id": "1",
                "userName": "John Doe",
                "action": "clock_in",
                "timestamp": datetime.utcnow().isoformat(),
                "department": department,
                "location": "Office"
            },
            {
                "id": "2",
                "userName": "Sarah Wilson",
                "action": "clock_out",
                "timestamp": datetime.utcnow().isoformat(),
                "department": department,
                "location": "Remote"
            }
        ],
        "department": department,
        "total_logs": 2
    }

@app.get("/api/manager/department-activity")
async def get_department_activity(department: str = "Sales"):
    """Get department activity"""
    return {
        "activities": [
            {
                "id": "1",
                "userName": "John Doe",
                "action": "Task Completed",
                "description": "Finished quarterly report",
                "timestamp": datetime.utcnow().isoformat(),
                "department": department
            },
            {
                "id": "2",
                "userName": "Sarah Wilson",
                "action": "Project Update",
                "description": "Updated client presentation",
                "timestamp": datetime.utcnow().isoformat(),
                "department": department
            }
        ],
        "department": department,
        "total_activities": 2
    }

@app.get("/api/manager/team-members")
async def get_team_members(department: str = "Sales"):
    """Get team members"""
    return {
        "members": [
            {
                "id": "1",
                "name": "John Doe",
                "role": "Senior Sales Rep",
                "status": "active",
                "hours_today": 6.5,
                "department": department,
                "last_activity": datetime.utcnow().isoformat()
            },
            {
                "id": "2",
                "name": "Sarah Wilson",
                "role": "Sales Manager",
                "status": "active",
                "hours_today": 7.2,
                "department": department,
                "last_activity": datetime.utcnow().isoformat()
            }
        ],
        "department": department,
        "total_members": 2
    }

@app.exception_handler(Exception)
async def global_exception_handler(request: Request, exc: Exception):
    """Global exception handler"""
    logger.error(f"Unhandled exception: {exc}")
    return JSONResponse(
        status_code=500,
        content={"detail": "Internal server error", "error": str(exc)}
    )

if __name__ == "__main__":
    logger.info("🚀 Starting CTNL WORK-BOARD Backend Server...")
    logger.info("📍 Server will run on: http://localhost:8002")
    logger.info("🔗 Health check: http://localhost:8002/health")
    logger.info("🔐 Login endpoint: POST http://localhost:8002/api/auth/login")
    
    uvicorn.run(
        app,
        host="0.0.0.0",
        port=8002,
        reload=False,  # Disable reload to prevent issues
        log_level="info"
    )
