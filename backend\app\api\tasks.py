"""
Tasks API routes
"""
from fastapi import APIRouter, Depends, HTTPException, status, Query
from motor.motor_asyncio import AsyncIOMotorDatabase
from typing import Dict, Any, List, Optional
from datetime import datetime
from bson import ObjectId
from pydantic import BaseModel

from ..core.database import get_database, create_audit_log
from ..core.security import get_current_user_token
from ..core.config import Collections
from ..models.project import Task, TaskStatus, Priority
from ..services.simple_websocket_service import broadcast_task_update

router = APIRouter()


class TaskCreate(BaseModel):
    title: str
    description: Optional[str] = None
    status: TaskStatus = TaskStatus.TODO
    priority: Priority = Priority.MEDIUM
    assignee_id: Optional[str] = None
    project_id: Optional[str] = None
    due_date: Optional[datetime] = None
    estimated_hours: Optional[float] = None
    tags: List[str] = []
    labels: List[str] = []
    parent_task_id: Optional[str] = None


class TaskUpdate(BaseModel):
    title: Optional[str] = None
    description: Optional[str] = None
    status: Optional[TaskStatus] = None
    priority: Optional[Priority] = None
    assignee_id: Optional[str] = None
    progress: Optional[float] = None
    due_date: Optional[datetime] = None
    estimated_hours: Optional[float] = None
    tags: Optional[List[str]] = None
    labels: Optional[List[str]] = None


class TaskComment(BaseModel):
    content: str


@router.post("/")
async def create_task(
    task_data: TaskCreate,
    current_user: Dict[str, Any] = Depends(get_current_user_token),
    db: AsyncIOMotorDatabase = Depends(get_database)
):
    """Create a new task"""
    
    user_id = current_user.get("sub")
    user_role = current_user.get("role")
    user_department = current_user.get("department")
    
    try:
        # Validate project access if project_id provided
        if task_data.project_id:
            project = await db[Collections.PROJECTS].find_one({"_id": ObjectId(task_data.project_id)})
            if not project:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="Project not found"
                )
            
            # Check project access
            has_access = (
                user_role in ["admin", "hr"] or
                project["department"] == user_department or
                ObjectId(user_id) in project.get("team_members", []) or
                project["manager_id"] == ObjectId(user_id)
            )
            
            if not has_access:
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail="Access denied to project"
                )
        
        # Validate assignee if provided
        assignee_name = None
        if task_data.assignee_id:
            assignee = await db[Collections.USERS].find_one({"_id": ObjectId(task_data.assignee_id)})
            if not assignee:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="Assignee not found"
                )
            assignee_name = f"{assignee['profile']['first_name']} {assignee['profile']['last_name']}"
        
        # Get project name if project_id provided
        project_name = None
        if task_data.project_id:
            project_name = project["name"]
        
        # Create task document
        task_doc = {
            "title": task_data.title,
            "description": task_data.description,
            "status": task_data.status.value,
            "priority": task_data.priority.value,
            "progress": 0.0,
            "assignee_id": ObjectId(task_data.assignee_id) if task_data.assignee_id else None,
            "assignee_name": assignee_name,
            "reviewer_id": None,
            "project_id": ObjectId(task_data.project_id) if task_data.project_id else None,
            "project_name": project_name,
            "milestone_id": None,
            "due_date": task_data.due_date,
            "start_date": None,
            "completion_date": None,
            "estimated_hours": task_data.estimated_hours,
            "actual_hours": 0.0,
            "time_logs": [],
            "parent_task_id": ObjectId(task_data.parent_task_id) if task_data.parent_task_id else None,
            "subtasks": [],
            "dependencies": [],
            "tags": task_data.tags,
            "labels": task_data.labels,
            "attachments": [],
            "checklist": [],
            "comments": [],
            "watchers": [ObjectId(user_id)],
            "created_at": datetime.utcnow(),
            "updated_at": datetime.utcnow(),
            "created_by": ObjectId(user_id),
            "is_recurring": False,
            "recurrence_pattern": None,
            "is_template": False
        }
        
        result = await db[Collections.TASKS].insert_one(task_doc)
        
        # Update parent task if this is a subtask
        if task_data.parent_task_id:
            await db[Collections.TASKS].update_one(
                {"_id": ObjectId(task_data.parent_task_id)},
                {"$push": {"subtasks": result.inserted_id}}
            )
        
        # Create audit log
        await create_audit_log(
            user_id=user_id,
            action="create_task",
            resource_type="task",
            resource_id=str(result.inserted_id),
            details={"title": task_data.title, "project_id": task_data.project_id}
        )
        
        # Get created task
        created_task = await db[Collections.TASKS].find_one({"_id": result.inserted_id})
        created_task["id"] = str(created_task["_id"])
        del created_task["_id"]
        
        # Convert ObjectIds to strings
        if created_task.get("assignee_id"):
            created_task["assignee_id"] = str(created_task["assignee_id"])
        if created_task.get("project_id"):
            created_task["project_id"] = str(created_task["project_id"])
        if created_task.get("parent_task_id"):
            created_task["parent_task_id"] = str(created_task["parent_task_id"])
        created_task["created_by"] = str(created_task["created_by"])
        created_task["subtasks"] = [str(id) for id in created_task["subtasks"]]
        created_task["watchers"] = [str(id) for id in created_task["watchers"]]

        # Broadcast task creation via WebSocket
        try:
            await broadcast_task_update(created_task)
        except Exception as e:
            # Don't fail the request if WebSocket broadcast fails
            print(f"WebSocket broadcast failed: {e}")

        return created_task
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to create task"
        )


@router.get("/")
async def get_tasks(
    page: int = Query(1, ge=1),
    size: int = Query(20, ge=1, le=100),
    status: Optional[str] = Query(None),
    priority: Optional[str] = Query(None),
    assignee_id: Optional[str] = Query(None),
    project_id: Optional[str] = Query(None),
    search: Optional[str] = Query(None),
    current_user: Dict[str, Any] = Depends(get_current_user_token),
    db: AsyncIOMotorDatabase = Depends(get_database)
):
    """Get tasks with pagination and filtering"""
    
    user_id = current_user.get("sub")
    user_role = current_user.get("role")
    user_department = current_user.get("department")
    
    try:
        # Build filter query
        filter_query = {}
        
        # Role-based filtering
        if user_role not in ["admin", "hr"]:
            # Non-admin users see tasks they're assigned to, created, or in their department projects
            user_obj_id = ObjectId(user_id)
            
            # Get user's accessible projects
            accessible_projects = await db[Collections.PROJECTS].find({
                "$or": [
                    {"department": user_department},
                    {"team_members": user_obj_id},
                    {"manager_id": user_obj_id}
                ]
            }).to_list(length=None)
            
            accessible_project_ids = [p["_id"] for p in accessible_projects]
            
            filter_query["$or"] = [
                {"assignee_id": user_obj_id},
                {"created_by": user_obj_id},
                {"watchers": user_obj_id},
                {"project_id": {"$in": accessible_project_ids}}
            ]
        
        if status:
            filter_query["status"] = status
        
        if priority:
            filter_query["priority"] = priority
        
        if assignee_id:
            filter_query["assignee_id"] = ObjectId(assignee_id)
        
        if project_id:
            filter_query["project_id"] = ObjectId(project_id)
        
        if search:
            filter_query["$or"] = [
                {"title": {"$regex": search, "$options": "i"}},
                {"description": {"$regex": search, "$options": "i"}},
                {"tags": {"$in": [{"$regex": search, "$options": "i"}]}},
                {"labels": {"$in": [{"$regex": search, "$options": "i"}]}}
            ]
        
        # Get total count
        total = await db[Collections.TASKS].count_documents(filter_query)
        
        # Calculate pagination
        skip = (page - 1) * size
        pages = (total + size - 1) // size
        
        # Get tasks
        tasks = await db[Collections.TASKS].find(filter_query).sort("updated_at", -1).skip(skip).limit(size).to_list(length=None)
        
        # Format response
        formatted_tasks = []
        for task in tasks:
            formatted_task = {
                "id": str(task["_id"]),
                "title": task["title"],
                "description": task.get("description"),
                "status": task["status"],
                "priority": task["priority"],
                "progress": task.get("progress", 0.0),
                "assignee_id": str(task["assignee_id"]) if task.get("assignee_id") else None,
                "assignee_name": task.get("assignee_name"),
                "project_id": str(task["project_id"]) if task.get("project_id") else None,
                "project_name": task.get("project_name"),
                "due_date": task.get("due_date"),
                "estimated_hours": task.get("estimated_hours"),
                "actual_hours": task.get("actual_hours", 0.0),
                "tags": task.get("tags", []),
                "labels": task.get("labels", []),
                "comments": len(task.get("comments", [])),
                "attachments": len(task.get("attachments", [])),
                "subtasks": len(task.get("subtasks", [])),
                "created_at": task["created_at"],
                "updated_at": task["updated_at"]
            }
            formatted_tasks.append(formatted_task)
        
        return {
            "tasks": formatted_tasks,
            "total": total,
            "page": page,
            "size": size,
            "pages": pages
        }
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve tasks"
        )


@router.get("/{task_id}")
async def get_task(
    task_id: str,
    current_user: Dict[str, Any] = Depends(get_current_user_token),
    db: AsyncIOMotorDatabase = Depends(get_database)
):
    """Get task by ID"""
    
    user_id = current_user.get("sub")
    user_role = current_user.get("role")
    user_department = current_user.get("department")
    
    try:
        task = await db[Collections.TASKS].find_one({"_id": ObjectId(task_id)})
        
        if not task:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Task not found"
            )
        
        # Check access permissions
        user_obj_id = ObjectId(user_id)
        has_access = (
            user_role in ["admin", "hr"] or
            task.get("assignee_id") == user_obj_id or
            task.get("created_by") == user_obj_id or
            user_obj_id in task.get("watchers", [])
        )
        
        # Check project access if task belongs to a project
        if not has_access and task.get("project_id"):
            project = await db[Collections.PROJECTS].find_one({"_id": task["project_id"]})
            if project:
                has_access = (
                    project["department"] == user_department or
                    user_obj_id in project.get("team_members", []) or
                    project["manager_id"] == user_obj_id
                )
        
        if not has_access:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Access denied"
            )
        
        # Format response
        formatted_task = {
            "id": str(task["_id"]),
            "title": task["title"],
            "description": task.get("description"),
            "status": task["status"],
            "priority": task["priority"],
            "progress": task.get("progress", 0.0),
            "assignee_id": str(task["assignee_id"]) if task.get("assignee_id") else None,
            "assignee_name": task.get("assignee_name"),
            "reviewer_id": str(task["reviewer_id"]) if task.get("reviewer_id") else None,
            "project_id": str(task["project_id"]) if task.get("project_id") else None,
            "project_name": task.get("project_name"),
            "milestone_id": str(task["milestone_id"]) if task.get("milestone_id") else None,
            "due_date": task.get("due_date"),
            "start_date": task.get("start_date"),
            "completion_date": task.get("completion_date"),
            "estimated_hours": task.get("estimated_hours"),
            "actual_hours": task.get("actual_hours", 0.0),
            "parent_task_id": str(task["parent_task_id"]) if task.get("parent_task_id") else None,
            "subtasks": [str(id) for id in task.get("subtasks", [])],
            "tags": task.get("tags", []),
            "labels": task.get("labels", []),
            "attachments": task.get("attachments", []),
            "checklist": task.get("checklist", []),
            "comments": task.get("comments", []),
            "watchers": [str(id) for id in task.get("watchers", [])],
            "created_at": task["created_at"],
            "updated_at": task["updated_at"],
            "created_by": str(task["created_by"]),
            "is_recurring": task.get("is_recurring", False),
            "recurrence_pattern": task.get("recurrence_pattern")
        }
        
        return formatted_task
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve task"
        )


@router.put("/{task_id}")
async def update_task(
    task_id: str,
    update_data: TaskUpdate,
    current_user: Dict[str, Any] = Depends(get_current_user_token),
    db: AsyncIOMotorDatabase = Depends(get_database)
):
    """Update task"""
    
    user_id = current_user.get("sub")
    user_role = current_user.get("role")
    
    try:
        task = await db[Collections.TASKS].find_one({"_id": ObjectId(task_id)})
        
        if not task:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Task not found"
            )
        
        # Check permissions
        user_obj_id = ObjectId(user_id)
        can_update = (
            user_role in ["admin"] or
            task.get("assignee_id") == user_obj_id or
            task.get("created_by") == user_obj_id
        )
        
        if not can_update:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Permission denied"
            )
        
        # Build update document
        update_doc = {"updated_at": datetime.utcnow()}
        
        if update_data.title is not None:
            update_doc["title"] = update_data.title
        
        if update_data.description is not None:
            update_doc["description"] = update_data.description
        
        if update_data.status is not None:
            update_doc["status"] = update_data.status.value
            if update_data.status == TaskStatus.COMPLETED:
                update_doc["completion_date"] = datetime.utcnow()
                update_doc["progress"] = 100.0
        
        if update_data.priority is not None:
            update_doc["priority"] = update_data.priority.value
        
        if update_data.assignee_id is not None:
            if update_data.assignee_id:
                assignee = await db[Collections.USERS].find_one({"_id": ObjectId(update_data.assignee_id)})
                if not assignee:
                    raise HTTPException(
                        status_code=status.HTTP_400_BAD_REQUEST,
                        detail="Assignee not found"
                    )
                update_doc["assignee_id"] = ObjectId(update_data.assignee_id)
                update_doc["assignee_name"] = f"{assignee['profile']['first_name']} {assignee['profile']['last_name']}"
            else:
                update_doc["assignee_id"] = None
                update_doc["assignee_name"] = None
        
        if update_data.progress is not None:
            update_doc["progress"] = max(0.0, min(100.0, update_data.progress))
        
        if update_data.due_date is not None:
            update_doc["due_date"] = update_data.due_date
        
        if update_data.estimated_hours is not None:
            update_doc["estimated_hours"] = update_data.estimated_hours
        
        if update_data.tags is not None:
            update_doc["tags"] = update_data.tags
        
        if update_data.labels is not None:
            update_doc["labels"] = update_data.labels
        
        # Update task
        await db[Collections.TASKS].update_one(
            {"_id": ObjectId(task_id)},
            {"$set": update_doc}
        )
        
        # Create audit log
        await create_audit_log(
            user_id=user_id,
            action="update_task",
            resource_type="task",
            resource_id=task_id,
            details=update_doc
        )

        # Get updated task for WebSocket broadcast
        updated_task = await get_task(task_id, current_user, db)

        # Broadcast task update via WebSocket
        try:
            await broadcast_task_update(updated_task)
        except Exception as e:
            # Don't fail the request if WebSocket broadcast fails
            print(f"WebSocket broadcast failed: {e}")

        # Return updated task
        return updated_task
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to update task"
        )


@router.delete("/{task_id}")
async def delete_task(
    task_id: str,
    current_user: Dict[str, Any] = Depends(get_current_user_token),
    db: AsyncIOMotorDatabase = Depends(get_database)
):
    """Delete task (soft delete)"""
    
    user_id = current_user.get("sub")
    user_role = current_user.get("role")
    
    try:
        task = await db[Collections.TASKS].find_one({"_id": ObjectId(task_id)})
        
        if not task:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Task not found"
            )
        
        # Check permissions
        user_obj_id = ObjectId(user_id)
        can_delete = (
            user_role in ["admin"] or
            task.get("created_by") == user_obj_id
        )
        
        if not can_delete:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Permission denied"
            )
        
        # Soft delete task
        await db[Collections.TASKS].update_one(
            {"_id": ObjectId(task_id)},
            {
                "$set": {
                    "is_deleted": True,
                    "deleted_at": datetime.utcnow(),
                    "deleted_by": user_obj_id,
                    "updated_at": datetime.utcnow()
                }
            }
        )
        
        # Remove from parent task subtasks if applicable
        if task.get("parent_task_id"):
            await db[Collections.TASKS].update_one(
                {"_id": task["parent_task_id"]},
                {"$pull": {"subtasks": ObjectId(task_id)}}
            )
        
        # Create audit log
        await create_audit_log(
            user_id=user_id,
            action="delete_task",
            resource_type="task",
            resource_id=task_id,
            details={"title": task["title"]}
        )

        # Broadcast task deletion via WebSocket
        try:
            await broadcast_task_update({
                "id": task_id,
                "action": "deleted",
                "title": task["title"],
                "project_id": str(task["project_id"]) if task.get("project_id") else None,
                "assignee_id": str(task["assignee_id"]) if task.get("assignee_id") else None,
                "created_by": str(task["created_by"])
            })
        except Exception as e:
            # Don't fail the request if WebSocket broadcast fails
            print(f"WebSocket broadcast failed: {e}")

        return {"message": "Task deleted successfully"}
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to delete task"
        )


@router.post("/{task_id}/comments")
async def add_task_comment(
    task_id: str,
    comment_data: TaskComment,
    current_user: Dict[str, Any] = Depends(get_current_user_token),
    db: AsyncIOMotorDatabase = Depends(get_database)
):
    """Add comment to task"""
    
    user_id = current_user.get("sub")
    
    try:
        task = await db[Collections.TASKS].find_one({"_id": ObjectId(task_id)})
        
        if not task:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Task not found"
            )
        
        # Get user info
        user = await db[Collections.USERS].find_one({"_id": ObjectId(user_id)})
        author_name = f"{user['profile']['first_name']} {user['profile']['last_name']}"
        
        # Create comment
        comment = {
            "id": str(ObjectId()),
            "content": comment_data.content,
            "author_id": ObjectId(user_id),
            "author_name": author_name,
            "created_at": datetime.utcnow(),
            "updated_at": None,
            "is_edited": False,
            "attachments": []
        }
        
        # Add comment to task
        await db[Collections.TASKS].update_one(
            {"_id": ObjectId(task_id)},
            {
                "$push": {"comments": comment},
                "$set": {"updated_at": datetime.utcnow()}
            }
        )
        
        # Create audit log
        await create_audit_log(
            user_id=user_id,
            action="add_task_comment",
            resource_type="task",
            resource_id=task_id,
            details={"comment_length": len(comment_data.content)}
        )
        
        return {"message": "Comment added successfully", "comment": comment}
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to add comment"
        )
