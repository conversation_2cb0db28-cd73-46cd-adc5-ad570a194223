"""
AI Service for OpenAI integration and intelligent automation
"""
import openai
from typing import Dict, Any, List, Optional
from datetime import datetime
from bson import ObjectId
from motor.motor_asyncio import AsyncIOMotorDatabase

from ..core.config import settings, Collections
from ..core.database import create_audit_log
import json
import asyncio


class AIService:
    """AI service for intelligent automation and assistance"""
    
    def __init__(self, db: AsyncIOMotorDatabase):
        self.db = db
        self.client = openai.AsyncOpenAI(api_key=settings.OPENAI_API_KEY)
        
    async def chat_completion(
        self, 
        user_id: str, 
        message: str, 
        context: Optional[Dict[str, Any]] = None,
        conversation_id: Optional[str] = None
    ) -> Dict[str, Any]:
        """Generate AI chat completion with context awareness"""
        
        try:
            # Get user information for context
            user = await self.db[Collections.USERS].find_one({"_id": ObjectId(user_id)})
            if not user:
                raise ValueError("User not found")
            
            # Build system context
            system_context = self._build_system_context(user, context)
            
            # Get conversation history if conversation_id provided
            messages = [{"role": "system", "content": system_context}]
            
            if conversation_id:
                conversation = await self.db[Collections.AI_CONVERSATIONS].find_one(
                    {"_id": ObjectId(conversation_id)}
                )
                if conversation:
                    messages.extend(conversation.get("messages", [])[-10:])  # Last 10 messages
            
            # Add current user message
            messages.append({"role": "user", "content": message})
            
            # Generate AI response
            response = await self.client.chat.completions.create(
                model=settings.OPENAI_MODEL,
                messages=messages,
                max_tokens=1000,
                temperature=0.7,
                presence_penalty=0.1,
                frequency_penalty=0.1
            )
            
            ai_message = response.choices[0].message.content
            
            # Save conversation
            conversation_data = await self._save_conversation(
                user_id, message, ai_message, context, conversation_id
            )
            
            # Create audit log
            await create_audit_log(
                user_id=user_id,
                action="ai_chat",
                resource_type="ai_conversation",
                resource_id=str(conversation_data["conversation_id"]),
                details={"message_length": len(message), "response_length": len(ai_message)}
            )
            
            return {
                "response": ai_message,
                "conversation_id": str(conversation_data["conversation_id"]),
                "message_id": str(conversation_data["message_id"]),
                "usage": {
                    "prompt_tokens": response.usage.prompt_tokens,
                    "completion_tokens": response.usage.completion_tokens,
                    "total_tokens": response.usage.total_tokens
                }
            }
            
        except Exception as e:
            raise Exception(f"AI chat completion failed: {str(e)}")
    
    async def analyze_document(
        self, 
        user_id: str, 
        document_content: str, 
        document_type: str = "general",
        analysis_type: str = "summary"
    ) -> Dict[str, Any]:
        """Analyze document content using AI"""
        
        try:
            # Build analysis prompt based on type
            analysis_prompts = {
                "summary": "Please provide a comprehensive summary of this document, highlighting key points and main themes.",
                "extract_tasks": "Extract all tasks, action items, and deliverables mentioned in this document. Format as a structured list.",
                "sentiment": "Analyze the sentiment and tone of this document. Identify any concerns, positive aspects, or neutral observations.",
                "compliance": "Review this document for compliance issues, policy violations, or areas that need attention.",
                "financial": "Extract and analyze all financial information, costs, budgets, and monetary values mentioned."
            }
            
            prompt = analysis_prompts.get(analysis_type, analysis_prompts["summary"])
            
            messages = [
                {
                    "role": "system",
                    "content": f"You are an expert document analyst specializing in {document_type} documents. Provide detailed, actionable insights."
                },
                {
                    "role": "user",
                    "content": f"{prompt}\n\nDocument content:\n{document_content}"
                }
            ]
            
            response = await self.client.chat.completions.create(
                model=settings.OPENAI_MODEL,
                messages=messages,
                max_tokens=1500,
                temperature=0.3
            )
            
            analysis_result = response.choices[0].message.content
            
            # Save analysis result
            analysis_doc = {
                "user_id": ObjectId(user_id),
                "document_type": document_type,
                "analysis_type": analysis_type,
                "content_preview": document_content[:500],
                "analysis_result": analysis_result,
                "created_at": datetime.utcnow(),
                "usage": {
                    "prompt_tokens": response.usage.prompt_tokens,
                    "completion_tokens": response.usage.completion_tokens,
                    "total_tokens": response.usage.total_tokens
                }
            }
            
            result = await self.db["document_analyses"].insert_one(analysis_doc)
            
            # Create audit log
            await create_audit_log(
                user_id=user_id,
                action="document_analysis",
                resource_type="document_analysis",
                resource_id=str(result.inserted_id),
                details={
                    "document_type": document_type,
                    "analysis_type": analysis_type,
                    "content_length": len(document_content)
                }
            )
            
            return {
                "analysis_id": str(result.inserted_id),
                "analysis_result": analysis_result,
                "document_type": document_type,
                "analysis_type": analysis_type,
                "created_at": datetime.utcnow().isoformat()
            }
            
        except Exception as e:
            raise Exception(f"Document analysis failed: {str(e)}")
    
    async def generate_task_suggestions(
        self, 
        user_id: str, 
        project_context: Optional[Dict[str, Any]] = None
    ) -> List[Dict[str, Any]]:
        """Generate intelligent task suggestions based on user context"""
        
        try:
            # Get user's recent tasks and projects
            user_obj_id = ObjectId(user_id)
            
            recent_tasks = await self.db[Collections.TASKS].find({
                "assignee_id": user_obj_id
            }).sort("created_at", -1).limit(10).to_list(length=None)
            
            user_projects = await self.db[Collections.PROJECTS].find({
                "team_members": user_obj_id
            }).to_list(length=None)
            
            # Build context for AI
            context = {
                "recent_tasks": [
                    {
                        "title": task.get("title"),
                        "status": task.get("status"),
                        "priority": task.get("priority")
                    } for task in recent_tasks
                ],
                "active_projects": [
                    {
                        "name": project.get("name"),
                        "status": project.get("status"),
                        "progress": project.get("progress", 0)
                    } for project in user_projects if project.get("status") == "active"
                ]
            }
            
            if project_context:
                context.update(project_context)
            
            prompt = f"""
            Based on the following context, suggest 5 relevant tasks that would be beneficial:
            
            Context: {json.dumps(context, indent=2)}
            
            Please provide task suggestions in the following JSON format:
            [
                {{
                    "title": "Task title",
                    "description": "Detailed description",
                    "priority": "high|medium|low",
                    "estimated_hours": 2,
                    "category": "development|design|testing|documentation|meeting"
                }}
            ]
            """
            
            response = await self.client.chat.completions.create(
                model=settings.OPENAI_MODEL,
                messages=[
                    {
                        "role": "system",
                        "content": "You are a project management AI assistant. Generate practical, actionable task suggestions."
                    },
                    {"role": "user", "content": prompt}
                ],
                max_tokens=1000,
                temperature=0.8
            )
            
            # Parse AI response
            ai_response = response.choices[0].message.content
            
            try:
                # Extract JSON from response
                import re
                json_match = re.search(r'\[.*\]', ai_response, re.DOTALL)
                if json_match:
                    suggestions = json.loads(json_match.group())
                else:
                    # Fallback to manual parsing
                    suggestions = self._parse_task_suggestions_fallback(ai_response)
            except:
                suggestions = self._parse_task_suggestions_fallback(ai_response)
            
            # Create audit log
            await create_audit_log(
                user_id=user_id,
                action="generate_task_suggestions",
                resource_type="ai_suggestion",
                resource_id="task_suggestions",
                details={"suggestions_count": len(suggestions)}
            )
            
            return suggestions
            
        except Exception as e:
            raise Exception(f"Task suggestion generation failed: {str(e)}")
    
    async def smart_scheduling(
        self, 
        user_id: str, 
        tasks: List[Dict[str, Any]],
        constraints: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """AI-powered smart scheduling of tasks"""
        
        try:
            # Get user's work patterns and availability
            user_obj_id = ObjectId(user_id)
            
            # Analyze user's time logs to understand work patterns
            time_logs = await self.db[Collections.TIME_LOGS].find({
                "user_id": user_obj_id
            }).sort("clock_in", -1).limit(50).to_list(length=None)
            
            work_patterns = self._analyze_work_patterns(time_logs)
            
            # Build scheduling context
            context = {
                "tasks": tasks,
                "work_patterns": work_patterns,
                "constraints": constraints or {}
            }
            
            prompt = f"""
            Create an optimal schedule for the following tasks based on work patterns and constraints:
            
            Context: {json.dumps(context, indent=2)}
            
            Consider:
            1. Task priorities and dependencies
            2. User's productive hours
            3. Task complexity and estimated duration
            4. Buffer time for unexpected issues
            5. Work-life balance
            
            Provide a schedule in JSON format with recommended time slots.
            """
            
            response = await self.client.chat.completions.create(
                model=settings.OPENAI_MODEL,
                messages=[
                    {
                        "role": "system",
                        "content": "You are an AI scheduling assistant specializing in productivity optimization."
                    },
                    {"role": "user", "content": prompt}
                ],
                max_tokens=1200,
                temperature=0.5
            )
            
            schedule_result = response.choices[0].message.content
            
            return {
                "schedule": schedule_result,
                "work_patterns": work_patterns,
                "recommendations": self._extract_scheduling_recommendations(schedule_result)
            }
            
        except Exception as e:
            raise Exception(f"Smart scheduling failed: {str(e)}")
    
    def _build_system_context(self, user: Dict[str, Any], context: Optional[Dict[str, Any]]) -> str:
        """Build system context for AI conversations"""
        
        base_context = f"""
        You are CTNL AI, an intelligent work management assistant. You're helping {user['profile']['first_name']} {user['profile']['last_name']}, 
        who works as a {user.get('position', user['role'])} in the {user['department']} department.
        
        Your capabilities include:
        - Task and project management assistance
        - Time tracking and productivity insights
        - Document analysis and summarization
        - Workflow optimization suggestions
        - Meeting scheduling and coordination
        - Data analysis and reporting
        
        Always be professional, helpful, and context-aware. Provide actionable insights and suggestions.
        """
        
        if context:
            base_context += f"\n\nAdditional context: {json.dumps(context, indent=2)}"
        
        return base_context
    
    async def _save_conversation(
        self, 
        user_id: str, 
        user_message: str, 
        ai_message: str, 
        context: Optional[Dict[str, Any]], 
        conversation_id: Optional[str]
    ) -> Dict[str, Any]:
        """Save conversation to database"""
        
        message_data = [
            {
                "id": str(ObjectId()),
                "role": "user",
                "content": user_message,
                "timestamp": datetime.utcnow().isoformat()
            },
            {
                "id": str(ObjectId()),
                "role": "assistant", 
                "content": ai_message,
                "timestamp": datetime.utcnow().isoformat()
            }
        ]
        
        if conversation_id:
            # Update existing conversation
            await self.db[Collections.AI_CONVERSATIONS].update_one(
                {"_id": ObjectId(conversation_id)},
                {
                    "$push": {"messages": {"$each": message_data}},
                    "$set": {"updated_at": datetime.utcnow()}
                }
            )
            return {
                "conversation_id": ObjectId(conversation_id),
                "message_id": message_data[1]["id"]
            }
        else:
            # Create new conversation
            conversation_doc = {
                "user_id": ObjectId(user_id),
                "messages": message_data,
                "context": context,
                "created_at": datetime.utcnow(),
                "updated_at": datetime.utcnow()
            }
            
            result = await self.db[Collections.AI_CONVERSATIONS].insert_one(conversation_doc)
            return {
                "conversation_id": result.inserted_id,
                "message_id": message_data[1]["id"]
            }
    
    def _analyze_work_patterns(self, time_logs: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Analyze user's work patterns from time logs"""
        
        if not time_logs:
            return {"productive_hours": [9, 10, 11, 14, 15, 16], "avg_daily_hours": 8}
        
        # Analyze productive hours, work duration patterns, etc.
        # This is a simplified version - in production, you'd do more sophisticated analysis
        
        productive_hours = []
        daily_hours = []
        
        for log in time_logs:
            if log.get("clock_in") and log.get("duration"):
                hour = log["clock_in"].hour
                productive_hours.append(hour)
                daily_hours.append(log["duration"] / 3600)
        
        # Find most common productive hours
        from collections import Counter
        hour_counts = Counter(productive_hours)
        top_hours = [hour for hour, count in hour_counts.most_common(6)]
        
        avg_hours = sum(daily_hours) / len(daily_hours) if daily_hours else 8
        
        return {
            "productive_hours": sorted(top_hours),
            "avg_daily_hours": round(avg_hours, 1),
            "total_sessions": len(time_logs)
        }
    
    def _parse_task_suggestions_fallback(self, ai_response: str) -> List[Dict[str, Any]]:
        """Fallback parser for task suggestions if JSON parsing fails"""
        
        # Simple fallback - extract task-like patterns from text
        suggestions = []
        lines = ai_response.split('\n')
        
        current_task = {}
        for line in lines:
            line = line.strip()
            if line.startswith(('1.', '2.', '3.', '4.', '5.', '-', '*')):
                if current_task:
                    suggestions.append(current_task)
                current_task = {
                    "title": line.split('.', 1)[-1].strip() if '.' in line else line[1:].strip(),
                    "description": "AI-generated task suggestion",
                    "priority": "medium",
                    "estimated_hours": 2,
                    "category": "general"
                }
        
        if current_task:
            suggestions.append(current_task)
        
        return suggestions[:5]  # Limit to 5 suggestions
    
    def _extract_scheduling_recommendations(self, schedule_result: str) -> List[str]:
        """Extract key recommendations from scheduling result"""
        
        recommendations = []
        lines = schedule_result.split('\n')
        
        for line in lines:
            line = line.strip()
            if any(keyword in line.lower() for keyword in ['recommend', 'suggest', 'consider', 'tip', 'advice']):
                recommendations.append(line)
        
        return recommendations[:5]  # Limit to 5 recommendations
