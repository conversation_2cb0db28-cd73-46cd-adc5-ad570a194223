/**
 * WebSocket Context for real-time features
 */
import React, { createContext, useContext, useEffect, useState, ReactNode, useCallback } from 'react'
import websocketService from '../services/websocket'
import { useAuth } from './AuthContext'

interface WebSocketContextType {
  isConnected: boolean
  send: (message: any) => void
  subscribe: (type: string, callback: (data: any) => void) => () => void
  joinRoom: (room: string) => void
  leaveRoom: (room: string) => void
  sendPing: () => void
  updateStatus: (status: 'online' | 'away' | 'busy' | 'offline') => void
}

const WebSocketContext = createContext<WebSocketContextType | undefined>(undefined)

interface WebSocketProviderProps {
  children: ReactNode
}

export function WebSocketProvider({ children }: WebSocketProviderProps) {
  const [isConnected, setIsConnected] = useState(false)
  const { user, isAuthenticated } = useAuth()

  useEffect(() => {
    // Update connection status based on websocket service
    const checkConnection = () => {
      setIsConnected(websocketService.connected)
    }

    // Check connection status periodically
    const interval = setInterval(checkConnection, 1000)
    
    return () => clearInterval(interval)
  }, [])

  const send = useCallback((message: any) => {
    websocketService.send(message)
  }, [])

  const subscribe = useCallback((type: string, callback: (data: any) => void) => {
    return websocketService.subscribe(type, callback)
  }, [])

  const joinRoom = useCallback((room: string) => {
    websocketService.joinRoom(room)
  }, [])

  const leaveRoom = useCallback((room: string) => {
    websocketService.leaveRoom(room)
  }, [])

  const sendPing = useCallback(() => {
    websocketService.sendPing()
  }, [])

  const updateStatus = useCallback((status: 'online' | 'away' | 'busy' | 'offline') => {
    websocketService.updateStatus(status)
  }, [])

  const value: WebSocketContextType = {
    isConnected,
    send,
    subscribe,
    joinRoom,
    leaveRoom,
    sendPing,
    updateStatus,
  }

  return (
    <WebSocketContext.Provider value={value}>
      {children}
    </WebSocketContext.Provider>
  )
}

export function useWebSocket() {
  const context = useContext(WebSocketContext)
  if (context === undefined) {
    throw new Error('useWebSocket must be used within a WebSocketProvider')
  }
  return context
}

// Custom hooks for specific real-time features
export function useRealTimeUpdates() {
  const { subscribe } = useWebSocket()

  const subscribeToTaskUpdates = useCallback((callback: (task: any) => void) => {
    return subscribe('task_update', callback)
  }, [subscribe])

  const subscribeToProjectUpdates = useCallback((callback: (project: any) => void) => {
    return subscribe('project_update', callback)
  }, [subscribe])

  const subscribeToNotifications = useCallback((callback: (notification: any) => void) => {
    return subscribe('notification', callback)
  }, [subscribe])

  return {
    subscribeToTaskUpdates,
    subscribeToProjectUpdates,
    subscribeToNotifications,
  }
}

export function useUserPresence() {
  const { send, subscribe, updateStatus } = useWebSocket()

  const subscribeToUserStatusUpdates = useCallback((callback: (data: any) => void) => {
    return subscribe('user_status_update', callback)
  }, [subscribe])

  const subscribeToUserTyping = useCallback((callback: (data: any) => void) => {
    return subscribe('user_typing', callback)
  }, [subscribe])

  const sendTypingIndicator = useCallback((room: string, typing: boolean = true) => {
    send({ type: 'user_typing', room, typing })
  }, [send])

  return {
    updateStatus,
    subscribeToUserStatusUpdates,
    subscribeToUserTyping,
    sendTypingIndicator,
  }
}

export default WebSocketContext
