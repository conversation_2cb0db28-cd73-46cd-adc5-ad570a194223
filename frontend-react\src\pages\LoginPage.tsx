/**
 * Professional Login Page Component
 * Features: Form validation, error handling, loading states, responsive design
 */
import React, { useState, useEffect } from 'react'
import { logger, ErrorHandler, PerformanceMonitor } from '../utils/logger'
import type { AuthResponse, LoginCredentials, User } from '../types'

interface LoginPageProps {
  onLoginSuccess?: (user: User) => void
  onNavigateToSignup?: () => void
  onNavigateToClockIn?: () => void
}

interface LoginState {
  isLoading: boolean
  message: string
  messageType: 'info' | 'success' | 'error'
  showPassword: boolean
  rememberMe: boolean
}

export default function LoginPage({ 
  onLoginSuccess, 
  onNavigateToSignup, 
  onNavigateToClockIn 
}: LoginPageProps) {
  const [credentials, setCredentials] = useState<LoginCredentials>({
    username: '',
    password: ''
  })
  
  const [state, setState] = useState<LoginState>({
    isLoading: false,
    message: '',
    messageType: 'info',
    showPassword: false,
    rememberMe: false
  })

  useEffect(() => {
    logger.componentMount('LoginPage')

    // Check for saved credentials
    const savedUsername = localStorage.getItem('rememberedUsername')
    if (savedUsername) {
      setCredentials(prev => ({ ...prev, username: savedUsername }))
      setState(prev => ({ ...prev, rememberMe: true }))
    }

    return () => {
      logger.componentUnmount('LoginPage')
    }
  }, [])

  // Add CSS animations for message popups
  React.useEffect(() => {
    const style = document.createElement('style')
    style.textContent = `
      @keyframes fadeInSlide {
        0% {
          opacity: 0;
          transform: translateY(-20px) scale(0.95);
        }
        100% {
          opacity: 1;
          transform: translateY(0) scale(1);
        }
      }

      @keyframes shake {
        0%, 100% { transform: translateX(0); }
        25% { transform: translateX(-5px); }
        75% { transform: translateX(5px); }
      }
    `
    document.head.appendChild(style)
    return () => document.head.removeChild(style)
  }, [])

  const updateMessage = (message: string, type: 'info' | 'success' | 'error' = 'info') => {
    setState(prev => ({ ...prev, message, messageType: type }))
  }

  const validateForm = (): boolean => {
    if (!credentials.username.trim()) {
      updateMessage('Please enter your username or email', 'error')
      return false
    }
    
    if (!credentials.password.trim()) {
      updateMessage('Please enter your password', 'error')
      return false
    }
    
    if (credentials.password.length < 6) {
      updateMessage('Password must be at least 6 characters long', 'error')
      return false
    }
    
    return true
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!validateForm()) return

    setState(prev => ({ ...prev, isLoading: true }))
    updateMessage('Signing in...', 'info')

    logger.authLogin(credentials.username)
    PerformanceMonitor.startTimer('login_request')

    try {
      const response = await fetch('http://localhost:8002/api/auth/login', {
        method: 'POST',
        mode: 'cors',
        credentials: 'include',
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json',
        },
        body: JSON.stringify(credentials),
      })

      const duration = PerformanceMonitor.endTimer('login_request')

      if (response.ok) {
        const data: AuthResponse = await response.json()
        
        logger.authLoginSuccess(credentials.username)
        logger.info(`Login successful in ${duration.toFixed(2)}ms`, 'LoginPage', {
          user: data.user.username,
          role: data.user.role
        })

        // Store authentication data
        localStorage.setItem('authToken', data.access_token)
        localStorage.setItem('user', JSON.stringify(data.user))
        
        // Handle remember me
        if (state.rememberMe) {
          localStorage.setItem('rememberedUsername', credentials.username)
        } else {
          localStorage.removeItem('rememberedUsername')
        }

        updateMessage(`Welcome back, ${data.user.username}!`, 'success')
        
        // Call success callback
        if (onLoginSuccess) {
          setTimeout(() => onLoginSuccess(data.user), 1000)
        }
        
      } else {
        const errorData = await response.json().catch(() => ({ detail: 'Login failed' }))
        let userFriendlyMessage = ''

        // Handle specific error cases
        if (response.status === 401) {
          userFriendlyMessage = '🔐 Invalid username or password. Please check your credentials and try again.'
        } else if (response.status === 500) {
          userFriendlyMessage = '🔧 Server error. Please try again in a moment.'
        } else if (response.status === 503) {
          userFriendlyMessage = '🔌 Service temporarily unavailable. Please try again later.'
        } else {
          userFriendlyMessage = errorData.detail || errorData.message || 'Login failed. Please try again.'
        }

        logger.authLoginFailed(credentials.username, errorData.detail || errorData.message)
        updateMessage(userFriendlyMessage, 'error')
      }
      
    } catch (error) {
      const handledError = ErrorHandler.handleApiError(error, 'LoginPage')
      logger.authLoginFailed(credentials.username, handledError.message)

      // User-friendly connection error message
      const connectionMessage = '🌐 Unable to connect to the server. Please check your internet connection and try again.'
      updateMessage(connectionMessage, 'error')
    } finally {
      setState(prev => ({ ...prev, isLoading: false }))
    }
  }

  const handleInputChange = (field: keyof LoginCredentials, value: string) => {
    setCredentials(prev => ({ ...prev, [field]: value }))
    // Clear error message when user starts typing
    if (state.message && state.messageType === 'error') {
      setState(prev => ({ ...prev, message: '' }))
    }
  }

  const togglePasswordVisibility = () => {
    setState(prev => ({ ...prev, showPassword: !prev.showPassword }))
  }

  const toggleRememberMe = () => {
    setState(prev => ({ ...prev, rememberMe: !prev.rememberMe }))
  }

  return (
    <div style={{
      minHeight: '100vh',
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
      background: 'linear-gradient(135deg, #0f0f0f 0%, #1a0a0a 50%, #0f0f0f 100%)',
      padding: '20px',
      position: 'relative',
      overflow: 'hidden'
    }}>
      {/* Animated background elements */}
      <div style={{
        position: 'absolute',
        top: '10%',
        left: '10%',
        width: '200px',
        height: '200px',
        background: 'radial-gradient(circle, rgba(239, 68, 68, 0.08) 0%, transparent 70%)',
        borderRadius: '50%',
        animation: 'float 8s ease-in-out infinite'
      }} />
      <div style={{
        position: 'absolute',
        bottom: '10%',
        right: '10%',
        width: '150px',
        height: '150px',
        background: 'radial-gradient(circle, rgba(239, 68, 68, 0.05) 0%, transparent 70%)',
        borderRadius: '50%',
        animation: 'float 6s ease-in-out infinite reverse'
      }} />

      <div style={{
        width: '100%',
        maxWidth: '450px',
        backgroundColor: '#1a1a1a',
        borderRadius: '24px',
        boxShadow: `
          20px 20px 60px #0a0a0a,
          -20px -20px 60px #2a2a2a,
          inset 2px 2px 10px rgba(239, 68, 68, 0.1),
          inset -2px -2px 10px rgba(0, 0, 0, 0.5)
        `,
        padding: '48px',
        position: 'relative',
        overflow: 'hidden',
        border: '1px solid rgba(239, 68, 68, 0.1)'
      }}>
        {/* Header */}
        <div style={{ textAlign: 'center', marginBottom: '40px' }}>
          <div style={{
            width: '100px',
            height: '100px',
            background: `
              linear-gradient(145deg, #2a2a2a, #0f0f0f),
              radial-gradient(circle at 30% 30%, rgba(239, 68, 68, 0.3), transparent)
            `,
            borderRadius: '50%',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            margin: '0 auto 24px',
            fontSize: '40px',
            boxShadow: `
              10px 10px 20px #0a0a0a,
              -10px -10px 20px #2a2a2a,
              inset 2px 2px 5px rgba(239, 68, 68, 0.1)
            `,
            border: '1px solid rgba(239, 68, 68, 0.2)'
          }}>
            🔐
          </div>
          <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'center', gap: '12px', marginBottom: '12px' }}>
            <img
              src="/logo.svg"
              alt="CTNL WORK-BOARD"
              style={{ height: '40px', width: 'auto' }}
            />
          </div>
          <h1 style={{
            fontSize: '32px',
            fontWeight: 'bold',
            background: 'linear-gradient(135deg, #8B0000, #DC143C)',
            WebkitBackgroundClip: 'text',
            WebkitTextFillColor: 'transparent',
            margin: '0 0 12px 0',
            textShadow: '0 0 20px rgba(139, 0, 0, 0.5)',
            textAlign: 'center'
          }}>
            Welcome to CTNL WORK-BOARD
          </h1>
          <p style={{
            color: '#9ca3af',
            fontSize: '16px',
            margin: 0,
            opacity: 0.8,
            textAlign: 'center'
          }}>
            Sign in to your AI-powered work management system
          </p>
        </div>

        {/* Form */}
        <form onSubmit={handleSubmit} style={{ marginBottom: '24px' }}>
          {/* Username Field */}
          <div style={{ marginBottom: '24px' }}>
            <label style={{
              display: 'block',
              fontSize: '14px',
              fontWeight: '500',
              color: '#e5e7eb',
              marginBottom: '8px',
              textShadow: '0 0 10px rgba(239, 68, 68, 0.3)'
            }}>
              Username or Email
            </label>
            <input
              type="text"
              value={credentials.username}
              onChange={(e) => handleInputChange('username', e.target.value)}
              placeholder="Enter your username or email"
              disabled={state.isLoading}
              style={{
                width: '100%',
                padding: '16px 20px',
                border: 'none',
                borderRadius: '12px',
                fontSize: '16px',
                transition: 'all 0.3s ease',
                backgroundColor: '#0f0f0f',
                color: '#ffffff',
                boxSizing: 'border-box',
                boxShadow: `
                  inset 8px 8px 16px #0a0a0a,
                  inset -8px -8px 16px #1a1a1a,
                  0 0 0 1px rgba(239, 68, 68, 0.1)
                `,
                outline: 'none'
              }}
              onFocus={(e) => {
                e.target.style.boxShadow = `
                  inset 8px 8px 16px #0a0a0a,
                  inset -8px -8px 16px #1a1a1a,
                  0 0 0 2px rgba(239, 68, 68, 0.3),
                  0 0 20px rgba(239, 68, 68, 0.1)
                `
              }}
              onBlur={(e) => {
                e.target.style.boxShadow = `
                  inset 8px 8px 16px #0a0a0a,
                  inset -8px -8px 16px #1a1a1a,
                  0 0 0 1px rgba(239, 68, 68, 0.1)
                `
              }}
            />
          </div>

          {/* Password Field */}
          <div style={{ marginBottom: '24px' }}>
            <label style={{
              display: 'block',
              fontSize: '14px',
              fontWeight: '500',
              color: '#e5e7eb',
              marginBottom: '8px',
              textShadow: '0 0 10px rgba(239, 68, 68, 0.3)'
            }}>
              Password
            </label>
            <div style={{ position: 'relative' }}>
              <input
                type={state.showPassword ? 'text' : 'password'}
                value={credentials.password}
                onChange={(e) => handleInputChange('password', e.target.value)}
                placeholder="Enter your password"
                disabled={state.isLoading}
                style={{
                  width: '100%',
                  padding: '16px 60px 16px 20px',
                  border: 'none',
                  borderRadius: '12px',
                  fontSize: '16px',
                  transition: 'all 0.3s ease',
                  backgroundColor: '#0f0f0f',
                  color: '#ffffff',
                  boxSizing: 'border-box',
                  boxShadow: `
                    inset 8px 8px 16px #0a0a0a,
                    inset -8px -8px 16px #1a1a1a,
                    0 0 0 1px rgba(239, 68, 68, 0.1)
                  `,
                  outline: 'none'
                }}
                onFocus={(e) => {
                  e.target.style.boxShadow = `
                    inset 8px 8px 16px #0a0a0a,
                    inset -8px -8px 16px #1a1a1a,
                    0 0 0 2px rgba(239, 68, 68, 0.3),
                    0 0 20px rgba(239, 68, 68, 0.1)
                  `
                }}
                onBlur={(e) => {
                  e.target.style.boxShadow = `
                    inset 8px 8px 16px #0a0a0a,
                    inset -8px -8px 16px #1a1a1a,
                    0 0 0 1px rgba(239, 68, 68, 0.1)
                  `
                }}
              />
              <button
                type="button"
                onClick={togglePasswordVisibility}
                disabled={state.isLoading}
                style={{
                  position: 'absolute',
                  right: '16px',
                  top: '50%',
                  transform: 'translateY(-50%)',
                  background: 'linear-gradient(145deg, #2a2a2a, #0f0f0f)',
                  border: 'none',
                  borderRadius: '8px',
                  width: '36px',
                  height: '36px',
                  fontSize: '16px',
                  cursor: state.isLoading ? 'not-allowed' : 'pointer',
                  color: '#ef4444',
                  boxShadow: `
                    4px 4px 8px #0a0a0a,
                    -4px -4px 8px #2a2a2a
                  `,
                  transition: 'all 0.2s ease'
                }}
                onMouseEnter={(e) => {
                  if (!state.isLoading) {
                    e.currentTarget.style.boxShadow = `
                      2px 2px 4px #0a0a0a,
                      -2px -2px 4px #2a2a2a,
                      0 0 10px rgba(239, 68, 68, 0.3)
                    `
                  }
                }}
                onMouseLeave={(e) => {
                  e.currentTarget.style.boxShadow = `
                    4px 4px 8px #0a0a0a,
                    -4px -4px 8px #2a2a2a
                  `
                }}
              >
                {state.showPassword ? '🙈' : '👁️'}
              </button>
            </div>
          </div>

          {/* Remember Me */}
          <div style={{
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'space-between',
            marginBottom: '32px'
          }}>
            <label style={{
              display: 'flex',
              alignItems: 'center',
              cursor: 'pointer',
              fontSize: '14px',
              color: '#e5e7eb'
            }}>
              <div style={{
                position: 'relative',
                marginRight: '12px'
              }}>
                <input
                  type="checkbox"
                  checked={state.rememberMe}
                  onChange={toggleRememberMe}
                  disabled={state.isLoading}
                  style={{
                    appearance: 'none',
                    width: '20px',
                    height: '20px',
                    borderRadius: '4px',
                    backgroundColor: '#0f0f0f',
                    boxShadow: `
                      inset 4px 4px 8px #0a0a0a,
                      inset -4px -4px 8px #1a1a1a,
                      0 0 0 1px rgba(239, 68, 68, 0.2)
                    `,
                    cursor: state.isLoading ? 'not-allowed' : 'pointer',
                    transition: 'all 0.2s ease'
                  }}
                />
                {state.rememberMe && (
                  <div style={{
                    position: 'absolute',
                    top: '50%',
                    left: '50%',
                    transform: 'translate(-50%, -50%)',
                    color: '#ef4444',
                    fontSize: '12px',
                    pointerEvents: 'none'
                  }}>
                    ✓
                  </div>
                )}
              </div>
              Remember me
            </label>
            <button
              type="button"
              style={{
                background: 'none',
                border: 'none',
                color: '#ef4444',
                fontSize: '14px',
                cursor: 'pointer',
                textDecoration: 'none',
                transition: 'all 0.2s ease',
                textShadow: '0 0 10px rgba(239, 68, 68, 0.3)'
              }}
              onMouseEnter={(e) => {
                e.currentTarget.style.textDecoration = 'underline'
                e.currentTarget.style.textShadow = '0 0 15px rgba(239, 68, 68, 0.5)'
              }}
              onMouseLeave={(e) => {
                e.currentTarget.style.textDecoration = 'none'
                e.currentTarget.style.textShadow = '0 0 10px rgba(239, 68, 68, 0.3)'
              }}
            >
              Forgot password?
            </button>
          </div>

          {/* Submit Button */}
          <button
            type="submit"
            disabled={state.isLoading}
            style={{
              width: '100%',
              padding: '18px',
              background: state.isLoading
                ? 'linear-gradient(145deg, #2a2a2a, #0f0f0f)'
                : 'linear-gradient(145deg, #ef4444, #dc2626)',
              color: state.isLoading ? '#6b7280' : '#ffffff',
              border: 'none',
              borderRadius: '16px',
              fontSize: '18px',
              fontWeight: '700',
              cursor: state.isLoading ? 'not-allowed' : 'pointer',
              transition: 'all 0.3s ease',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              gap: '12px',
              boxShadow: state.isLoading
                ? `
                  inset 4px 4px 8px #0a0a0a,
                  inset -4px -4px 8px #2a2a2a
                `
                : `
                  8px 8px 16px #0a0a0a,
                  -8px -8px 16px #2a2a2a,
                  0 0 20px rgba(239, 68, 68, 0.3),
                  inset 1px 1px 2px rgba(255, 255, 255, 0.1)
                `,
              textShadow: state.isLoading ? 'none' : '0 0 10px rgba(255, 255, 255, 0.3)',
              position: 'relative',
              overflow: 'hidden'
            }}
            onMouseEnter={(e) => {
              if (!state.isLoading) {
                e.currentTarget.style.transform = 'translateY(-2px)'
                e.currentTarget.style.boxShadow = `
                  12px 12px 24px #0a0a0a,
                  -12px -12px 24px #2a2a2a,
                  0 0 30px rgba(239, 68, 68, 0.4),
                  inset 1px 1px 2px rgba(255, 255, 255, 0.2)
                `
              }
            }}
            onMouseLeave={(e) => {
              if (!state.isLoading) {
                e.currentTarget.style.transform = 'translateY(0)'
                e.currentTarget.style.boxShadow = `
                  8px 8px 16px #0a0a0a,
                  -8px -8px 16px #2a2a2a,
                  0 0 20px rgba(239, 68, 68, 0.3),
                  inset 1px 1px 2px rgba(255, 255, 255, 0.1)
                `
              }
            }}
            onMouseDown={(e) => {
              if (!state.isLoading) {
                e.currentTarget.style.transform = 'translateY(1px)'
                e.currentTarget.style.boxShadow = `
                  4px 4px 8px #0a0a0a,
                  -4px -4px 8px #2a2a2a,
                  0 0 15px rgba(239, 68, 68, 0.2),
                  inset 2px 2px 4px rgba(0, 0, 0, 0.3)
                `
              }
            }}
            onMouseUp={(e) => {
              if (!state.isLoading) {
                e.currentTarget.style.transform = 'translateY(-2px)'
                e.currentTarget.style.boxShadow = `
                  12px 12px 24px #0a0a0a,
                  -12px -12px 24px #2a2a2a,
                  0 0 30px rgba(239, 68, 68, 0.4),
                  inset 1px 1px 2px rgba(255, 255, 255, 0.2)
                `
              }
            }}
          >
            {state.isLoading && (
              <div style={{
                width: '24px',
                height: '24px',
                border: '3px solid transparent',
                borderTop: '3px solid #ef4444',
                borderRadius: '50%',
                animation: 'spin 1s linear infinite'
              }} />
            )}
            {state.isLoading ? 'Signing in...' : '🔐 Sign In'}
          </button>
        </form>

        {/* Enhanced Message Display */}
        {state.message && (
          <div style={{
            padding: '16px 20px',
            borderRadius: '12px',
            marginBottom: '24px',
            fontSize: '16px',
            fontWeight: '600',
            textAlign: 'center',
            position: 'relative',
            backgroundColor:
              state.messageType === 'success' ? 'rgba(34, 197, 94, 0.1)' :
              state.messageType === 'error' ? 'rgba(239, 68, 68, 0.1)' : 'rgba(59, 130, 246, 0.1)',
            color:
              state.messageType === 'success' ? '#22c55e' :
              state.messageType === 'error' ? '#ef4444' : '#3b82f6',
            border: `2px solid ${
              state.messageType === 'success' ? '#22c55e' :
              state.messageType === 'error' ? '#ef4444' : '#3b82f6'
            }`,
            boxShadow: `0 4px 12px ${
              state.messageType === 'success' ? 'rgba(34, 197, 94, 0.2)' :
              state.messageType === 'error' ? 'rgba(239, 68, 68, 0.2)' : 'rgba(59, 130, 246, 0.2)'
            }`,
            animation: state.messageType === 'error'
              ? 'fadeInSlide 0.3s ease-out, shake 0.5s ease-in-out 0.3s'
              : 'fadeInSlide 0.3s ease-out'
          }}>
            <div style={{
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              gap: '8px'
            }}>
              <span style={{ fontSize: '20px' }}>
                {state.messageType === 'success' ? '✅' :
                 state.messageType === 'error' ? '❌' : 'ℹ️'}
              </span>
              {state.message}
            </div>

            {/* Close button for error messages */}
            {state.messageType === 'error' && (
              <button
                type="button"
                onClick={() => setState(prev => ({ ...prev, message: '' }))}
                style={{
                  position: 'absolute',
                  top: '8px',
                  right: '8px',
                  background: 'none',
                  border: 'none',
                  color: '#ef4444',
                  fontSize: '18px',
                  cursor: 'pointer',
                  padding: '4px',
                  borderRadius: '4px',
                  lineHeight: 1
                }}
                onMouseEnter={(e) => {
                  e.currentTarget.style.backgroundColor = 'rgba(239, 68, 68, 0.1)'
                }}
                onMouseLeave={(e) => {
                  e.currentTarget.style.backgroundColor = 'transparent'
                }}
              >
                ×
              </button>
            )}
          </div>
        )}

        {/* Navigation Links */}
        <div style={{ textAlign: 'center' }}>
          <p style={{
            color: '#9ca3af',
            fontSize: '14px',
            margin: '0 0 20px 0',
            opacity: 0.8
          }}>
            Don't have an account?{' '}
            <button
              type="button"
              onClick={onNavigateToSignup}
              style={{
                background: 'linear-gradient(145deg, #2a2a2a, #0f0f0f)',
                border: 'none',
                color: '#ef4444',
                fontSize: '14px',
                cursor: 'pointer',
                textDecoration: 'none',
                fontWeight: '600',
                padding: '8px 16px',
                borderRadius: '8px',
                boxShadow: `
                  4px 4px 8px #0a0a0a,
                  -4px -4px 8px #2a2a2a
                `,
                transition: 'all 0.2s ease',
                textShadow: '0 0 10px rgba(239, 68, 68, 0.3)'
              }}
              onMouseEnter={(e) => {
                e.currentTarget.style.transform = 'translateY(-1px)'
                e.currentTarget.style.boxShadow = `
                  6px 6px 12px #0a0a0a,
                  -6px -6px 12px #2a2a2a,
                  0 0 15px rgba(239, 68, 68, 0.2)
                `
                e.currentTarget.style.textShadow = '0 0 15px rgba(239, 68, 68, 0.5)'
              }}
              onMouseLeave={(e) => {
                e.currentTarget.style.transform = 'translateY(0)'
                e.currentTarget.style.boxShadow = `
                  4px 4px 8px #0a0a0a,
                  -4px -4px 8px #2a2a2a
                `
                e.currentTarget.style.textShadow = '0 0 10px rgba(239, 68, 68, 0.3)'
              }}
            >
              Sign up here
            </button>
          </p>
          
          {onNavigateToClockIn && (
            <button
              type="button"
              onClick={onNavigateToClockIn}
              style={{
                background: 'linear-gradient(145deg, #2a2a2a, #0f0f0f)',
                border: '1px solid rgba(239, 68, 68, 0.3)',
                color: '#9ca3af',
                fontSize: '14px',
                cursor: 'pointer',
                padding: '12px 20px',
                borderRadius: '10px',
                fontWeight: '500',
                transition: 'all 0.3s ease',
                boxShadow: `
                  4px 4px 8px #0a0a0a,
                  -4px -4px 8px #2a2a2a
                `,
                textShadow: '0 0 10px rgba(239, 68, 68, 0.2)'
              }}
              onMouseEnter={(e) => {
                e.currentTarget.style.background = 'linear-gradient(145deg, #ef4444, #dc2626)'
                e.currentTarget.style.color = '#ffffff'
                e.currentTarget.style.transform = 'translateY(-1px)'
                e.currentTarget.style.boxShadow = `
                  6px 6px 12px #0a0a0a,
                  -6px -6px 12px #2a2a2a,
                  0 0 20px rgba(239, 68, 68, 0.3)
                `
                e.currentTarget.style.textShadow = '0 0 15px rgba(255, 255, 255, 0.3)'
              }}
              onMouseLeave={(e) => {
                e.currentTarget.style.background = 'linear-gradient(145deg, #2a2a2a, #0f0f0f)'
                e.currentTarget.style.color = '#9ca3af'
                e.currentTarget.style.transform = 'translateY(0)'
                e.currentTarget.style.boxShadow = `
                  4px 4px 8px #0a0a0a,
                  -4px -4px 8px #2a2a2a
                `
                e.currentTarget.style.textShadow = '0 0 10px rgba(239, 68, 68, 0.2)'
              }}
            >
              ⏰ Quick Clock In
            </button>
          )}
        </div>
      </div>

      {/* CSS Animations */}
      <style>{`
        @keyframes spin {
          0% { transform: rotate(0deg); }
          100% { transform: rotate(360deg); }
        }

        @keyframes float {
          0%, 100% {
            transform: translateY(0px) rotate(0deg);
            opacity: 0.7;
          }
          50% {
            transform: translateY(-20px) rotate(180deg);
            opacity: 1;
          }
        }

        /* Neumorphic glow effect */
        @keyframes glow {
          0%, 100% {
            box-shadow: 0 0 20px rgba(239, 68, 68, 0.1);
          }
          50% {
            box-shadow: 0 0 40px rgba(239, 68, 68, 0.3);
          }
        }
      `}</style>
    </div>
  )
}
