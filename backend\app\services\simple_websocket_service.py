"""
Simple WebSocket Service using FastAPI's native WebSocket support
"""
import asyncio
import json
import logging
from typing import Dict, Any, List, Set
from datetime import datetime
from fastapi import WebSocket, WebSocketDisconnect
from motor.motor_asyncio import AsyncIOMotorDatabase
from bson import ObjectId

from ..core.database import get_database
from ..core.config import Collections

logger = logging.getLogger(__name__)

class SimpleWebSocketManager:
    def __init__(self):
        # Store active connections: {user_id: {websocket, user_data}}
        self.active_connections: Dict[str, Dict[str, Any]] = {}
        # Store room memberships: {room_name: set of user_ids}
        self.rooms: Dict[str, Set[str]] = {}
        
    async def connect(self, websocket: WebSocket, user_id: str, user_data: Dict[str, Any]):
        """Accept WebSocket connection and store user info"""
        await websocket.accept()
        
        # Store connection
        self.active_connections[user_id] = {
            'websocket': websocket,
            'user_data': user_data,
            'connected_at': datetime.utcnow()
        }
        
        # Update user online status in database
        try:
            db = await get_database()
            await db[Collections.USERS].update_one(
                {"_id": ObjectId(user_id)},
                {
                    "$set": {
                        "is_online": True,
                        "activity.last_active": datetime.utcnow()
                    }
                }
            )
        except Exception as e:
            logger.error(f"Failed to update user online status: {e}")
        
        # Join user to their personal room
        await self.join_room(user_id, f"user_{user_id}")
        
        # Join user to their department room if available
        if user_data.get('department'):
            await self.join_room(user_id, f"department_{user_data['department']}")
        
        # Send connection confirmation
        await self.send_personal_message(user_id, {
            'type': 'connection_established',
            'message': 'Connected successfully',
            'user_id': user_id,
            'timestamp': datetime.utcnow().isoformat()
        })
        
        logger.info(f"User {user_data.get('username', user_id)} connected via WebSocket")
    
    async def disconnect(self, user_id: str):
        """Handle user disconnection"""
        if user_id in self.active_connections:
            # Remove from all rooms
            rooms_to_leave = []
            for room_name, members in self.rooms.items():
                if user_id in members:
                    rooms_to_leave.append(room_name)
            
            for room_name in rooms_to_leave:
                await self.leave_room(user_id, room_name)
            
            # Update user offline status in database
            try:
                db = await get_database()
                await db[Collections.USERS].update_one(
                    {"_id": ObjectId(user_id)},
                    {
                        "$set": {
                            "is_online": False,
                            "activity.last_active": datetime.utcnow()
                        }
                    }
                )
            except Exception as e:
                logger.error(f"Failed to update user offline status: {e}")
            
            # Remove connection
            user_data = self.active_connections[user_id]['user_data']
            del self.active_connections[user_id]
            
            logger.info(f"User {user_data.get('username', user_id)} disconnected from WebSocket")
    
    async def join_room(self, user_id: str, room_name: str):
        """Add user to a room"""
        if room_name not in self.rooms:
            self.rooms[room_name] = set()
        
        self.rooms[room_name].add(user_id)
        
        # Notify user they joined the room
        await self.send_personal_message(user_id, {
            'type': 'room_joined',
            'room': room_name,
            'message': f'Joined room {room_name}',
            'timestamp': datetime.utcnow().isoformat()
        })
    
    async def leave_room(self, user_id: str, room_name: str):
        """Remove user from a room"""
        if room_name in self.rooms and user_id in self.rooms[room_name]:
            self.rooms[room_name].remove(user_id)
            
            # Clean up empty rooms
            if not self.rooms[room_name]:
                del self.rooms[room_name]
            
            # Notify user they left the room
            await self.send_personal_message(user_id, {
                'type': 'room_left',
                'room': room_name,
                'message': f'Left room {room_name}',
                'timestamp': datetime.utcnow().isoformat()
            })
    
    async def send_personal_message(self, user_id: str, message: Dict[str, Any]):
        """Send message to a specific user"""
        if user_id in self.active_connections:
            try:
                websocket = self.active_connections[user_id]['websocket']
                await websocket.send_text(json.dumps(message))
            except Exception as e:
                logger.error(f"Failed to send message to user {user_id}: {e}")
                # Remove broken connection
                await self.disconnect(user_id)
    
    async def broadcast_to_room(self, room_name: str, message: Dict[str, Any], exclude_user: str = None):
        """Broadcast message to all users in a room"""
        if room_name in self.rooms:
            for user_id in self.rooms[room_name].copy():  # Use copy to avoid modification during iteration
                if exclude_user and user_id == exclude_user:
                    continue
                await self.send_personal_message(user_id, message)
    
    async def broadcast_to_all(self, message: Dict[str, Any]):
        """Broadcast message to all connected users"""
        for user_id in list(self.active_connections.keys()):  # Use list to avoid modification during iteration
            await self.send_personal_message(user_id, message)
    
    async def handle_message(self, user_id: str, message: Dict[str, Any]):
        """Handle incoming WebSocket message"""
        try:
            message_type = message.get('type')
            
            if message_type == 'ping':
                await self.send_personal_message(user_id, {
                    'type': 'pong',
                    'message': 'pong',
                    'timestamp': message.get('timestamp'),
                    'server_time': datetime.utcnow().isoformat()
                })
            
            elif message_type == 'join_room':
                room = message.get('room')
                if room:
                    await self.join_room(user_id, room)
            
            elif message_type == 'leave_room':
                room = message.get('room')
                if room:
                    await self.leave_room(user_id, room)
            
            elif message_type == 'user_typing':
                room = message.get('room')
                if room and user_id in self.active_connections:
                    user_data = self.active_connections[user_id]['user_data']
                    await self.broadcast_to_room(room, {
                        'type': 'user_typing',
                        'user_id': user_id,
                        'username': user_data.get('username'),
                        'room': room,
                        'typing': message.get('typing', True)
                    }, exclude_user=user_id)
            
            elif message_type == 'update_status':
                status = message.get('status')
                if status and user_id in self.active_connections:
                    self.active_connections[user_id]['user_data']['status'] = status
                    user_data = self.active_connections[user_id]['user_data']
                    
                    # Broadcast status update to department room
                    department = user_data.get('department')
                    if department:
                        await self.broadcast_to_room(f"department_{department}", {
                            'type': 'user_status_update',
                            'user_id': user_id,
                            'username': user_data.get('username'),
                            'status': status,
                            'timestamp': datetime.utcnow().isoformat()
                        })
            
            else:
                logger.warning(f"Unknown message type: {message_type}")
                
        except Exception as e:
            logger.error(f"Error handling WebSocket message: {e}")
    
    # Broadcasting methods for API integration
    async def broadcast_task_update(self, task_data: Dict[str, Any]):
        """Broadcast task updates to relevant users"""
        try:
            # Broadcast to project room if task has project_id
            if task_data.get('project_id'):
                await self.broadcast_to_room(f"project_{task_data['project_id']}", {
                    'type': 'task_update',
                    'data': task_data
                })
            
            # Broadcast to assignee
            if task_data.get('assignee_id'):
                await self.send_personal_message(task_data['assignee_id'], {
                    'type': 'task_update',
                    'data': task_data
                })
            
            # Broadcast to creator
            if task_data.get('created_by'):
                await self.send_personal_message(task_data['created_by'], {
                    'type': 'task_update',
                    'data': task_data
                })
                
        except Exception as e:
            logger.error(f"Task broadcast error: {e}")
    
    async def broadcast_project_update(self, project_data: Dict[str, Any]):
        """Broadcast project updates to relevant users"""
        try:
            project_id = project_data.get('id') or project_data.get('_id')
            if project_id:
                await self.broadcast_to_room(f"project_{project_id}", {
                    'type': 'project_update',
                    'data': project_data
                })
                
        except Exception as e:
            logger.error(f"Project broadcast error: {e}")
    
    async def broadcast_notification(self, notification_data: Dict[str, Any], target_users: List[str] = None):
        """Broadcast notifications to specific users or all connected users"""
        try:
            message = {
                'type': 'notification',
                'data': notification_data
            }
            
            if target_users:
                # Send to specific users
                for user_id in target_users:
                    await self.send_personal_message(user_id, message)
            else:
                # Broadcast to all connected users
                await self.broadcast_to_all(message)
                
        except Exception as e:
            logger.error(f"Notification broadcast error: {e}")

# Global WebSocket manager instance
websocket_manager = SimpleWebSocketManager()

# Helper functions for API integration
async def broadcast_task_update(task_data: Dict[str, Any]):
    """Helper function to broadcast task updates"""
    await websocket_manager.broadcast_task_update(task_data)

async def broadcast_project_update(project_data: Dict[str, Any]):
    """Helper function to broadcast project updates"""
    await websocket_manager.broadcast_project_update(project_data)

async def broadcast_notification(notification_data: Dict[str, Any], target_users: List[str] = None):
    """Helper function to broadcast notifications"""
    await websocket_manager.broadcast_notification(notification_data, target_users)
