#!/usr/bin/env python3
"""
Create demo users for testing
"""

import asyncio
import sys
import os
from datetime import datetime
from passlib.context import CryptContext

# Add the app directory to the path
sys.path.append(os.path.join(os.path.dirname(__file__), 'app'))

from app.core.database_simple import connect_to_mongo, mongodb, close_mongo_connection
from app.models.user import User, UserProfile

# Password hashing
pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")

async def create_demo_users():
    """Create demo users for testing"""
    print("🚀 Creating Demo Users...")
    
    # Connect to database
    success = await connect_to_mongo()
    if not success:
        print("❌ Failed to connect to database")
        return False
    
    try:
        # Demo users data
        demo_users = [
            {
                "username": "admin",
                "email": "<EMAIL>",
                "password": "admin123",
                "role": "admin",
                "profile": {
                    "first_name": "Admin",
                    "last_name": "User",
                    "phone": "+1234567890"
                }
            },
            {
                "username": "manager",
                "email": "<EMAIL>", 
                "password": "manager123",
                "role": "manager",
                "profile": {
                    "first_name": "Manager",
                    "last_name": "User",
                    "phone": "+**********"
                }
            },
            {
                "username": "staff",
                "email": "<EMAIL>",
                "password": "staff123", 
                "role": "staff",
                "profile": {
                    "first_name": "Staff",
                    "last_name": "User",
                    "phone": "+**********"
                }
            },
            {
                "username": "accountant",
                "email": "<EMAIL>",
                "password": "accountant123",
                "role": "accountant", 
                "profile": {
                    "first_name": "Accountant",
                    "last_name": "User",
                    "phone": "+**********"
                }
            }
        ]
        
        users_collection = mongodb.database.users
        created_count = 0
        
        for user_data in demo_users:
            # Check if user already exists
            existing_user = await users_collection.find_one({
                "$or": [
                    {"username": user_data["username"]},
                    {"email": user_data["email"]}
                ]
            })
            
            if existing_user:
                print(f"⚠️  User {user_data['username']} already exists, skipping...")
                continue
            
            # Hash password
            hashed_password = pwd_context.hash(user_data["password"])
            
            # Create user document
            user_doc = {
                "username": user_data["username"],
                "email": user_data["email"],
                "hashed_password": hashed_password,
                "profile": user_data["profile"],
                "role": user_data["role"],
                "is_active": True,
                "is_verified": True,
                "created_at": datetime.utcnow(),
                "updated_at": datetime.utcnow(),
                "employee_id": f"EMP{1000 + created_count}",
                "department": "General",
                "permissions": ["read", "write"] if user_data["role"] != "admin" else ["read", "write", "admin"]
            }
            
            # Insert user
            result = await users_collection.insert_one(user_doc)
            print(f"✅ Created user: {user_data['username']} (ID: {result.inserted_id})")
            created_count += 1
        
        print(f"\n🎉 Successfully created {created_count} demo users!")
        print("\n📋 Demo Login Credentials:")
        print("=" * 50)
        for user_data in demo_users:
            print(f"👤 {user_data['role'].upper()}")
            print(f"   Username: {user_data['username']}")
            print(f"   Password: {user_data['password']}")
            print(f"   Email: {user_data['email']}")
            print()
        
        return True
        
    except Exception as e:
        print(f"❌ Error creating demo users: {e}")
        return False
    
    finally:
        await close_mongo_connection()

async def main():
    """Main function"""
    print("🎯 CTNL AI Work Management System")
    print("🔧 Demo User Creation Script")
    print("=" * 50)
    
    success = await create_demo_users()
    
    if success:
        print("✅ Demo users created successfully!")
        print("🌐 You can now login at: http://localhost:3000")
    else:
        print("❌ Failed to create demo users")

if __name__ == "__main__":
    asyncio.run(main())
