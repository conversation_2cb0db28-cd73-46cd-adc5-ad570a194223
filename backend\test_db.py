#!/usr/bin/env python3
"""
Database Connection Test Script
Tests MongoDB Atlas connection and basic operations
"""

import asyncio
import sys
import os
from datetime import datetime

# Add the app directory to the path
sys.path.append(os.path.join(os.path.dirname(__file__), 'app'))

from app.core.database import connect_to_mongo, close_mongo_connection, mongodb
from app.core.config import settings
from app.models.user import User, UserProfile
from app.schemas.user import UserCreate

async def test_database_connection():
    """Test database connection and basic operations"""
    print("🧪 TESTING DATABASE CONNECTION")
    print("=" * 50)
    
    try:
        # Test connection
        print("1. Testing MongoDB connection...")
        await connect_to_mongo()
        
        if mongodb.database is None:
            print("   ⚠️  Using in-memory storage (MongoDB not connected)")
            print("   ✅ In-memory storage is working")
        else:
            print("   ✅ Connected to MongoDB Atlas successfully!")
            print(f"   📊 Database: {settings.DATABASE_NAME}")
        
        # Test basic operations
        print("\n2. Testing basic database operations...")
        
        # Create a test user
        test_user_data = UserCreate(
            username="testuser",
            email="<EMAIL>",
            password="testpassword123",
            profile=UserProfile(
                first_name="Test",
                last_name="User",
                phone="+1234567890"
            ),
            role="staff"
        )
        
        print("   📝 Creating test user...")
        
        # In a real scenario, you'd use the user service
        # For now, just test the data structure
        test_user = User(
            username=test_user_data.username,
            email=test_user_data.email,
            hashed_password="hashed_password_here",
            profile=test_user_data.profile,
            role=test_user_data.role,
            is_active=True,
            is_verified=True,
            created_at=datetime.utcnow(),
            updated_at=datetime.utcnow()
        )
        
        print("   ✅ Test user data structure created successfully")
        print(f"   👤 User: {test_user.username} ({test_user.email})")
        print(f"   🏷️  Role: {test_user.role}")
        
        # Test serialization
        print("\n3. Testing data serialization...")
        user_dict = test_user.dict()
        print("   ✅ User data serialized successfully")
        print(f"   📦 Serialized fields: {len(user_dict)} fields")
        
        print("\n4. Testing configuration...")
        print(f"   🔧 MongoDB URL: {settings.MONGODB_URL[:50]}...")
        print(f"   🗄️  Database Name: {settings.DATABASE_NAME}")
        print(f"   🔐 JWT Secret: {'*' * 20}")
        print(f"   🤖 OpenAI API Key: {'Set' if settings.OPENAI_API_KEY != 'your-openai-api-key-here' else 'Not Set'}")
        
        print("\n" + "=" * 50)
        print("🎉 DATABASE TEST COMPLETED SUCCESSFULLY!")
        
        if mongodb.database is None:
            print("\n📝 NOTE: Currently using in-memory storage")
            print("   • Data will persist during the session")
            print("   • Data will reset when server restarts")
            print("   • Perfect for development and testing")
        else:
            print("\n📝 NOTE: Connected to MongoDB Atlas")
            print("   • Data will persist permanently")
            print("   • Suitable for production use")
        
        return True
        
    except Exception as e:
        print(f"\n❌ DATABASE TEST FAILED: {e}")
        return False
    
    finally:
        # Clean up
        await close_mongo_connection()

async def test_api_endpoints():
    """Test API endpoints"""
    print("\n🌐 TESTING API ENDPOINTS")
    print("=" * 50)
    
    import aiohttp
    
    base_url = "http://localhost:8000"
    
    try:
        async with aiohttp.ClientSession() as session:
            # Test health endpoint
            print("1. Testing health endpoint...")
            async with session.get(f"{base_url}/health") as response:
                if response.status == 200:
                    data = await response.json()
                    print("   ✅ Health endpoint working")
                    print(f"   📊 Status: {data.get('status')}")
                    print(f"   🕐 Timestamp: {data.get('timestamp')}")
                    print(f"   📦 Version: {data.get('version')}")
                else:
                    print(f"   ❌ Health endpoint failed: {response.status}")
            
            # Test API docs
            print("\n2. Testing API documentation...")
            async with session.get(f"{base_url}/docs") as response:
                if response.status == 200:
                    print("   ✅ API documentation accessible")
                    print(f"   🔗 Available at: {base_url}/docs")
                else:
                    print(f"   ❌ API docs failed: {response.status}")
            
            # Test OpenAPI spec
            print("\n3. Testing OpenAPI specification...")
            async with session.get(f"{base_url}/openapi.json") as response:
                if response.status == 200:
                    spec = await response.json()
                    print("   ✅ OpenAPI spec accessible")
                    print(f"   📋 Title: {spec.get('info', {}).get('title')}")
                    print(f"   📦 Version: {spec.get('info', {}).get('version')}")
                    print(f"   🛣️  Paths: {len(spec.get('paths', {}))}")
                else:
                    print(f"   ❌ OpenAPI spec failed: {response.status}")
        
        print("\n🎉 API ENDPOINTS TEST COMPLETED!")
        return True
        
    except Exception as e:
        print(f"\n❌ API ENDPOINTS TEST FAILED: {e}")
        return False

async def main():
    """Main test function"""
    print("🚀 CTNL AI WORK MANAGEMENT SYSTEM")
    print("🧪 DATABASE & API CONNECTION TEST")
    print("=" * 60)
    
    # Test database
    db_success = await test_database_connection()
    
    # Test API endpoints
    api_success = await test_api_endpoints()
    
    print("\n" + "=" * 60)
    print("📊 TEST SUMMARY")
    print("=" * 60)
    print(f"Database Test: {'✅ PASSED' if db_success else '❌ FAILED'}")
    print(f"API Test:      {'✅ PASSED' if api_success else '❌ FAILED'}")
    
    if db_success and api_success:
        print("\n🎉 ALL TESTS PASSED! Your system is ready to use!")
        print("\n🌐 Access your application:")
        print("   • Frontend: http://localhost:3001")
        print("   • Backend:  http://localhost:8000")
        print("   • API Docs: http://localhost:8000/docs")
    else:
        print("\n⚠️  Some tests failed. Check the output above for details.")
    
    print("=" * 60)

if __name__ == "__main__":
    asyncio.run(main())
