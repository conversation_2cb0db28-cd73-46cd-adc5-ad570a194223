/**
 * Memo System Page Component
 * Features: Create memos, approval workflow, role-based access control
 */
import React, { useState, useEffect, useCallback } from 'react'
import { logger, <PERSON>rrorHandler, PerformanceMonitor } from '../utils/logger'
import type { User, Memo } from '../types'

interface MemosPageProps {
  user: User
  onNavigateBack?: () => void
}

interface MemosState {
  isLoading: boolean
  memos: Memo[]
  filteredMemos: Memo[]
  showCreateModal: boolean
  showViewModal: boolean
  selectedMemo: Memo | null
  newMemo: {
    title: string
    content: string
    type: 'announcement' | 'approval_request' | 'general'
    priority: 'low' | 'medium' | 'high'
    recipients: 'all' | 'managers' | 'department'
    department?: string
  }
  filters: {
    type: string
    status: string
    priority: string
    search: string
  }
  error: string | null
}

const memoTypes = [
  { value: 'announcement', label: 'Announcement', description: 'General announcements to all users' },
  { value: 'approval_request', label: 'Approval Request', description: 'Request approval from managers' },
  { value: 'general', label: 'General Memo', description: 'General communication' }
]

const departments = [
  'General', 'Engineering', 'Marketing', 'Sales', 'HR', 'Finance', 
  'Operations', 'Customer Support', 'Design', 'Product', 'Legal'
]

export default function MemosPage({ user, onNavigateBack }: MemosPageProps) {
  const [state, setState] = useState<MemosState>({
    isLoading: true,
    memos: [],
    filteredMemos: [],
    showCreateModal: false,
    showViewModal: false,
    selectedMemo: null,
    newMemo: {
      title: '',
      content: '',
      type: 'general',
      priority: 'medium',
      recipients: 'all',
      department: user.department
    },
    filters: {
      type: 'all',
      status: 'all',
      priority: 'all',
      search: ''
    },
    error: null
  })

  useEffect(() => {
    logger.componentMount('MemosPage')
    loadMemosData()
    
    return () => {
      logger.componentUnmount('MemosPage')
    }
  }, [])

  useEffect(() => {
    // Apply filters
    applyFilters()
  }, [state.memos, state.filters])

  const loadMemosData = useCallback(async () => {
    try {
      setState(prev => ({ ...prev, isLoading: true, error: null }))
      logger.info('Loading memos data', 'MemosPage')
      
      PerformanceMonitor.startTimer('memos_load')

      // Determine endpoint based on user role
      const endpoint = (user.role === 'admin' || user.role === 'manager') 
        ? 'http://localhost:8002/api/memos/all'  // Managers and admins see all memos
        : 'http://localhost:8002/api/memos/my'   // Users see only their own memos

      const response = await fetch(endpoint, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('authToken')}`,
          'Content-Type': 'application/json'
        }
      })

      const duration = PerformanceMonitor.endTimer('memos_load')

      if (response.ok) {
        const data = await response.json()
        setState(prev => ({
          ...prev,
          isLoading: false,
          memos: data.memos || []
        }))

        logger.info(`Memos loaded in ${duration.toFixed(2)}ms`, 'MemosPage', {
          memoCount: data.memos?.length || 0,
          userRole: user.role
        })
      } else {
        // If endpoint doesn't exist, use mock data
        const mockMemos: Memo[] = [
          {
            id: '1',
            title: 'Welcome to the Team!',
            content: 'We are excited to have you join our team. Please review the employee handbook and complete your onboarding tasks.',
            type: 'announcement',
            priority: 'medium',
            status: 'approved',
            author_id: user.id,
            author_name: user.username,
            recipients: 'all',
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString()
          },
          {
            id: '2',
            title: 'Budget Approval Request',
            content: 'Requesting approval for Q4 marketing budget increase of $50,000 for digital advertising campaigns.',
            type: 'approval_request',
            priority: 'high',
            status: 'pending',
            author_id: user.id,
            author_name: user.username,
            recipients: 'managers',
            created_at: new Date(Date.now() - 86400000).toISOString(),
            updated_at: new Date(Date.now() - 86400000).toISOString()
          }
        ]

        setState(prev => ({
          ...prev,
          isLoading: false,
          memos: mockMemos
        }))

        logger.info('Using mock memos data', 'MemosPage')
      }
      
    } catch (error) {
      const handledError = ErrorHandler.handleApiError(error, 'MemosPage')
      setState(prev => ({ 
        ...prev, 
        isLoading: false, 
        error: handledError.message 
      }))
      logger.error('Failed to load memos data', 'MemosPage', error)
    }
  }, [user])

  const applyFilters = useCallback(() => {
    let filtered = [...state.memos]

    // Apply filters
    if (state.filters.type !== 'all') {
      filtered = filtered.filter(memo => memo.type === state.filters.type)
    }
    if (state.filters.status !== 'all') {
      filtered = filtered.filter(memo => memo.status === state.filters.status)
    }
    if (state.filters.priority !== 'all') {
      filtered = filtered.filter(memo => memo.priority === state.filters.priority)
    }
    if (state.filters.search) {
      const searchLower = state.filters.search.toLowerCase()
      filtered = filtered.filter(memo => 
        memo.title.toLowerCase().includes(searchLower) ||
        memo.content.toLowerCase().includes(searchLower)
      )
    }

    // Sort by creation date (newest first)
    filtered.sort((a, b) => new Date(b.created_at).getTime() - new Date(a.created_at).getTime())

    setState(prev => ({ ...prev, filteredMemos: filtered }))
  }, [state.memos, state.filters])

  const handleCreateMemo = async () => {
    if (!state.newMemo.title.trim() || !state.newMemo.content.trim()) {
      alert('Please enter both title and content')
      return
    }

    try {
      setState(prev => ({ ...prev, isLoading: true }))
      
      const memoData = {
        ...state.newMemo,
        author_id: user.id,
        author_name: user.username,
        status: state.newMemo.type === 'approval_request' ? 'pending' : 'approved',
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      }

      const response = await fetch('http://localhost:8002/api/memos', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('authToken')}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(memoData)
      })

      if (response.ok) {
        const createdMemo = await response.json()
        setState(prev => ({
          ...prev,
          memos: [createdMemo, ...prev.memos],
          showCreateModal: false,
          isLoading: false
        }))
        
        // Reset form
        setState(prev => ({
          ...prev,
          newMemo: {
            title: '',
            content: '',
            type: 'general',
            priority: 'medium',
            recipients: 'all',
            department: user.department
          }
        }))
        
        logger.info('Memo created successfully', 'MemosPage', { memoId: createdMemo.id })
      } else {
        // If API fails, add to local state (mock behavior)
        const mockMemo: Memo = {
          id: Date.now().toString(),
          ...memoData
        }
        
        setState(prev => ({
          ...prev,
          memos: [mockMemo, ...prev.memos],
          showCreateModal: false,
          isLoading: false,
          newMemo: {
            title: '',
            content: '',
            type: 'general',
            priority: 'medium',
            recipients: 'all',
            department: user.department
          }
        }))
        
        logger.info('Memo created locally (mock)', 'MemosPage')
      }
    } catch (error) {
      logger.error('Failed to create memo', 'MemosPage', error)
      setState(prev => ({ ...prev, isLoading: false }))
      alert('Failed to create memo. Please try again.')
    }
  }

  const handleApproveMemo = async (memoId: string, action: 'approve' | 'reject') => {
    if (user.role !== 'admin' && user.role !== 'manager') {
      alert('You do not have permission to approve memos')
      return
    }

    try {
      const response = await fetch(`http://localhost:8002/api/memos/${memoId}/approve`, {
        method: 'PATCH',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('authToken')}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ action, approved_by: user.id })
      })

      if (response.ok) {
        setState(prev => ({
          ...prev,
          memos: prev.memos.map(memo => 
            memo.id === memoId 
              ? { ...memo, status: action === 'approve' ? 'approved' : 'rejected', approved_by: user.id }
              : memo
          )
        }))
        logger.info(`Memo ${action}d`, 'MemosPage', { memoId, action })
      } else {
        // Mock behavior if API fails
        setState(prev => ({
          ...prev,
          memos: prev.memos.map(memo => 
            memo.id === memoId 
              ? { ...memo, status: action === 'approve' ? 'approved' : 'rejected', approved_by: user.id }
              : memo
          )
        }))
        logger.info(`Memo ${action}d locally (mock)`, 'MemosPage', { memoId, action })
      }
    } catch (error) {
      logger.error(`Failed to ${action} memo`, 'MemosPage', error)
      alert(`Failed to ${action} memo. Please try again.`)
    }
  }

  const getTypeColor = (type: string): string => {
    switch (type) {
      case 'announcement': return '#ff0000'
      case 'approval_request': return '#f59e0b'
      case 'general': return '#10b981'
      default: return '#6b7280'
    }
  }

  const getStatusColor = (status: string): string => {
    switch (status) {
      case 'approved': return '#10b981'
      case 'pending': return '#f59e0b'
      case 'rejected': return '#ef4444'
      default: return '#6b7280'
    }
  }

  const getPriorityColor = (priority: string): string => {
    switch (priority) {
      case 'high': return '#ef4444'
      case 'medium': return '#f59e0b'
      case 'low': return '#10b981'
      default: return '#6b7280'
    }
  }

  const canApproveMemo = (memo: Memo): boolean => {
    return (user.role === 'admin' || user.role === 'manager') && 
           memo.status === 'pending' && 
           memo.author_id !== user.id
  }

  if (state.error) {
    return (
      <div style={{
        minHeight: '100vh',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        backgroundColor: '#f9fafb',
        padding: '20px'
      }}>
        <div style={{
          maxWidth: '500px',
          width: '100%',
          backgroundColor: 'white',
          padding: '40px',
          borderRadius: '16px',
          boxShadow: '0 10px 15px -3px rgba(0, 0, 0, 0.1)',
          textAlign: 'center'
        }}>
          <div style={{ fontSize: '48px', marginBottom: '20px' }}>⚠️</div>
          <h2 style={{ color: '#ef4444', marginBottom: '16px' }}>Memos Error</h2>
          <p style={{ color: '#6b7280', marginBottom: '24px' }}>{state.error}</p>
          <button
            onClick={loadMemosData}
            style={{
              padding: '12px 24px',
              backgroundColor: '#667eea',
              color: 'white',
              border: 'none',
              borderRadius: '8px',
              cursor: 'pointer',
              marginRight: '12px'
            }}
          >
            Try Again
          </button>
          <button
            onClick={onNavigateBack}
            style={{
              padding: '12px 24px',
              backgroundColor: '#6b7280',
              color: 'white',
              border: 'none',
              borderRadius: '8px',
              cursor: 'pointer'
            }}
          >
            Go Back
          </button>
        </div>
      </div>
    )
  }

  return (
    <div style={{ minHeight: '100vh', backgroundColor: '#f9fafb' }}>
      {/* Header */}
      <header style={{
        backgroundColor: 'white',
        boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1)',
        padding: '16px 0'
      }}>
        <div style={{
          maxWidth: '1400px',
          margin: '0 auto',
          padding: '0 24px',
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center'
        }}>
          <div style={{ display: 'flex', alignItems: 'center', gap: '16px' }}>
            <button
              onClick={onNavigateBack}
              style={{
                padding: '8px 12px',
                backgroundColor: '#f3f4f6',
                border: 'none',
                borderRadius: '6px',
                cursor: 'pointer',
                fontSize: '14px'
              }}
            >
              ← Back
            </button>
            <div>
              <h1 style={{ 
                margin: 0, 
                color: '#1f2937', 
                fontSize: '24px',
                fontWeight: 'bold'
              }}>
                📝 Memo System
              </h1>
              <p style={{ 
                margin: '4px 0 0 0', 
                color: '#6b7280', 
                fontSize: '14px' 
              }}>
                {user.role === 'admin' || user.role === 'manager' 
                  ? 'Manage all memos and approvals' 
                  : 'Create and view your memos'}
              </p>
            </div>
          </div>
          
          <button
            onClick={() => setState(prev => ({ ...prev, showCreateModal: true }))}
            style={{
              padding: '12px 20px',
              backgroundColor: '#667eea',
              color: 'white',
              border: 'none',
              borderRadius: '8px',
              cursor: 'pointer',
              fontSize: '14px',
              fontWeight: '500',
              display: 'flex',
              alignItems: 'center',
              gap: '8px'
            }}
          >
            ➕ New Memo
          </button>
        </div>
      </header>

      {/* Main Content */}
      <main style={{ maxWidth: '1400px', margin: '0 auto', padding: '24px' }}>
        {/* Filters */}
        <div style={{
          backgroundColor: 'white',
          borderRadius: '12px',
          padding: '24px',
          marginBottom: '24px',
          boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)'
        }}>
          <h2 style={{ 
            fontSize: '18px', 
            fontWeight: 'bold', 
            color: '#1f2937', 
            marginBottom: '20px' 
          }}>
            🔍 Filters & Search
          </h2>
          
          <div style={{
            display: 'grid',
            gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))',
            gap: '16px'
          }}>
            <div>
              <label style={{ 
                display: 'block', 
                fontSize: '14px', 
                fontWeight: '500', 
                color: '#374151', 
                marginBottom: '6px' 
              }}>
                Type
              </label>
              <select
                value={state.filters.type}
                onChange={(e) => setState(prev => ({
                  ...prev,
                  filters: { ...prev.filters, type: e.target.value }
                }))}
                style={{
                  width: '100%',
                  padding: '8px 12px',
                  border: '1px solid #d1d5db',
                  borderRadius: '6px',
                  fontSize: '14px',
                  backgroundColor: 'white'
                }}
              >
                <option value="all">All Types</option>
                <option value="announcement">Announcement</option>
                <option value="approval_request">Approval Request</option>
                <option value="general">General</option>
              </select>
            </div>

            <div>
              <label style={{ 
                display: 'block', 
                fontSize: '14px', 
                fontWeight: '500', 
                color: '#374151', 
                marginBottom: '6px' 
              }}>
                Status
              </label>
              <select
                value={state.filters.status}
                onChange={(e) => setState(prev => ({
                  ...prev,
                  filters: { ...prev.filters, status: e.target.value }
                }))}
                style={{
                  width: '100%',
                  padding: '8px 12px',
                  border: '1px solid #d1d5db',
                  borderRadius: '6px',
                  fontSize: '14px',
                  backgroundColor: 'white'
                }}
              >
                <option value="all">All Status</option>
                <option value="approved">Approved</option>
                <option value="pending">Pending</option>
                <option value="rejected">Rejected</option>
              </select>
            </div>

            <div>
              <label style={{ 
                display: 'block', 
                fontSize: '14px', 
                fontWeight: '500', 
                color: '#374151', 
                marginBottom: '6px' 
              }}>
                Priority
              </label>
              <select
                value={state.filters.priority}
                onChange={(e) => setState(prev => ({
                  ...prev,
                  filters: { ...prev.filters, priority: e.target.value }
                }))}
                style={{
                  width: '100%',
                  padding: '8px 12px',
                  border: '1px solid #d1d5db',
                  borderRadius: '6px',
                  fontSize: '14px',
                  backgroundColor: 'white'
                }}
              >
                <option value="all">All Priority</option>
                <option value="high">High</option>
                <option value="medium">Medium</option>
                <option value="low">Low</option>
              </select>
            </div>

            <div>
              <label style={{ 
                display: 'block', 
                fontSize: '14px', 
                fontWeight: '500', 
                color: '#374151', 
                marginBottom: '6px' 
              }}>
                Search
              </label>
              <input
                type="text"
                placeholder="Search memos..."
                value={state.filters.search}
                onChange={(e) => setState(prev => ({
                  ...prev,
                  filters: { ...prev.filters, search: e.target.value }
                }))}
                style={{
                  width: '100%',
                  padding: '8px 12px',
                  border: '1px solid #d1d5db',
                  borderRadius: '6px',
                  fontSize: '14px'
                }}
              />
            </div>
          </div>
        </div>

        {/* Memos List */}
        <div style={{
          backgroundColor: 'white',
          borderRadius: '12px',
          padding: '24px',
          boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)'
        }}>
          <div style={{
            display: 'flex',
            justifyContent: 'space-between',
            alignItems: 'center',
            marginBottom: '20px'
          }}>
            <h2 style={{ 
              fontSize: '18px', 
              fontWeight: 'bold', 
              color: '#1f2937',
              margin: 0
            }}>
              📝 Memos ({state.filteredMemos.length})
            </h2>
            
            <button
              onClick={loadMemosData}
              disabled={state.isLoading}
              style={{
                padding: '8px 16px',
                backgroundColor: state.isLoading ? '#9ca3af' : '#f3f4f6',
                border: '1px solid #d1d5db',
                borderRadius: '6px',
                cursor: state.isLoading ? 'not-allowed' : 'pointer',
                fontSize: '14px',
                display: 'flex',
                alignItems: 'center',
                gap: '8px'
              }}
            >
              {state.isLoading ? (
                <div style={{
                  width: '16px',
                  height: '16px',
                  border: '2px solid transparent',
                  borderTop: '2px solid #6b7280',
                  borderRadius: '50%',
                  animation: 'spin 1s linear infinite'
                }} />
              ) : (
                '🔄'
              )}
              Refresh
            </button>
          </div>
          
          {state.isLoading ? (
            <div style={{
              display: 'flex',
              justifyContent: 'center',
              alignItems: 'center',
              height: '200px'
            }}>
              <div style={{
                width: '40px',
                height: '40px',
                border: '4px solid #e5e7eb',
                borderTop: '4px solid #667eea',
                borderRadius: '50%',
                animation: 'spin 1s linear infinite'
              }} />
            </div>
          ) : state.filteredMemos.length > 0 ? (
            <div style={{ display: 'flex', flexDirection: 'column', gap: '16px' }}>
              {state.filteredMemos.map((memo) => (
                <div
                  key={memo.id}
                  style={{
                    padding: '24px',
                    border: '1px solid #e5e7eb',
                    borderRadius: '12px',
                    backgroundColor: '#fafafa',
                    transition: 'all 0.2s'
                  }}
                  onMouseEnter={(e) => {
                    e.currentTarget.style.backgroundColor = '#f3f4f6'
                    e.currentTarget.style.borderColor = '#d1d5db'
                  }}
                  onMouseLeave={(e) => {
                    e.currentTarget.style.backgroundColor = '#fafafa'
                    e.currentTarget.style.borderColor = '#e5e7eb'
                  }}
                >
                  <div style={{
                    display: 'flex',
                    justifyContent: 'space-between',
                    alignItems: 'flex-start',
                    marginBottom: '16px'
                  }}>
                    <div style={{ flex: 1 }}>
                      <div style={{ display: 'flex', alignItems: 'center', gap: '12px', marginBottom: '8px' }}>
                        <h3 style={{
                          margin: 0,
                          fontSize: '18px',
                          fontWeight: '600',
                          color: '#1f2937'
                        }}>
                          {memo.title}
                        </h3>
                        
                        <span style={{
                          padding: '4px 8px',
                          borderRadius: '12px',
                          fontSize: '12px',
                          fontWeight: '500',
                          backgroundColor: getTypeColor(memo.type) + '20',
                          color: getTypeColor(memo.type)
                        }}>
                          {memo.type.replace('_', ' ')}
                        </span>
                        
                        <span style={{
                          padding: '4px 8px',
                          borderRadius: '12px',
                          fontSize: '12px',
                          fontWeight: '500',
                          backgroundColor: getPriorityColor(memo.priority) + '20',
                          color: getPriorityColor(memo.priority)
                        }}>
                          {memo.priority}
                        </span>
                      </div>
                      
                      <p style={{
                        margin: '0 0 12px 0',
                        fontSize: '14px',
                        color: '#6b7280',
                        lineHeight: '1.5'
                      }}>
                        {memo.content.length > 200 
                          ? memo.content.substring(0, 200) + '...' 
                          : memo.content}
                      </p>
                      
                      <div style={{
                        display: 'flex',
                        gap: '16px',
                        fontSize: '12px',
                        color: '#6b7280'
                      }}>
                        <span>By: {memo.author_name}</span>
                        <span>To: {memo.recipients}</span>
                        <span>Created: {new Date(memo.created_at).toLocaleDateString()}</span>
                      </div>
                    </div>
                    
                    <div style={{
                      display: 'flex',
                      flexDirection: 'column',
                      gap: '8px',
                      alignItems: 'flex-end'
                    }}>
                      <span style={{
                        padding: '6px 12px',
                        borderRadius: '12px',
                        fontSize: '12px',
                        fontWeight: '500',
                        backgroundColor: getStatusColor(memo.status) + '20',
                        color: getStatusColor(memo.status)
                      }}>
                        {memo.status}
                      </span>
                      
                      <div style={{ display: 'flex', gap: '8px' }}>
                        <button
                          onClick={() => setState(prev => ({ 
                            ...prev, 
                            selectedMemo: memo, 
                            showViewModal: true 
                          }))}
                          style={{
                            padding: '6px 12px',
                            backgroundColor: '#667eea',
                            color: 'white',
                            border: 'none',
                            borderRadius: '6px',
                            cursor: 'pointer',
                            fontSize: '12px'
                          }}
                        >
                          View
                        </button>
                        
                        {canApproveMemo(memo) && (
                          <>
                            <button
                              onClick={() => handleApproveMemo(memo.id, 'approve')}
                              style={{
                                padding: '6px 12px',
                                backgroundColor: '#10b981',
                                color: 'white',
                                border: 'none',
                                borderRadius: '6px',
                                cursor: 'pointer',
                                fontSize: '12px'
                              }}
                            >
                              Approve
                            </button>
                            <button
                              onClick={() => handleApproveMemo(memo.id, 'reject')}
                              style={{
                                padding: '6px 12px',
                                backgroundColor: '#ef4444',
                                color: 'white',
                                border: 'none',
                                borderRadius: '6px',
                                cursor: 'pointer',
                                fontSize: '12px'
                              }}
                            >
                              Reject
                            </button>
                          </>
                        )}
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div style={{
              textAlign: 'center',
              color: '#6b7280',
              fontStyle: 'italic',
              padding: '60px 0'
            }}>
              <div style={{ fontSize: '48px', marginBottom: '16px' }}>📝</div>
              <p>No memos found matching your criteria</p>
              <button
                onClick={() => setState(prev => ({ ...prev, showCreateModal: true }))}
                style={{
                  marginTop: '16px',
                  padding: '12px 20px',
                  backgroundColor: '#667eea',
                  color: 'white',
                  border: 'none',
                  borderRadius: '8px',
                  cursor: 'pointer',
                  fontSize: '14px'
                }}
              >
                Create Your First Memo
              </button>
            </div>
          )}
        </div>
      </main>

      {/* Create Memo Modal */}
      {state.showCreateModal && (
        <div style={{
          position: 'fixed',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          backgroundColor: 'rgba(0, 0, 0, 0.5)',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          zIndex: 1000
        }}>
          <div style={{
            backgroundColor: 'white',
            borderRadius: '12px',
            padding: '32px',
            maxWidth: '600px',
            width: '90%',
            maxHeight: '90vh',
            overflowY: 'auto'
          }}>
            <h2 style={{
              fontSize: '20px',
              fontWeight: 'bold',
              color: '#1f2937',
              marginBottom: '24px'
            }}>
              ➕ Create New Memo
            </h2>
            
            <div style={{ display: 'flex', flexDirection: 'column', gap: '16px' }}>
              <div>
                <label style={{
                  display: 'block',
                  fontSize: '14px',
                  fontWeight: '500',
                  color: '#374151',
                  marginBottom: '6px'
                }}>
                  Title *
                </label>
                <input
                  type="text"
                  value={state.newMemo.title}
                  onChange={(e) => setState(prev => ({
                    ...prev,
                    newMemo: { ...prev.newMemo, title: e.target.value }
                  }))}
                  placeholder="Enter memo title"
                  style={{
                    width: '100%',
                    padding: '12px',
                    border: '1px solid #d1d5db',
                    borderRadius: '6px',
                    fontSize: '14px',
                    boxSizing: 'border-box'
                  }}
                />
              </div>

              <div>
                <label style={{
                  display: 'block',
                  fontSize: '14px',
                  fontWeight: '500',
                  color: '#374151',
                  marginBottom: '6px'
                }}>
                  Content *
                </label>
                <textarea
                  value={state.newMemo.content}
                  onChange={(e) => setState(prev => ({
                    ...prev,
                    newMemo: { ...prev.newMemo, content: e.target.value }
                  }))}
                  placeholder="Enter memo content"
                  rows={6}
                  style={{
                    width: '100%',
                    padding: '12px',
                    border: '1px solid #d1d5db',
                    borderRadius: '6px',
                    fontSize: '14px',
                    resize: 'vertical',
                    boxSizing: 'border-box'
                  }}
                />
              </div>

              <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '16px' }}>
                <div>
                  <label style={{
                    display: 'block',
                    fontSize: '14px',
                    fontWeight: '500',
                    color: '#374151',
                    marginBottom: '6px'
                  }}>
                    Type
                  </label>
                  <select
                    value={state.newMemo.type}
                    onChange={(e) => setState(prev => ({
                      ...prev,
                      newMemo: { 
                        ...prev.newMemo, 
                        type: e.target.value as 'announcement' | 'approval_request' | 'general',
                        recipients: e.target.value === 'approval_request' ? 'managers' : prev.newMemo.recipients
                      }
                    }))}
                    style={{
                      width: '100%',
                      padding: '12px',
                      border: '1px solid #d1d5db',
                      borderRadius: '6px',
                      fontSize: '14px',
                      backgroundColor: 'white'
                    }}
                  >
                    {memoTypes.map(type => (
                      <option key={type.value} value={type.value}>
                        {type.label}
                      </option>
                    ))}
                  </select>
                  <p style={{
                    margin: '4px 0 0 0',
                    fontSize: '12px',
                    color: '#6b7280'
                  }}>
                    {memoTypes.find(t => t.value === state.newMemo.type)?.description}
                  </p>
                </div>

                <div>
                  <label style={{
                    display: 'block',
                    fontSize: '14px',
                    fontWeight: '500',
                    color: '#374151',
                    marginBottom: '6px'
                  }}>
                    Priority
                  </label>
                  <select
                    value={state.newMemo.priority}
                    onChange={(e) => setState(prev => ({
                      ...prev,
                      newMemo: { 
                        ...prev.newMemo, 
                        priority: e.target.value as 'low' | 'medium' | 'high' 
                      }
                    }))}
                    style={{
                      width: '100%',
                      padding: '12px',
                      border: '1px solid #d1d5db',
                      borderRadius: '6px',
                      fontSize: '14px',
                      backgroundColor: 'white'
                    }}
                  >
                    <option value="low">Low</option>
                    <option value="medium">Medium</option>
                    <option value="high">High</option>
                  </select>
                </div>
              </div>

              <div>
                <label style={{
                  display: 'block',
                  fontSize: '14px',
                  fontWeight: '500',
                  color: '#374151',
                  marginBottom: '6px'
                }}>
                  Recipients
                </label>
                <select
                  value={state.newMemo.recipients}
                  onChange={(e) => setState(prev => ({
                    ...prev,
                    newMemo: { ...prev.newMemo, recipients: e.target.value as 'all' | 'managers' | 'department' }
                  }))}
                  disabled={state.newMemo.type === 'approval_request'}
                  style={{
                    width: '100%',
                    padding: '12px',
                    border: '1px solid #d1d5db',
                    borderRadius: '6px',
                    fontSize: '14px',
                    backgroundColor: state.newMemo.type === 'approval_request' ? '#f9fafb' : 'white'
                  }}
                >
                  <option value="all">All Users</option>
                  <option value="managers">Managers Only</option>
                  <option value="department">My Department</option>
                </select>
                {state.newMemo.type === 'approval_request' && (
                  <p style={{
                    margin: '4px 0 0 0',
                    fontSize: '12px',
                    color: '#6b7280'
                  }}>
                    Approval requests are automatically sent to managers
                  </p>
                )}
              </div>

              {state.newMemo.recipients === 'department' && (
                <div>
                  <label style={{
                    display: 'block',
                    fontSize: '14px',
                    fontWeight: '500',
                    color: '#374151',
                    marginBottom: '6px'
                  }}>
                    Department
                  </label>
                  <select
                    value={state.newMemo.department}
                    onChange={(e) => setState(prev => ({
                      ...prev,
                      newMemo: { ...prev.newMemo, department: e.target.value }
                    }))}
                    style={{
                      width: '100%',
                      padding: '12px',
                      border: '1px solid #d1d5db',
                      borderRadius: '6px',
                      fontSize: '14px',
                      backgroundColor: 'white'
                    }}
                  >
                    {departments.map(dept => (
                      <option key={dept} value={dept}>{dept}</option>
                    ))}
                  </select>
                </div>
              )}
            </div>

            <div style={{
              display: 'flex',
              justifyContent: 'flex-end',
              gap: '12px',
              marginTop: '24px'
            }}>
              <button
                onClick={() => setState(prev => ({ ...prev, showCreateModal: false }))}
                style={{
                  padding: '12px 20px',
                  backgroundColor: '#f3f4f6',
                  border: '1px solid #d1d5db',
                  borderRadius: '6px',
                  cursor: 'pointer',
                  fontSize: '14px'
                }}
              >
                Cancel
              </button>
              <button
                onClick={handleCreateMemo}
                disabled={!state.newMemo.title.trim() || !state.newMemo.content.trim() || state.isLoading}
                style={{
                  padding: '12px 20px',
                  backgroundColor: (!state.newMemo.title.trim() || !state.newMemo.content.trim() || state.isLoading) ? '#9ca3af' : '#667eea',
                  color: 'white',
                  border: 'none',
                  borderRadius: '6px',
                  cursor: (!state.newMemo.title.trim() || !state.newMemo.content.trim() || state.isLoading) ? 'not-allowed' : 'pointer',
                  fontSize: '14px',
                  fontWeight: '500'
                }}
              >
                {state.isLoading ? 'Creating...' : 'Create Memo'}
              </button>
            </div>
          </div>
        </div>
      )}

      {/* View Memo Modal */}
      {state.showViewModal && state.selectedMemo && (
        <div style={{
          position: 'fixed',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          backgroundColor: 'rgba(0, 0, 0, 0.5)',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          zIndex: 1000
        }}>
          <div style={{
            backgroundColor: 'white',
            borderRadius: '12px',
            padding: '32px',
            maxWidth: '700px',
            width: '90%',
            maxHeight: '90vh',
            overflowY: 'auto'
          }}>
            <div style={{
              display: 'flex',
              justifyContent: 'space-between',
              alignItems: 'flex-start',
              marginBottom: '24px'
            }}>
              <div>
                <h2 style={{
                  fontSize: '24px',
                  fontWeight: 'bold',
                  color: '#1f2937',
                  margin: '0 0 8px 0'
                }}>
                  {state.selectedMemo.title}
                </h2>
                
                <div style={{ display: 'flex', gap: '8px', marginBottom: '12px' }}>
                  <span style={{
                    padding: '4px 8px',
                    borderRadius: '12px',
                    fontSize: '12px',
                    fontWeight: '500',
                    backgroundColor: getTypeColor(state.selectedMemo.type) + '20',
                    color: getTypeColor(state.selectedMemo.type)
                  }}>
                    {state.selectedMemo.type.replace('_', ' ')}
                  </span>
                  
                  <span style={{
                    padding: '4px 8px',
                    borderRadius: '12px',
                    fontSize: '12px',
                    fontWeight: '500',
                    backgroundColor: getPriorityColor(state.selectedMemo.priority) + '20',
                    color: getPriorityColor(state.selectedMemo.priority)
                  }}>
                    {state.selectedMemo.priority}
                  </span>
                  
                  <span style={{
                    padding: '4px 8px',
                    borderRadius: '12px',
                    fontSize: '12px',
                    fontWeight: '500',
                    backgroundColor: getStatusColor(state.selectedMemo.status) + '20',
                    color: getStatusColor(state.selectedMemo.status)
                  }}>
                    {state.selectedMemo.status}
                  </span>
                </div>
                
                <div style={{
                  fontSize: '14px',
                  color: '#6b7280',
                  marginBottom: '16px'
                }}>
                  <p style={{ margin: '0 0 4px 0' }}>
                    <strong>From:</strong> {state.selectedMemo.author_name}
                  </p>
                  <p style={{ margin: '0 0 4px 0' }}>
                    <strong>To:</strong> {state.selectedMemo.recipients}
                  </p>
                  <p style={{ margin: '0 0 4px 0' }}>
                    <strong>Created:</strong> {new Date(state.selectedMemo.created_at).toLocaleString()}
                  </p>
                  {state.selectedMemo.approved_by && (
                    <p style={{ margin: '0 0 4px 0' }}>
                      <strong>Approved by:</strong> {state.selectedMemo.approved_by}
                    </p>
                  )}
                </div>
              </div>
              
              <button
                onClick={() => setState(prev => ({ ...prev, showViewModal: false, selectedMemo: null }))}
                style={{
                  padding: '8px',
                  backgroundColor: '#f3f4f6',
                  border: 'none',
                  borderRadius: '6px',
                  cursor: 'pointer',
                  fontSize: '18px'
                }}
              >
                ✕
              </button>
            </div>
            
            <div style={{
              padding: '20px',
              backgroundColor: '#f9fafb',
              borderRadius: '8px',
              marginBottom: '24px'
            }}>
              <h3 style={{
                fontSize: '16px',
                fontWeight: '600',
                color: '#1f2937',
                marginBottom: '12px'
              }}>
                Content
              </h3>
              <div style={{
                fontSize: '14px',
                color: '#374151',
                lineHeight: '1.6',
                whiteSpace: 'pre-wrap'
              }}>
                {state.selectedMemo.content}
              </div>
            </div>
            
            {canApproveMemo(state.selectedMemo) && (
              <div style={{
                display: 'flex',
                justifyContent: 'center',
                gap: '12px',
                marginTop: '24px'
              }}>
                <button
                  onClick={() => {
                    handleApproveMemo(state.selectedMemo!.id, 'approve')
                    setState(prev => ({ ...prev, showViewModal: false, selectedMemo: null }))
                  }}
                  style={{
                    padding: '12px 24px',
                    backgroundColor: '#10b981',
                    color: 'white',
                    border: 'none',
                    borderRadius: '6px',
                    cursor: 'pointer',
                    fontSize: '14px',
                    fontWeight: '500'
                  }}
                >
                  ✅ Approve
                </button>
                <button
                  onClick={() => {
                    handleApproveMemo(state.selectedMemo!.id, 'reject')
                    setState(prev => ({ ...prev, showViewModal: false, selectedMemo: null }))
                  }}
                  style={{
                    padding: '12px 24px',
                    backgroundColor: '#ef4444',
                    color: 'white',
                    border: 'none',
                    borderRadius: '6px',
                    cursor: 'pointer',
                    fontSize: '14px',
                    fontWeight: '500'
                  }}
                >
                  ❌ Reject
                </button>
              </div>
            )}
          </div>
        </div>
      )}

      {/* CSS Animation */}
      <style>{`
        @keyframes spin {
          0% { transform: rotate(0deg); }
          100% { transform: rotate(360deg); }
        }
      `}</style>
    </div>
  )
}
