/**
 * Enhanced Dashboard Page Component
 * Features: Analytics, charts, real-time data, quick actions
 */
import React, { useState, useEffect, useCallback } from 'react'
import { logger, <PERSON>rror<PERSON>andler, PerformanceMonitor } from '../utils/logger'
import type { DashboardStats, Task, User, Project, Notification } from '../types'
import AIAssistant from '../components/AIAssistant'
import GitHubIntegration from '../components/GitHubIntegration'

interface DashboardPageProps {
  user: User
  onNavigateToTasks?: () => void
  onNavigateToReports?: () => void
  onNavigateToMemos?: () => void
  onNavigateToProjects?: () => void
  onNavigateToFiles?: () => void
  onNavigateToNotifications?: () => void
  onNavigateToAI?: () => void
  onLogout?: () => void
}

interface DashboardState {
  isLoading: boolean
  stats: DashboardStats | null
  recentTasks: Task[]
  recentProjects: Project[]
  notifications: Notification[]
  quickStats: {
    todayHours: number
    weeklyHours: number
    completionRate: number
    activeProjects: number
  }
  error: string | null
  // Manager-specific data
  departmentLogs: TimeLog[]
  departmentActivity: ActivityLog[]
  teamMembers: User[]
  departmentStats: {
    totalEmployees: number
    activeToday: number
    totalHoursToday: number
    averageHoursPerEmployee: number
  }
}

interface TimeLog {
  id: string
  userId: string
  userName: string
  action: 'clock-in' | 'clock-out' | 'break-start' | 'break-end'
  timestamp: string
  location?: string
  notes?: string
}

interface ActivityLog {
  id: string
  userId: string
  userName: string
  action: string
  description: string
  timestamp: string
  category: 'attendance' | 'task' | 'project' | 'system'
}

export default function DashboardPage({
  user,
  onNavigateToTasks,
  onNavigateToReports,
  onNavigateToMemos,
  onNavigateToProjects,
  onNavigateToFiles,
  onNavigateToNotifications,
  onNavigateToAI,
  onLogout
}: DashboardPageProps) {
  const [state, setState] = useState<DashboardState>({
    isLoading: true,
    stats: null,
    recentTasks: [],
    recentProjects: [],
    notifications: [],
    quickStats: {
      todayHours: 0,
      weeklyHours: 0,
      completionRate: 0,
      activeProjects: 0
    },
    error: null,
    // Manager-specific data
    departmentLogs: [],
    departmentActivity: [],
    teamMembers: [],
    departmentStats: {
      totalEmployees: 0,
      activeToday: 0,
      totalHoursToday: 0,
      averageHoursPerEmployee: 0
    }
  })

  // AI Assistant state
  const [isAIOpen, setIsAIOpen] = useState(false)

  // GitHub Integration state
  const [isGitHubOpen, setIsGitHubOpen] = useState(false)

  useEffect(() => {
    logger.componentMount('DashboardPage')
    loadDashboardData()
    
    // Set up real-time updates
    const interval = setInterval(loadDashboardData, 30000) // Refresh every 30 seconds
    
    return () => {
      clearInterval(interval)
      logger.componentUnmount('DashboardPage')
    }
  }, [])

  const loadDashboardData = useCallback(async () => {
    try {
      setState(prev => ({ ...prev, isLoading: true, error: null }))
      logger.info('Loading dashboard data', 'DashboardPage')
      
      PerformanceMonitor.startTimer('dashboard_load')

      // Load multiple data sources in parallel
      const [statsResponse, tasksResponse, projectsResponse, notificationsResponse] = await Promise.all([
        fetch('http://localhost:8002/api/dashboard/stats', {
          headers: {
            'Authorization': `Bearer ${localStorage.getItem('authToken')}`,
            'Content-Type': 'application/json'
          }
        }),
        fetch('http://localhost:8002/api/tasks?limit=5&sort=updated_at', {
          headers: {
            'Authorization': `Bearer ${localStorage.getItem('authToken')}`,
            'Content-Type': 'application/json'
          }
        }),
        fetch('http://localhost:8002/api/projects?limit=3&status=active', {
          headers: {
            'Authorization': `Bearer ${localStorage.getItem('authToken')}`,
            'Content-Type': 'application/json'
          }
        }),
        fetch('http://localhost:8002/api/notifications?limit=5&unread=true', {
          headers: {
            'Authorization': `Bearer ${localStorage.getItem('authToken')}`,
            'Content-Type': 'application/json'
          }
        })
      ])

      const duration = PerformanceMonitor.endTimer('dashboard_load')

      // Process responses
      const stats = statsResponse.ok ? await statsResponse.json() : null
      const tasks = tasksResponse.ok ? await tasksResponse.json() : { tasks: [] }
      const projects = projectsResponse.ok ? await projectsResponse.json() : { projects: [] }
      const notifications = notificationsResponse.ok ? await notificationsResponse.json() : { notifications: [] }

      // Calculate quick stats (mock data for now)
      const quickStats = {
        todayHours: 6.5,
        weeklyHours: 32.5,
        completionRate: 85,
        activeProjects: projects.projects?.length || 0
      }

      // Load manager-specific data if user is a manager
      let managerData = {
        departmentLogs: [],
        departmentActivity: [],
        teamMembers: [],
        departmentStats: {
          totalEmployees: 0,
          activeToday: 0,
          totalHoursToday: 0,
          averageHoursPerEmployee: 0
        }
      }

      if (user.role === 'manager') {
        try {
          const [logsResponse, activityResponse, teamResponse] = await Promise.all([
            fetch(`http://localhost:8002/api/manager/department-logs?department=${user.department}`, {
              headers: {
                'Authorization': `Bearer ${localStorage.getItem('authToken')}`,
                'Content-Type': 'application/json'
              }
            }),
            fetch(`http://localhost:8002/api/manager/department-activity?department=${user.department}`, {
              headers: {
                'Authorization': `Bearer ${localStorage.getItem('authToken')}`,
                'Content-Type': 'application/json'
              }
            }),
            fetch(`http://localhost:8002/api/manager/team-members?department=${user.department}`, {
              headers: {
                'Authorization': `Bearer ${localStorage.getItem('authToken')}`,
                'Content-Type': 'application/json'
              }
            })
          ])

          const logs = logsResponse.ok ? await logsResponse.json() : { logs: [] }
          const activity = activityResponse.ok ? await activityResponse.json() : { activities: [] }
          const team = teamResponse.ok ? await teamResponse.json() : { members: [] }

          // Calculate department stats
          const teamMembers = team.members || []
          const todayLogs = logs.logs?.filter((log: TimeLog) => {
            const logDate = new Date(log.timestamp).toDateString()
            const today = new Date().toDateString()
            return logDate === today
          }) || []

          const activeToday = new Set(todayLogs.map((log: TimeLog) => log.userId)).size
          const totalHoursToday = todayLogs.length * 0.5 // Rough estimate
          const averageHoursPerEmployee = teamMembers.length > 0 ? totalHoursToday / teamMembers.length : 0

          managerData = {
            departmentLogs: logs.logs || [],
            departmentActivity: activity.activities || [],
            teamMembers,
            departmentStats: {
              totalEmployees: teamMembers.length,
              activeToday,
              totalHoursToday,
              averageHoursPerEmployee
            }
          }
        } catch (error) {
          logger.error('Failed to load manager data', 'DashboardPage', error)
        }
      }

      setState(prev => ({
        ...prev,
        isLoading: false,
        stats,
        recentTasks: tasks.tasks || [],
        recentProjects: projects.projects || [],
        notifications: notifications.notifications || [],
        quickStats,
        ...managerData
      }))

      logger.info(`Dashboard loaded in ${duration.toFixed(2)}ms`, 'DashboardPage')
      
    } catch (error) {
      const handledError = ErrorHandler.handleApiError(error, 'DashboardPage')
      setState(prev => ({ 
        ...prev, 
        isLoading: false, 
        error: handledError.message 
      }))
      logger.error('Failed to load dashboard data', 'DashboardPage', error)
    }
  }, [])

  const handleQuickAction = (action: string) => {
    logger.info(`Quick action: ${action}`, 'DashboardPage')

    switch (action) {
      case 'new-task':
        onNavigateToTasks?.()
        break
      case 'new-memo':
        onNavigateToMemos?.()
        break
      case 'view-reports':
        onNavigateToReports?.()
        break
      case 'new-project':
        onNavigateToProjects?.()
        break
      case 'upload-files':
        onNavigateToFiles?.()
        break
      case 'view-notifications':
        onNavigateToNotifications?.()
        break
      case 'ai-assistant':
        onNavigateToAI?.()
        break
      default:
        logger.warn(`Unknown quick action: ${action}`, 'DashboardPage')
    }
  }

  const getGreeting = (): string => {
    const hour = new Date().getHours()
    if (hour < 12) return 'Good morning'
    if (hour < 17) return 'Good afternoon'
    return 'Good evening'
  }

  const getProgressColor = (percentage: number): string => {
    if (percentage >= 80) return '#10b981'
    if (percentage >= 60) return '#f59e0b'
    return '#ef4444'
  }

  if (state.error) {
    return (
      <div style={{
        minHeight: '100vh',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        backgroundColor: '#f9fafb',
        padding: '20px'
      }}>
        <div style={{
          maxWidth: '500px',
          width: '100%',
          backgroundColor: '#1a1a1a',
          padding: '40px',
          borderRadius: '16px',
          boxShadow: '0 10px 15px -3px rgba(0, 0, 0, 0.1)',
          textAlign: 'center'
        }}>
          <div style={{ fontSize: '48px', marginBottom: '20px' }}>⚠️</div>
          <h2 style={{ color: '#ef4444', marginBottom: '16px' }}>Dashboard Error</h2>
          <p style={{ color: '#6b7280', marginBottom: '24px' }}>{state.error}</p>
          <button
            onClick={loadDashboardData}
            style={{
              padding: '12px 24px',
              backgroundColor: '#667eea',
              color: '#DC143C',
              border: 'none',
              borderRadius: '8px',
              cursor: 'pointer',
              marginRight: '12px'
            }}
          >
            Try Again
          </button>
          <button
            onClick={onLogout}
            style={{
              padding: '12px 24px',
              backgroundColor: '#6b7280',
              color: '#8B0000',
              border: 'none',
              borderRadius: '8px',
              cursor: 'pointer'
            }}
          >
            Logout
          </button>
        </div>
      </div>
    )
  }

  return (
    <div style={{
      minHeight: '100vh',
      background: 'linear-gradient(135deg, #0f0f0f 0%, #1a0a0a 50%, #0f0f0f 100%)',
      position: 'relative',
      overflow: 'hidden'
    }}>
      {/* Animated background elements */}
      <div style={{
        position: 'absolute',
        top: '10%',
        left: '10%',
        width: '200px',
        height: '200px',
        background: 'radial-gradient(circle, rgba(239, 68, 68, 0.05) 0%, transparent 70%)',
        borderRadius: '50%',
        animation: 'float 12s ease-in-out infinite'
      }} />
      <div style={{
        position: 'absolute',
        bottom: '10%',
        right: '10%',
        width: '150px',
        height: '150px',
        background: 'radial-gradient(circle, rgba(239, 68, 68, 0.03) 0%, transparent 70%)',
        borderRadius: '50%',
        animation: 'float 8s ease-in-out infinite reverse'
      }} />
      {/* Compact Header */}
      <header style={{
        backgroundColor: '#1a1a1a',
        boxShadow: `
          0 2px 4px rgba(0, 0, 0, 0.3),
          0 0 10px rgba(239, 68, 68, 0.1)
        `,
        padding: '12px 0',
        border: '1px solid rgba(239, 68, 68, 0.1)',
        position: 'relative',
        zIndex: 10
      }}>
        <div style={{
          maxWidth: '1200px',
          margin: '0 auto',
          padding: '0 16px',
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center'
        }}>
          <div>
            <div style={{ display: 'flex', alignItems: 'center', gap: '12px' }}>
              <img
                src="/logo.svg"
                alt="CTNL WORK-BOARD"
                style={{ height: '32px', width: 'auto' }}
              />
              <h1 style={{
                margin: 0,
                background: 'linear-gradient(135deg, #8B0000, #DC143C)',
                WebkitBackgroundClip: 'text',
                WebkitTextFillColor: 'transparent',
                fontSize: '20px',
                fontWeight: 'bold',
                textShadow: '0 0 10px rgba(139, 0, 0, 0.5)'
              }}>
                🚀 CTNL WORK-BOARD
              </h1>
            </div>
            <p style={{
              margin: '2px 0 0 0',
              color: '#9ca3af',
              fontSize: '12px',
              opacity: 0.8
            }}>
              {getGreeting()}, {user.username}! ({user.role})
            </p>
          </div>
          
          <div style={{ display: 'flex', gap: '8px', alignItems: 'center' }}>
            {/* GitHub Integration */}
            <button
              onClick={() => setIsGitHubOpen(true)}
              style={{
                padding: '8px',
                background: 'linear-gradient(145deg, #2a2a2a, #0f0f0f)',
                border: 'none',
                borderRadius: '8px',
                cursor: 'pointer',
                fontSize: '16px',
                color: '#8B0000',
                boxShadow: '0 4px 8px rgba(139, 0, 0, 0.3)',
                transition: 'all 0.3s ease'
              }}
              title="GitHub Integration"
            >
              🐙
            </button>

            {/* AI Assistant */}
            <button
              onClick={() => setIsAIOpen(true)}
              style={{
                padding: '8px',
                background: 'linear-gradient(145deg, #8B0000, #DC143C)',
                border: 'none',
                borderRadius: '8px',
                cursor: 'pointer',
                fontSize: '16px',
                color: '#DC143C',
                boxShadow: '0 4px 8px rgba(139, 0, 0, 0.3)',
                transition: 'all 0.3s ease'
              }}
              title="AI Assistant"
            >
              🤖
            </button>

            {/* Notifications */}
            <div style={{ position: 'relative' }}>
              <button style={{
                padding: '8px',
                background: 'linear-gradient(145deg, #2a2a2a, #0f0f0f)',
                border: 'none',
                borderRadius: '8px',
                cursor: 'pointer',
                fontSize: '16px',
                color: '#ef4444',
                boxShadow: `
                  6px 6px 12px #0a0a0a,
                  -6px -6px 12px #2a2a2a
                `,
                transition: 'all 0.2s ease'
              }}
              onMouseEnter={(e) => {
                e.currentTarget.style.transform = 'translateY(-1px)'
                e.currentTarget.style.boxShadow = `
                  8px 8px 16px #0a0a0a,
                  -8px -8px 16px #2a2a2a,
                  0 0 15px rgba(239, 68, 68, 0.3)
                `
              }}
              onMouseLeave={(e) => {
                e.currentTarget.style.transform = 'translateY(0)'
                e.currentTarget.style.boxShadow = `
                  6px 6px 12px #0a0a0a,
                  -6px -6px 12px #2a2a2a
                `
              }}
              >
                🔔
              </button>
              {state.notifications.length > 0 && (
                <span style={{
                  position: 'absolute',
                  top: '-4px',
                  right: '-4px',
                  backgroundColor: '#8B0000',
                  color: '#DC143C',
                  borderRadius: '50%',
                  width: '20px',
                  height: '20px',
                  fontSize: '12px',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center'
                }}>
                  {state.notifications.length}
                </span>
              )}
            </div>
            
            <button
              onClick={onLogout}
              style={{
                padding: '12px 20px',
                background: 'linear-gradient(145deg, #ef4444, #dc2626)',
                color: '#1a1a1a',
                border: 'none',
                borderRadius: '12px',
                cursor: 'pointer',
                fontSize: '14px',
                fontWeight: '600',
                boxShadow: `
                  6px 6px 12px #0a0a0a,
                  -6px -6px 12px #2a2a2a,
                  0 0 15px rgba(239, 68, 68, 0.3)
                `,
                transition: 'all 0.2s ease',
                textShadow: '0 0 10px rgba(139, 0, 0, 0.5)'
              }}
              onMouseEnter={(e) => {
                e.currentTarget.style.transform = 'translateY(-1px)'
                e.currentTarget.style.boxShadow = `
                  8px 8px 16px #0a0a0a,
                  -8px -8px 16px #2a2a2a,
                  0 0 20px rgba(239, 68, 68, 0.4)
                `
              }}
              onMouseLeave={(e) => {
                e.currentTarget.style.transform = 'translateY(0)'
                e.currentTarget.style.boxShadow = `
                  6px 6px 12px #0a0a0a,
                  -6px -6px 12px #2a2a2a,
                  0 0 15px rgba(239, 68, 68, 0.3)
                `
              }}
            >
              🚪 Logout
            </button>
          </div>
        </div>
      </header>

      {/* Compact Main Content */}
      <main style={{ maxWidth: '1200px', margin: '0 auto', padding: '16px' }}>
        {state.isLoading ? (
          <div style={{
            display: 'flex',
            justifyContent: 'center',
            alignItems: 'center',
            height: '400px'
          }}>
            <div style={{
              width: '40px',
              height: '40px',
              border: '4px solid #e5e7eb',
              borderTop: '4px solid #667eea',
              borderRadius: '50%',
              animation: 'spin 1s linear infinite'
            }} />
          </div>
        ) : (
          <>
            {/* Compact Quick Stats Row */}
            <div style={{
              display: 'grid',
              gridTemplateColumns: 'repeat(auto-fit, minmax(180px, 1fr))',
              gap: '12px',
              marginBottom: '20px'
            }}>
              <div style={{
                backgroundColor: '#1a1a1a',
                padding: '16px',
                borderRadius: '12px',
                boxShadow: `
                  6px 6px 12px #0a0a0a,
                  -6px -6px 12px #2a2a2a,
                  inset 1px 1px 3px rgba(239, 68, 68, 0.1)
                `,
                border: '1px solid rgba(239, 68, 68, 0.1)',
                transition: 'all 0.3s ease'
              }}
              onMouseEnter={(e) => {
                e.currentTarget.style.transform = 'translateY(-2px)'
                e.currentTarget.style.boxShadow = `
                  16px 16px 32px #0a0a0a,
                  -16px -16px 32px #2a2a2a,
                  inset 2px 2px 6px rgba(239, 68, 68, 0.15),
                  0 0 20px rgba(239, 68, 68, 0.1)
                `
              }}
              onMouseLeave={(e) => {
                e.currentTarget.style.transform = 'translateY(0)'
                e.currentTarget.style.boxShadow = `
                  12px 12px 24px #0a0a0a,
                  -12px -12px 24px #2a2a2a,
                  inset 2px 2px 6px rgba(239, 68, 68, 0.1)
                `
              }}
              >
                <div style={{ display: 'flex', alignItems: 'center', marginBottom: '12px' }}>
                  <div style={{
                    width: '56px',
                    height: '56px',
                    background: 'linear-gradient(145deg, #2a2a2a, #0f0f0f)',
                    borderRadius: '16px',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    marginRight: '20px',
                    boxShadow: `
                      6px 6px 12px #0a0a0a,
                      -6px -6px 12px #2a2a2a,
                      inset 1px 1px 2px rgba(239, 68, 68, 0.1)
                    `,
                    border: '1px solid rgba(239, 68, 68, 0.2)'
                  }}>
                    <span style={{ fontSize: '28px', filter: 'drop-shadow(0 0 8px rgba(239, 68, 68, 0.3))' }}>⏰</span>
                  </div>
                  <div>
                    <h3 style={{ margin: 0, fontSize: '14px', color: '#9ca3af', opacity: 0.8 }}>Today's Hours</h3>
                    <p style={{
                      margin: 0,
                      fontSize: '32px',
                      fontWeight: 'bold',
                      background: 'linear-gradient(135deg, #8B0000, #DC143C)',
                      WebkitBackgroundClip: 'text',
                      WebkitTextFillColor: 'transparent',
                      textShadow: '0 0 15px rgba(139, 0, 0, 0.5)'
                    }}>
                      {state.quickStats.todayHours}h
                    </p>
                  </div>
                </div>
                <div style={{
                  width: '100%',
                  height: '4px',
                  backgroundColor: '#e5e7eb',
                  borderRadius: '2px',
                  overflow: 'hidden'
                }}>
                  <div style={{
                    width: `${(state.quickStats.todayHours / 8) * 100}%`,
                    height: '100%',
                    backgroundColor: '#3b82f6',
                    transition: 'width 0.3s ease'
                  }} />
                </div>
              </div>

              <div style={{
                backgroundColor: '#2a2a2a',
                padding: '24px',
                borderRadius: '12px',
                boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)'
              }}>
                <div style={{ display: 'flex', alignItems: 'center', marginBottom: '12px' }}>
                  <div style={{
                    width: '48px',
                    height: '48px',
                    backgroundColor: '#d1fae5',
                    borderRadius: '12px',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    marginRight: '16px'
                  }}>
                    <span style={{ fontSize: '24px' }}>✅</span>
                  </div>
                  <div>
                    <h3 style={{ margin: 0, fontSize: '14px', color: '#6b7280' }}>Completion Rate</h3>
                    <p style={{ margin: 0, fontSize: '28px', fontWeight: 'bold', color: '#1f2937' }}>
                      {state.quickStats.completionRate}%
                    </p>
                  </div>
                </div>
                <div style={{
                  width: '100%',
                  height: '4px',
                  backgroundColor: '#e5e7eb',
                  borderRadius: '2px',
                  overflow: 'hidden'
                }}>
                  <div style={{
                    width: `${state.quickStats.completionRate}%`,
                    height: '100%',
                    backgroundColor: getProgressColor(state.quickStats.completionRate),
                    transition: 'width 0.3s ease'
                  }} />
                </div>
              </div>

              <div style={{
                backgroundColor: '#2a2a2a',
                padding: '24px',
                borderRadius: '12px',
                boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)'
              }}>
                <div style={{ display: 'flex', alignItems: 'center', marginBottom: '12px' }}>
                  <div style={{
                    width: '48px',
                    height: '48px',
                    backgroundColor: '#fef3c7',
                    borderRadius: '12px',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    marginRight: '16px'
                  }}>
                    <span style={{ fontSize: '24px' }}>📁</span>
                  </div>
                  <div>
                    <h3 style={{ margin: 0, fontSize: '14px', color: '#6b7280' }}>Active Projects</h3>
                    <p style={{ margin: 0, fontSize: '28px', fontWeight: 'bold', color: '#1f2937' }}>
                      {state.quickStats.activeProjects}
                    </p>
                  </div>
                </div>
              </div>

              <div style={{
                backgroundColor: '#2a2a2a',
                padding: '24px',
                borderRadius: '12px',
                boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)'
              }}>
                <div style={{ display: 'flex', alignItems: 'center', marginBottom: '12px' }}>
                  <div style={{
                    width: '48px',
                    height: '48px',
                    backgroundColor: '#e0e7ff',
                    borderRadius: '12px',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    marginRight: '16px'
                  }}>
                    <span style={{ fontSize: '24px' }}>📊</span>
                  </div>
                  <div>
                    <h3 style={{ margin: 0, fontSize: '14px', color: '#6b7280' }}>Weekly Hours</h3>
                    <p style={{ margin: 0, fontSize: '28px', fontWeight: 'bold', color: '#1f2937' }}>
                      {state.quickStats.weeklyHours}h
                    </p>
                  </div>
                </div>
                <div style={{
                  width: '100%',
                  height: '4px',
                  backgroundColor: '#e5e7eb',
                  borderRadius: '2px',
                  overflow: 'hidden'
                }}>
                  <div style={{
                    width: `${(state.quickStats.weeklyHours / 40) * 100}%`,
                    height: '100%',
                    backgroundColor: '#6366f1',
                    transition: 'width 0.3s ease'
                  }} />
                </div>
              </div>
            </div>

            {/* Quick Actions */}
            <div style={{
              backgroundColor: '#2a2a2a',
              borderRadius: '12px',
              padding: '24px',
              marginBottom: '32px',
              boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)'
            }}>
              <h2 style={{ 
                fontSize: '18px', 
                fontWeight: 'bold', 
                color: '#1f2937', 
                marginBottom: '20px' 
              }}>
                🚀 Quick Actions
              </h2>
              
              <div style={{
                display: 'grid',
                gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))',
                gap: '16px'
              }}>
                <button
                  onClick={() => handleQuickAction('new-task')}
                  style={{
                    padding: '20px',
                    backgroundColor: '#f8fafc',
                    border: '2px solid #e2e8f0',
                    borderRadius: '12px',
                    cursor: 'pointer',
                    textAlign: 'left',
                    transition: 'all 0.2s'
                  }}
                  onMouseEnter={(e) => {
                    e.currentTarget.style.borderColor = '#667eea'
                    e.currentTarget.style.backgroundColor = '#f0f4ff'
                  }}
                  onMouseLeave={(e) => {
                    e.currentTarget.style.borderColor = '#e2e8f0'
                    e.currentTarget.style.backgroundColor = '#f8fafc'
                  }}
                >
                  <div style={{ fontSize: '32px', marginBottom: '8px' }}>📋</div>
                  <h3 style={{ margin: 0, fontSize: '16px', fontWeight: '600', color: '#1f2937' }}>
                    Create Task
                  </h3>
                  <p style={{ margin: '4px 0 0 0', fontSize: '14px', color: '#6b7280' }}>
                    Add a new task or assignment
                  </p>
                </button>

                <button
                  onClick={() => handleQuickAction('new-memo')}
                  style={{
                    padding: '20px',
                    backgroundColor: '#f8fafc',
                    border: '2px solid #e2e8f0',
                    borderRadius: '12px',
                    cursor: 'pointer',
                    textAlign: 'left',
                    transition: 'all 0.2s'
                  }}
                  onMouseEnter={(e) => {
                    e.currentTarget.style.borderColor = '#10b981'
                    e.currentTarget.style.backgroundColor = '#f0fdf4'
                  }}
                  onMouseLeave={(e) => {
                    e.currentTarget.style.borderColor = '#e2e8f0'
                    e.currentTarget.style.backgroundColor = '#f8fafc'
                  }}
                >
                  <div style={{ fontSize: '32px', marginBottom: '8px' }}>📝</div>
                  <h3 style={{ margin: 0, fontSize: '16px', fontWeight: '600', color: '#1f2937' }}>
                    Write Memo
                  </h3>
                  <p style={{ margin: '4px 0 0 0', fontSize: '14px', color: '#6b7280' }}>
                    Send internal communication
                  </p>
                </button>

                <button
                  onClick={() => handleQuickAction('view-reports')}
                  style={{
                    padding: '20px',
                    backgroundColor: '#f8fafc',
                    border: '2px solid #e2e8f0',
                    borderRadius: '12px',
                    cursor: 'pointer',
                    textAlign: 'left',
                    transition: 'all 0.2s'
                  }}
                  onMouseEnter={(e) => {
                    e.currentTarget.style.borderColor = '#f59e0b'
                    e.currentTarget.style.backgroundColor = '#3a3a3a'
                  }}
                  onMouseLeave={(e) => {
                    e.currentTarget.style.borderColor = '#e2e8f0'
                    e.currentTarget.style.backgroundColor = '#2a2a2a'
                  }}
                >
                  <div style={{ fontSize: '32px', marginBottom: '8px' }}>📈</div>
                  <h3 style={{ margin: 0, fontSize: '16px', fontWeight: '600', color: '#1f2937' }}>
                    View Reports
                  </h3>
                  <p style={{ margin: '4px 0 0 0', fontSize: '14px', color: '#6b7280' }}>
                    Analytics and insights
                  </p>
                </button>

                <button
                  onClick={() => handleQuickAction('new-project')}
                  style={{
                    padding: '20px',
                    backgroundColor: '#f8fafc',
                    border: '2px solid #e2e8f0',
                    borderRadius: '12px',
                    cursor: 'pointer',
                    textAlign: 'left',
                    transition: 'all 0.2s'
                  }}
                  onMouseEnter={(e) => {
                    e.currentTarget.style.borderColor = '#06b6d4'
                    e.currentTarget.style.backgroundColor = '#f0fdfa'
                  }}
                  onMouseLeave={(e) => {
                    e.currentTarget.style.borderColor = '#e2e8f0'
                    e.currentTarget.style.backgroundColor = '#f8fafc'
                  }}
                >
                  <div style={{ fontSize: '32px', marginBottom: '8px' }}>📁</div>
                  <h3 style={{ margin: 0, fontSize: '16px', fontWeight: '600', color: '#1f2937' }}>
                    New Project
                  </h3>
                  <p style={{ margin: '4px 0 0 0', fontSize: '14px', color: '#6b7280' }}>
                    Create and manage projects
                  </p>
                </button>

                <button
                  onClick={() => handleQuickAction('upload-files')}
                  style={{
                    padding: '20px',
                    backgroundColor: '#f8fafc',
                    border: '2px solid #e2e8f0',
                    borderRadius: '12px',
                    cursor: 'pointer',
                    textAlign: 'left',
                    transition: 'all 0.2s'
                  }}
                  onMouseEnter={(e) => {
                    e.currentTarget.style.borderColor = '#84cc16'
                    e.currentTarget.style.backgroundColor = '#f7fee7'
                  }}
                  onMouseLeave={(e) => {
                    e.currentTarget.style.borderColor = '#e2e8f0'
                    e.currentTarget.style.backgroundColor = '#f8fafc'
                  }}
                >
                  <div style={{ fontSize: '32px', marginBottom: '8px' }}>📂</div>
                  <h3 style={{ margin: 0, fontSize: '16px', fontWeight: '600', color: '#1f2937' }}>
                    Upload Files
                  </h3>
                  <p style={{ margin: '4px 0 0 0', fontSize: '14px', color: '#6b7280' }}>
                    Manage documents and files
                  </p>
                </button>

                <button
                  onClick={() => handleQuickAction('ai-assistant')}
                  style={{
                    padding: '20px',
                    backgroundColor: '#f8fafc',
                    border: '2px solid #e2e8f0',
                    borderRadius: '12px',
                    cursor: 'pointer',
                    textAlign: 'left',
                    transition: 'all 0.2s'
                  }}
                  onMouseEnter={(e) => {
                    e.currentTarget.style.borderColor = '#a855f7'
                    e.currentTarget.style.backgroundColor = '#faf5ff'
                  }}
                  onMouseLeave={(e) => {
                    e.currentTarget.style.borderColor = '#e2e8f0'
                    e.currentTarget.style.backgroundColor = '#f8fafc'
                  }}
                >
                  <div style={{ fontSize: '32px', marginBottom: '8px' }}>🤖</div>
                  <h3 style={{ margin: 0, fontSize: '16px', fontWeight: '600', color: '#1f2937' }}>
                    AI Assistant
                  </h3>
                  <p style={{ margin: '4px 0 0 0', fontSize: '14px', color: '#6b7280' }}>
                    Get intelligent help
                  </p>
                </button>

                <button
                  onClick={loadDashboardData}
                  style={{
                    padding: '20px',
                    backgroundColor: '#f8fafc',
                    border: '2px solid #e2e8f0',
                    borderRadius: '12px',
                    cursor: 'pointer',
                    textAlign: 'left',
                    transition: 'all 0.2s'
                  }}
                  onMouseEnter={(e) => {
                    e.currentTarget.style.borderColor = '#6b7280'
                    e.currentTarget.style.backgroundColor = '#f9fafb'
                  }}
                  onMouseLeave={(e) => {
                    e.currentTarget.style.borderColor = '#e2e8f0'
                    e.currentTarget.style.backgroundColor = '#f8fafc'
                  }}
                >
                  <div style={{ fontSize: '32px', marginBottom: '8px' }}>🔄</div>
                  <h3 style={{ margin: 0, fontSize: '16px', fontWeight: '600', color: '#1f2937' }}>
                    Refresh Data
                  </h3>
                  <p style={{ margin: '4px 0 0 0', fontSize: '14px', color: '#6b7280' }}>
                    Update dashboard content
                  </p>
                </button>
              </div>
            </div>

            {/* Manager Dashboard - Department Logs and Activity */}
            {user.role === 'manager' && (
              <>
                {/* Department Stats Row */}
                <div style={{
                  display: 'grid',
                  gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))',
                  gap: '20px',
                  marginBottom: '32px'
                }}>
                  <div style={{
                    backgroundColor: '#1a1a1a',
                    padding: '24px',
                    borderRadius: '16px',
                    boxShadow: `
                      12px 12px 24px #0a0a0a,
                      -12px -12px 24px #2a2a2a,
                      inset 2px 2px 6px rgba(239, 68, 68, 0.1)
                    `,
                    border: '1px solid rgba(239, 68, 68, 0.1)',
                    textAlign: 'center'
                  }}>
                    <div style={{ fontSize: '32px', marginBottom: '8px' }}>👥</div>
                    <div style={{ fontSize: '24px', fontWeight: 'bold', color: '#8B0000', marginBottom: '4px' }}>
                      {state.departmentStats.totalEmployees}
                    </div>
                    <div style={{ fontSize: '14px', color: '#9ca3af' }}>TOTAL EMPLOYEES</div>
                  </div>

                  <div style={{
                    backgroundColor: '#1a1a1a',
                    padding: '24px',
                    borderRadius: '16px',
                    boxShadow: `
                      12px 12px 24px #0a0a0a,
                      -12px -12px 24px #2a2a2a,
                      inset 2px 2px 6px rgba(34, 197, 94, 0.1)
                    `,
                    border: '1px solid rgba(34, 197, 94, 0.1)',
                    textAlign: 'center'
                  }}>
                    <div style={{ fontSize: '32px', marginBottom: '8px' }}>✅</div>
                    <div style={{ fontSize: '24px', fontWeight: 'bold', color: '#22c55e', marginBottom: '4px' }}>
                      {state.departmentStats.activeToday}
                    </div>
                    <div style={{ fontSize: '14px', color: '#9ca3af' }}>ACTIVE TODAY</div>
                  </div>

                  <div style={{
                    backgroundColor: '#1a1a1a',
                    padding: '24px',
                    borderRadius: '16px',
                    boxShadow: `
                      12px 12px 24px #0a0a0a,
                      -12px -12px 24px #2a2a2a,
                      inset 2px 2px 6px rgba(59, 130, 246, 0.1)
                    `,
                    border: '1px solid rgba(59, 130, 246, 0.1)',
                    textAlign: 'center'
                  }}>
                    <div style={{ fontSize: '32px', marginBottom: '8px' }}>⏰</div>
                    <div style={{ fontSize: '24px', fontWeight: 'bold', color: '#3b82f6', marginBottom: '4px' }}>
                      {state.departmentStats.totalHoursToday.toFixed(1)}
                    </div>
                    <div style={{ fontSize: '14px', color: '#9ca3af' }}>TOTAL HOURS TODAY</div>
                  </div>

                  <div style={{
                    backgroundColor: '#1a1a1a',
                    padding: '24px',
                    borderRadius: '16px',
                    boxShadow: `
                      12px 12px 24px #0a0a0a,
                      -12px -12px 24px #2a2a2a,
                      inset 2px 2px 6px rgba(168, 85, 247, 0.1)
                    `,
                    border: '1px solid rgba(168, 85, 247, 0.1)',
                    textAlign: 'center'
                  }}>
                    <div style={{ fontSize: '32px', marginBottom: '8px' }}>📊</div>
                    <div style={{ fontSize: '24px', fontWeight: 'bold', color: '#a855f7', marginBottom: '4px' }}>
                      {state.departmentStats.averageHoursPerEmployee.toFixed(1)}
                    </div>
                    <div style={{ fontSize: '14px', color: '#9ca3af' }}>AVG HOURS/EMPLOYEE</div>
                  </div>
                </div>

                {/* Department Logs and Activity */}
                <div style={{
                  display: 'grid',
                  gridTemplateColumns: 'repeat(auto-fit, minmax(500px, 1fr))',
                  gap: '24px',
                  marginBottom: '32px'
                }}>
                  {/* Department Time Logs */}
                  <div style={{
                    backgroundColor: '#1a1a1a',
                    borderRadius: '16px',
                    padding: '24px',
                    boxShadow: `
                      12px 12px 24px #0a0a0a,
                      -12px -12px 24px #2a2a2a,
                      inset 2px 2px 6px rgba(239, 68, 68, 0.1)
                    `,
                    border: '1px solid rgba(239, 68, 68, 0.1)'
                  }}>
                    <h2 style={{
                      fontSize: '20px',
                      fontWeight: 'bold',
                      color: '#8B0000',
                      marginBottom: '20px',
                      display: 'flex',
                      alignItems: 'center',
                      gap: '8px'
                    }}>
                      🕐 DEPARTMENT TIME LOGS
                    </h2>

                    <div style={{
                      maxHeight: '400px',
                      overflowY: 'auto',
                      display: 'flex',
                      flexDirection: 'column',
                      gap: '12px'
                    }}>
                      {state.departmentLogs.length > 0 ? (
                        state.departmentLogs.slice(0, 10).map((log, index) => (
                          <div
                            key={log.id || index}
                            style={{
                              padding: '16px',
                              backgroundColor: '#2a2a2a',
                              borderRadius: '12px',
                              border: '1px solid rgba(239, 68, 68, 0.1)',
                              display: 'flex',
                              justifyContent: 'space-between',
                              alignItems: 'center'
                            }}
                          >
                            <div>
                              <div style={{
                                fontWeight: '600',
                                color: '#8B0000',
                                marginBottom: '4px'
                              }}>
                                {log.userName}
                              </div>
                              <div style={{
                                fontSize: '14px',
                                color: '#9ca3af',
                                display: 'flex',
                                alignItems: 'center',
                                gap: '8px'
                              }}>
                                <span style={{
                                  padding: '2px 8px',
                                  borderRadius: '12px',
                                  fontSize: '12px',
                                  fontWeight: '600',
                                  backgroundColor: log.action === 'clock-in' ? '#22c55e' :
                                                 log.action === 'clock-out' ? '#ef4444' : '#f59e0b',
                                  color: '#1a1a1a'
                                }}>
                                  {log.action.toUpperCase()}
                                </span>
                                {new Date(log.timestamp).toLocaleString()}
                              </div>
                            </div>
                          </div>
                        ))
                      ) : (
                        <div style={{
                          textAlign: 'center',
                          color: '#9ca3af',
                          padding: '40px',
                          fontSize: '16px'
                        }}>
                          NO TIME LOGS TODAY
                        </div>
                      )}
                    </div>
                  </div>

                  {/* Department Activity */}
                  <div style={{
                    backgroundColor: '#1a1a1a',
                    borderRadius: '16px',
                    padding: '24px',
                    boxShadow: `
                      12px 12px 24px #0a0a0a,
                      -12px -12px 24px #2a2a2a,
                      inset 2px 2px 6px rgba(34, 197, 94, 0.1)
                    `,
                    border: '1px solid rgba(34, 197, 94, 0.1)'
                  }}>
                    <h2 style={{
                      fontSize: '20px',
                      fontWeight: 'bold',
                      color: '#8B0000',
                      marginBottom: '20px',
                      display: 'flex',
                      alignItems: 'center',
                      gap: '8px'
                    }}>
                      📋 DEPARTMENT ACTIVITY
                    </h2>

                    <div style={{
                      maxHeight: '400px',
                      overflowY: 'auto',
                      display: 'flex',
                      flexDirection: 'column',
                      gap: '12px'
                    }}>
                      {state.departmentActivity.length > 0 ? (
                        state.departmentActivity.slice(0, 10).map((activity, index) => (
                          <div
                            key={activity.id || index}
                            style={{
                              padding: '16px',
                              backgroundColor: '#2a2a2a',
                              borderRadius: '12px',
                              border: '1px solid rgba(34, 197, 94, 0.1)'
                            }}
                          >
                            <div style={{
                              display: 'flex',
                              justifyContent: 'space-between',
                              alignItems: 'flex-start',
                              marginBottom: '8px'
                            }}>
                              <div style={{
                                fontWeight: '600',
                                color: '#8B0000'
                              }}>
                                {activity.userName}
                              </div>
                              <div style={{
                                fontSize: '12px',
                                color: '#9ca3af'
                              }}>
                                {new Date(activity.timestamp).toLocaleTimeString()}
                              </div>
                            </div>
                            <div style={{
                              fontSize: '14px',
                              color: '#22c55e',
                              fontWeight: '600',
                              marginBottom: '4px'
                            }}>
                              {activity.action}
                            </div>
                            <div style={{
                              fontSize: '14px',
                              color: '#9ca3af'
                            }}>
                              {activity.description}
                            </div>
                          </div>
                        ))
                      ) : (
                        <div style={{
                          textAlign: 'center',
                          color: '#9ca3af',
                          padding: '40px',
                          fontSize: '16px'
                        }}>
                          NO RECENT ACTIVITY
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              </>
            )}

            {/* Recent Activity Grid */}
            <div style={{
              display: 'grid',
              gridTemplateColumns: 'repeat(auto-fit, minmax(400px, 1fr))',
              gap: '24px'
            }}>
              {/* Recent Tasks */}
              <div style={{
                backgroundColor: '#2a2a2a',
                borderRadius: '12px',
                padding: '24px',
                boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)'
              }}>
                <h2 style={{ 
                  fontSize: '18px', 
                  fontWeight: 'bold', 
                  color: '#1f2937', 
                  marginBottom: '20px' 
                }}>
                  📋 Recent Tasks
                </h2>
                
                {state.recentTasks.length > 0 ? (
                  <div style={{ display: 'flex', flexDirection: 'column', gap: '12px' }}>
                    {state.recentTasks.slice(0, 5).map((task, index) => (
                      <div
                        key={task.id || index}
                        style={{
                          padding: '16px',
                          backgroundColor: '#f9fafb',
                          borderRadius: '8px',
                          border: '1px solid #e5e7eb',
                          display: 'flex',
                          justifyContent: 'space-between',
                          alignItems: 'center'
                        }}
                      >
                        <div>
                          <h4 style={{ 
                            margin: '0 0 4px 0', 
                            fontSize: '14px', 
                            fontWeight: '600',
                            color: '#1f2937' 
                          }}>
                            {task.title}
                          </h4>
                          <p style={{ 
                            margin: 0, 
                            fontSize: '12px', 
                            color: '#6b7280' 
                          }}>
                            Status: {task.status} • Priority: {task.priority}
                          </p>
                        </div>
                        <span style={{
                          padding: '4px 8px',
                          borderRadius: '12px',
                          fontSize: '12px',
                          fontWeight: '500',
                          backgroundColor: 
                            task.priority === 'high' ? '#fee2e2' :
                            task.priority === 'medium' ? '#fef3c7' : '#d1fae5',
                          color:
                            task.priority === 'high' ? '#991b1b' :
                            task.priority === 'medium' ? '#92400e' : '#065f46'
                        }}>
                          {task.priority}
                        </span>
                      </div>
                    ))}
                  </div>
                ) : (
                  <div style={{ 
                    textAlign: 'center', 
                    color: '#6b7280', 
                    fontStyle: 'italic',
                    padding: '40px 0'
                  }}>
                    No recent tasks found
                  </div>
                )}
              </div>

              {/* Recent Projects */}
              <div style={{
                backgroundColor: '#2a2a2a',
                borderRadius: '12px',
                padding: '24px',
                boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)'
              }}>
                <h2 style={{ 
                  fontSize: '18px', 
                  fontWeight: 'bold', 
                  color: '#1f2937', 
                  marginBottom: '20px' 
                }}>
                  📁 Active Projects
                </h2>
                
                {state.recentProjects.length > 0 ? (
                  <div style={{ display: 'flex', flexDirection: 'column', gap: '12px' }}>
                    {state.recentProjects.slice(0, 3).map((project, index) => (
                      <div
                        key={project.id || index}
                        style={{
                          padding: '16px',
                          backgroundColor: '#f9fafb',
                          borderRadius: '8px',
                          border: '1px solid #e5e7eb'
                        }}
                      >
                        <h4 style={{ 
                          margin: '0 0 8px 0', 
                          fontSize: '14px', 
                          fontWeight: '600',
                          color: '#1f2937' 
                        }}>
                          {project.name}
                        </h4>
                        {project.description && (
                          <p style={{ 
                            margin: '0 0 8px 0', 
                            fontSize: '12px', 
                            color: '#6b7280' 
                          }}>
                            {project.description}
                          </p>
                        )}
                        <div style={{ 
                          display: 'flex', 
                          justifyContent: 'space-between',
                          alignItems: 'center'
                        }}>
                          <span style={{
                            fontSize: '12px',
                            color: '#6b7280'
                          }}>
                            Status: {project.status}
                          </span>
                          <span style={{
                            padding: '2px 8px',
                            borderRadius: '8px',
                            fontSize: '10px',
                            fontWeight: '500',
                            backgroundColor: '#dbeafe',
                            color: '#1e40af'
                          }}>
                            Active
                          </span>
                        </div>
                      </div>
                    ))}
                  </div>
                ) : (
                  <div style={{ 
                    textAlign: 'center', 
                    color: '#6b7280', 
                    fontStyle: 'italic',
                    padding: '40px 0'
                  }}>
                    No active projects found
                  </div>
                )}
              </div>
            </div>
          </>
        )}
      </main>

      {/* CSS Animations */}
      <style>{`
        @keyframes spin {
          0% { transform: rotate(0deg); }
          100% { transform: rotate(360deg); }
        }

        @keyframes float {
          0%, 100% {
            transform: translateY(0px) rotate(0deg);
            opacity: 0.7;
          }
          50% {
            transform: translateY(-20px) rotate(180deg);
            opacity: 1;
          }
        }
      `}</style>

      {/* GitHub Integration */}
      <GitHubIntegration
        isOpen={isGitHubOpen}
        onClose={() => setIsGitHubOpen(false)}
      />

      {/* AI Assistant */}
      <AIAssistant
        isOpen={isAIOpen}
        onClose={() => setIsAIOpen(false)}
      />
    </div>
  )
}
