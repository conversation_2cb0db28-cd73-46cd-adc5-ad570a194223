import React, { useState, useEffect } from 'react';

interface AIMessage {
  id: string;
  type: 'user' | 'ai';
  content: string;
  timestamp: string;
  suggestions?: string[];
  insights?: string[];
}

interface AIAssistantProps {
  isOpen: boolean;
  onClose: () => void;
}

const AIAssistant: React.FC<AIAssistantProps> = ({ isOpen, onClose }) => {
  const [messages, setMessages] = useState<AIMessage[]>([
    {
      id: '1',
      type: 'ai',
      content: 'Hello! I\'m your AI assistant for CTNL WORK-BOARD. I can help you with task management, productivity insights, and work optimization. How can I assist you today?',
      timestamp: new Date().toISOString(),
      suggestions: ['Create Task', 'Project Status', 'Productivity Tips', 'Schedule Optimization']
    }
  ]);
  const [inputMessage, setInputMessage] = useState('');
  const [isLoading, setIsLoading] = useState(false);

  const sendMessage = async () => {
    if (!inputMessage.trim()) return;

    const userMessage: AIMessage = {
      id: Date.now().toString(),
      type: 'user',
      content: inputMessage,
      timestamp: new Date().toISOString()
    };

    setMessages(prev => [...prev, userMessage]);
    setInputMessage('');
    setIsLoading(true);

    try {
      const response = await fetch('http://localhost:8002/api/ai/chat', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          message: inputMessage,
          conversation_id: 'dashboard'
        })
      });

      const data = await response.json();

      const aiMessage: AIMessage = {
        id: (Date.now() + 1).toString(),
        type: 'ai',
        content: data.ai_response || 'I apologize, but I\'m having trouble processing your request right now.',
        timestamp: new Date().toISOString(),
        suggestions: data.suggestions || [],
        insights: data.insights || []
      };

      setMessages(prev => [...prev, aiMessage]);
    } catch (error) {
      console.error('AI Chat error:', error);
      const errorMessage: AIMessage = {
        id: (Date.now() + 1).toString(),
        type: 'ai',
        content: 'I\'m experiencing technical difficulties. Please try again in a moment.',
        timestamp: new Date().toISOString()
      };
      setMessages(prev => [...prev, errorMessage]);
    } finally {
      setIsLoading(false);
    }
  };

  const handleSuggestionClick = (suggestion: string) => {
    setInputMessage(suggestion);
  };

  if (!isOpen) return null;

  return (
    <div style={{
      position: 'fixed',
      top: 0,
      left: 0,
      right: 0,
      bottom: 0,
      backgroundColor: 'rgba(0, 0, 0, 0.8)',
      zIndex: 1000,
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center'
    }}>
      <div style={{
        backgroundColor: '#1a1a1a',
        borderRadius: '16px',
        width: '90%',
        maxWidth: '600px',
        height: '80%',
        maxHeight: '700px',
        border: '2px solid #8B0000',
        boxShadow: '0 20px 40px rgba(139, 0, 0, 0.3)',
        display: 'flex',
        flexDirection: 'column',
        overflow: 'hidden'
      }}>
        {/* Header */}
        <div style={{
          padding: '20px',
          borderBottom: '1px solid #8B0000',
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
          background: 'linear-gradient(135deg, #2a2a2a, #1a1a1a)'
        }}>
          <h2 style={{
            margin: 0,
            color: '#8B0000',
            fontSize: '20px',
            fontWeight: 'bold'
          }}>
            🤖 CTNL AI Assistant
          </h2>
          <button
            onClick={onClose}
            style={{
              background: 'none',
              border: 'none',
              color: '#8B0000',
              fontSize: '24px',
              cursor: 'pointer',
              padding: '4px'
            }}
          >
            ✕
          </button>
        </div>

        {/* Messages */}
        <div style={{
          flex: 1,
          padding: '20px',
          overflowY: 'auto',
          display: 'flex',
          flexDirection: 'column',
          gap: '16px'
        }}>
          {messages.map((message) => (
            <div
              key={message.id}
              style={{
                display: 'flex',
                flexDirection: message.type === 'user' ? 'row-reverse' : 'row',
                gap: '12px'
              }}
            >
              <div style={{
                width: '32px',
                height: '32px',
                borderRadius: '50%',
                backgroundColor: message.type === 'user' ? '#8B0000' : '#DC143C',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                fontSize: '16px',
                flexShrink: 0
              }}>
                {message.type === 'user' ? '👤' : '🤖'}
              </div>
              <div style={{
                backgroundColor: message.type === 'user' ? '#2a2a2a' : '#1f1f1f',
                padding: '12px 16px',
                borderRadius: '12px',
                maxWidth: '70%',
                border: `1px solid ${message.type === 'user' ? '#8B0000' : '#DC143C'}`
              }}>
                <p style={{
                  margin: 0,
                  color: '#ffffff',
                  fontSize: '14px',
                  lineHeight: '1.5'
                }}>
                  {message.content}
                </p>
                
                {message.suggestions && message.suggestions.length > 0 && (
                  <div style={{ marginTop: '12px' }}>
                    <p style={{
                      margin: '0 0 8px 0',
                      color: '#8B0000',
                      fontSize: '12px',
                      fontWeight: 'bold'
                    }}>
                      Suggestions:
                    </p>
                    <div style={{ display: 'flex', flexWrap: 'wrap', gap: '6px' }}>
                      {message.suggestions.map((suggestion, index) => (
                        <button
                          key={index}
                          onClick={() => handleSuggestionClick(suggestion)}
                          style={{
                            backgroundColor: '#8B0000',
                            color: '#ffffff',
                            border: 'none',
                            borderRadius: '6px',
                            padding: '4px 8px',
                            fontSize: '12px',
                            cursor: 'pointer'
                          }}
                        >
                          {suggestion}
                        </button>
                      ))}
                    </div>
                  </div>
                )}

                {message.insights && message.insights.length > 0 && (
                  <div style={{ marginTop: '12px' }}>
                    <p style={{
                      margin: '0 0 8px 0',
                      color: '#DC143C',
                      fontSize: '12px',
                      fontWeight: 'bold'
                    }}>
                      Insights:
                    </p>
                    <ul style={{
                      margin: 0,
                      paddingLeft: '16px',
                      color: '#9ca3af',
                      fontSize: '12px'
                    }}>
                      {message.insights.map((insight, index) => (
                        <li key={index}>{insight}</li>
                      ))}
                    </ul>
                  </div>
                )}
              </div>
            </div>
          ))}
          
          {isLoading && (
            <div style={{
              display: 'flex',
              gap: '12px'
            }}>
              <div style={{
                width: '32px',
                height: '32px',
                borderRadius: '50%',
                backgroundColor: '#DC143C',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                fontSize: '16px'
              }}>
                🤖
              </div>
              <div style={{
                backgroundColor: '#1f1f1f',
                padding: '12px 16px',
                borderRadius: '12px',
                border: '1px solid #DC143C'
              }}>
                <p style={{
                  margin: 0,
                  color: '#8B0000',
                  fontSize: '14px'
                }}>
                  AI is thinking...
                </p>
              </div>
            </div>
          )}
        </div>

        {/* Input */}
        <div style={{
          padding: '20px',
          borderTop: '1px solid #8B0000',
          display: 'flex',
          gap: '12px'
        }}>
          <input
            type="text"
            value={inputMessage}
            onChange={(e) => setInputMessage(e.target.value)}
            onKeyPress={(e) => e.key === 'Enter' && sendMessage()}
            placeholder="Ask me anything about your work..."
            style={{
              flex: 1,
              padding: '12px',
              backgroundColor: '#2a2a2a',
              border: '1px solid #8B0000',
              borderRadius: '8px',
              color: '#ffffff',
              fontSize: '14px'
            }}
          />
          <button
            onClick={sendMessage}
            disabled={!inputMessage.trim() || isLoading}
            style={{
              backgroundColor: '#8B0000',
              color: '#ffffff',
              border: 'none',
              borderRadius: '8px',
              padding: '12px 20px',
              fontSize: '14px',
              fontWeight: 'bold',
              cursor: 'pointer',
              opacity: (!inputMessage.trim() || isLoading) ? 0.5 : 1
            }}
          >
            Send
          </button>
        </div>
      </div>
    </div>
  );
};

export default AIAssistant;
