"""
WebSocket Event Handlers
"""
import logging
from typing import Dict, Any
from datetime import datetime
from ..services.websocket_service import websocket_service

logger = logging.getLogger(__name__)

# Get the Socket.IO server instance
sio = websocket_service.sio

@sio.event
async def connect(sid: str, environ: Dict[str, Any], auth: Dict[str, Any]):
    """Handle client connection"""
    return await websocket_service.handle_connect(sid, environ, auth)

@sio.event
async def disconnect(sid: str):
    """Handle client disconnection"""
    await websocket_service.handle_disconnect(sid)

@sio.event
async def join_room(sid: str, data: Dict[str, Any]):
    """Handle room join requests"""
    await websocket_service.handle_join_room(sid, data)

@sio.event
async def leave_room(sid: str, data: Dict[str, Any]):
    """Handle room leave requests"""
    await websocket_service.handle_leave_room(sid, data)

@sio.event
async def ping(sid: str, data: Dict[str, Any]):
    """Handle ping requests for connection health check"""
    try:
        await sio.emit('pong', {
            'message': 'pong',
            'timestamp': data.get('timestamp'),
            'server_time': str(datetime.utcnow().isoformat())
        }, room=sid)
    except Exception as e:
        logger.error(f"Ping handler error: {e}")

@sio.event
async def user_typing(sid: str, data: Dict[str, Any]):
    """Handle user typing indicators"""
    try:
        if sid not in websocket_service.connected_users:
            return
            
        user_data = websocket_service.connected_users[sid]
        room = data.get('room')
        
        if room:
            # Broadcast typing indicator to room (excluding sender)
            await sio.emit('user_typing', {
                'user_id': user_data['user_id'],
                'username': user_data['username'],
                'room': room,
                'typing': data.get('typing', True)
            }, room=room, skip_sid=sid)
            
    except Exception as e:
        logger.error(f"User typing handler error: {e}")

@sio.event
async def request_online_users(sid: str, data: Dict[str, Any]):
    """Handle requests for online users list"""
    try:
        if sid not in websocket_service.connected_users:
            return
            
        # Get list of online users
        online_users = []
        for session_id, user_data in websocket_service.connected_users.items():
            online_users.append({
                'user_id': user_data['user_id'],
                'username': user_data['username'],
                'role': user_data['role'],
                'department': user_data['department']
            })
        
        await sio.emit('online_users_list', {
            'users': online_users,
            'count': len(online_users)
        }, room=sid)
        
    except Exception as e:
        logger.error(f"Online users request handler error: {e}")

@sio.event
async def send_direct_message(sid: str, data: Dict[str, Any]):
    """Handle direct messages between users"""
    try:
        if sid not in websocket_service.connected_users:
            return
            
        sender_data = websocket_service.connected_users[sid]
        target_user_id = data.get('target_user_id')
        message = data.get('message')
        
        if not target_user_id or not message:
            return
            
        # Send message to target user
        target_room = f"user_{target_user_id}"
        await sio.emit('direct_message', {
            'from_user_id': sender_data['user_id'],
            'from_username': sender_data['username'],
            'message': message,
            'timestamp': datetime.utcnow().isoformat()
        }, room=target_room)
        
        # Send confirmation to sender
        await sio.emit('message_sent', {
            'to_user_id': target_user_id,
            'message': message,
            'timestamp': datetime.utcnow().isoformat()
        }, room=sid)
        
    except Exception as e:
        logger.error(f"Direct message handler error: {e}")

@sio.event
async def update_user_status(sid: str, data: Dict[str, Any]):
    """Handle user status updates"""
    try:
        if sid not in websocket_service.connected_users:
            return
            
        user_data = websocket_service.connected_users[sid]
        status = data.get('status')  # online, away, busy, offline
        
        if status:
            # Update user status in connected users
            websocket_service.connected_users[sid]['status'] = status
            
            # Broadcast status update to relevant rooms
            user_id = user_data['user_id']
            department = user_data.get('department')
            
            status_update = {
                'user_id': user_id,
                'username': user_data['username'],
                'status': status,
                'timestamp': datetime.utcnow().isoformat()
            }
            
            # Broadcast to department room
            if department:
                await sio.emit('user_status_update', status_update, 
                             room=f"department_{department}")
            
    except Exception as e:
        logger.error(f"User status update handler error: {e}")
