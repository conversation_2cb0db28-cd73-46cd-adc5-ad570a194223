"""
Dashboard API routes
"""
from fastapi import APIRouter, Depends, HTTPException, status
from motor.motor_asyncio import AsyncIOMotorDatabase
from typing import Dict, Any, List
from datetime import datetime, timedelta
from bson import ObjectId

from ..core.database import get_database
from ..core.security import get_current_user_token
from ..core.config import Collections, UserRoles

router = APIRouter()


@router.get("/stats")
async def get_dashboard_stats(
    current_user: Dict[str, Any] = Depends(get_current_user_token),
    db: AsyncIOMotorDatabase = Depends(get_database)
):
    """Get dashboard statistics based on user role"""
    user_id = current_user.get("sub")
    user_role = current_user.get("role")
    user_department = current_user.get("department")
    
    try:
        stats = {}
        
        if user_role == UserRoles.ADMIN:
            # Admin gets system-wide statistics
            stats = await get_admin_stats(db)
        elif user_role == UserRoles.MANAGER:
            # Manager gets department/team statistics
            stats = await get_manager_stats(db, user_department, user_id)
        elif user_role == UserRoles.STAFF:
            # Staff gets personal statistics
            stats = await get_staff_stats(db, user_id)
        elif user_role == UserRoles.ACCOUNTANT:
            # Accountant gets financial statistics
            stats = await get_accountant_stats(db, user_department)
        elif user_role == UserRoles.HR:
            # HR gets employee-related statistics
            stats = await get_hr_stats(db)
        else:
            # Default staff-level stats
            stats = await get_staff_stats(db, user_id)
        
        return stats
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve dashboard statistics"
        )


async def get_admin_stats(db: AsyncIOMotorDatabase) -> Dict[str, Any]:
    """Get admin dashboard statistics"""
    # User statistics
    total_users = await db[Collections.USERS].count_documents({})
    active_users = await db[Collections.USERS].count_documents({"is_active": True})
    online_users = await db[Collections.USERS].count_documents({"is_online": True})
    
    # Project statistics
    total_projects = await db[Collections.PROJECTS].count_documents({})
    active_projects = await db[Collections.PROJECTS].count_documents({"status": "active"})
    
    # Task statistics
    pending_tasks = await db[Collections.TASKS].count_documents({"status": "pending"})
    completed_tasks = await db[Collections.TASKS].count_documents({"status": "completed"})
    
    # Approval statistics
    pending_approvals = await db[Collections.APPROVALS].count_documents({"status": "pending"})
    
    # Recent activities
    recent_activities = await db[Collections.AUDIT_LOGS].find({}).sort("timestamp", -1).limit(10).to_list(length=None)
    
    # Format activities
    formatted_activities = []
    for activity in recent_activities:
        user = await db[Collections.USERS].find_one({"_id": activity.get("user_id")})
        formatted_activities.append({
            "id": str(activity["_id"]),
            "type": activity.get("action", "unknown"),
            "title": activity.get("action", "").replace("_", " ").title(),
            "description": f"{activity.get('resource_type', '')} {activity.get('action', '')}",
            "user_name": f"{user['profile']['first_name']} {user['profile']['last_name']}" if user else "Unknown",
            "timestamp": activity.get("timestamp", datetime.utcnow()).isoformat()
        })
    
    return {
        "total_users": total_users,
        "active_users": active_users,
        "online_users": online_users,
        "total_projects": total_projects,
        "active_projects": active_projects,
        "pending_tasks": pending_tasks,
        "completed_tasks": completed_tasks,
        "pending_approvals": pending_approvals,
        "system_health": "healthy",
        "recent_activities": formatted_activities
    }


async def get_manager_stats(db: AsyncIOMotorDatabase, department: str, manager_id: str) -> Dict[str, Any]:
    """Get manager dashboard statistics"""
    # Team statistics
    team_members = await db[Collections.USERS].find({
        "department": department,
        "is_active": True
    }).to_list(length=None)
    
    total_members = len(team_members)
    online_members = len([m for m in team_members if m.get("is_online", False)])
    
    # Project statistics for department
    total_projects = await db[Collections.PROJECTS].count_documents({"department": department})
    active_projects = await db[Collections.PROJECTS].count_documents({
        "department": department,
        "status": "active"
    })
    completed_projects = await db[Collections.PROJECTS].count_documents({
        "department": department,
        "status": "completed"
    })
    
    # Task statistics for team
    team_member_ids = [m["_id"] for m in team_members]
    pending_tasks = await db[Collections.TASKS].count_documents({
        "assignee_id": {"$in": team_member_ids},
        "status": "pending"
    })
    in_progress_tasks = await db[Collections.TASKS].count_documents({
        "assignee_id": {"$in": team_member_ids},
        "status": "in_progress"
    })
    completed_tasks = await db[Collections.TASKS].count_documents({
        "assignee_id": {"$in": team_member_ids},
        "status": "completed"
    })
    
    # Approval statistics
    pending_approvals = await db[Collections.APPROVALS].count_documents({
        "approver_id": ObjectId(manager_id),
        "status": "pending"
    })
    
    # Format team members with current status
    formatted_team = []
    for member in team_members[:4]:  # Limit to 4 for dashboard
        # Get current task
        current_task = await db[Collections.TASKS].find_one({
            "assignee_id": member["_id"],
            "status": "in_progress"
        })
        
        # Get today's time logs
        today_start = datetime.utcnow().replace(hour=0, minute=0, second=0, microsecond=0)
        time_logs = await db[Collections.TIME_LOGS].find({
            "user_id": member["_id"],
            "clock_in": {"$gte": today_start}
        }).to_list(length=None)
        
        total_hours = sum([
            (log.get("duration", 0) / 3600) for log in time_logs if log.get("duration")
        ])
        
        formatted_team.append({
            "id": str(member["_id"]),
            "name": f"{member['profile']['first_name']} {member['profile']['last_name']}",
            "role": member.get("position", member.get("role", "Staff")),
            "status": "online" if member.get("is_online") else "offline",
            "current_task": current_task.get("title") if current_task else None,
            "hours_today": round(total_hours, 1)
        })
    
    return {
        "team_stats": {
            "total_members": total_members,
            "active_members": len([m for m in team_members if m.get("is_active", True)]),
            "online_members": online_members
        },
        "project_stats": {
            "total_projects": total_projects,
            "active_projects": active_projects,
            "completed_projects": completed_projects,
            "overdue_projects": 0  # TODO: Calculate based on due dates
        },
        "task_stats": {
            "total_tasks": pending_tasks + in_progress_tasks + completed_tasks,
            "pending_tasks": pending_tasks,
            "in_progress_tasks": in_progress_tasks,
            "completed_tasks": completed_tasks
        },
        "approval_stats": {
            "pending_approvals": pending_approvals,
            "leave_requests": 0,  # TODO: Count leave requests
            "procurement_requests": 0  # TODO: Count procurement requests
        },
        "team_members": formatted_team,
        "recent_activities": [],  # TODO: Get team activities
        "pending_approvals": []  # TODO: Get pending approvals
    }


async def get_staff_stats(db: AsyncIOMotorDatabase, user_id: str) -> Dict[str, Any]:
    """Get staff dashboard statistics"""
    user_obj_id = ObjectId(user_id)
    
    # Task statistics
    total_tasks = await db[Collections.TASKS].count_documents({"assignee_id": user_obj_id})
    pending_tasks = await db[Collections.TASKS].count_documents({
        "assignee_id": user_obj_id,
        "status": "pending"
    })
    in_progress_tasks = await db[Collections.TASKS].count_documents({
        "assignee_id": user_obj_id,
        "status": "in_progress"
    })
    completed_tasks = await db[Collections.TASKS].count_documents({
        "assignee_id": user_obj_id,
        "status": "completed"
    })
    
    # Time tracking statistics
    today_start = datetime.utcnow().replace(hour=0, minute=0, second=0, microsecond=0)
    week_start = today_start - timedelta(days=today_start.weekday())
    
    # Get today's time logs
    today_logs = await db[Collections.TIME_LOGS].find({
        "user_id": user_obj_id,
        "clock_in": {"$gte": today_start}
    }).to_list(length=None)
    
    # Get week's time logs
    week_logs = await db[Collections.TIME_LOGS].find({
        "user_id": user_obj_id,
        "clock_in": {"$gte": week_start}
    }).to_list(length=None)
    
    # Calculate hours
    today_hours = sum([
        (log.get("duration", 0) / 3600) for log in today_logs if log.get("duration")
    ])
    week_hours = sum([
        (log.get("duration", 0) / 3600) for log in week_logs if log.get("duration")
    ])
    
    # Check if currently clocked in
    is_clocked_in = any([log for log in today_logs if not log.get("clock_out")])
    last_clock_in = None
    if is_clocked_in:
        active_log = next((log for log in today_logs if not log.get("clock_out")), None)
        if active_log:
            last_clock_in = active_log.get("clock_in")
    
    # Project statistics
    user_projects = await db[Collections.PROJECTS].find({
        "team_members": user_obj_id
    }).to_list(length=None)
    
    total_projects = len(user_projects)
    active_projects = len([p for p in user_projects if p.get("status") == "active"])
    
    # Recent tasks
    recent_tasks = await db[Collections.TASKS].find({
        "assignee_id": user_obj_id
    }).sort("updated_at", -1).limit(5).to_list(length=None)
    
    formatted_tasks = []
    for task in recent_tasks:
        formatted_tasks.append({
            "id": str(task["_id"]),
            "title": task.get("title", ""),
            "status": task.get("status", "pending"),
            "due_date": task.get("due_date").isoformat() if task.get("due_date") else None,
            "priority": task.get("priority", "medium")
        })
    
    return {
        "my_tasks": {
            "total": total_tasks,
            "pending": pending_tasks,
            "in_progress": in_progress_tasks,
            "completed": completed_tasks
        },
        "time_tracking": {
            "today_hours": round(today_hours, 1),
            "week_hours": round(week_hours, 1),
            "is_clocked_in": is_clocked_in,
            "last_clock_in": last_clock_in.isoformat() if last_clock_in else None
        },
        "my_projects": {
            "total": total_projects,
            "active": active_projects
        },
        "leave_balance": {
            "annual": 15,  # TODO: Get from user profile or leave system
            "sick": 8
        },
        "recent_tasks": formatted_tasks,
        "upcoming_deadlines": []  # TODO: Get upcoming deadlines
    }


async def get_accountant_stats(db: AsyncIOMotorDatabase, department: str) -> Dict[str, Any]:
    """Get accountant dashboard statistics"""
    # TODO: Implement accountant-specific statistics
    return {
        "financial_overview": {},
        "pending_invoices": 0,
        "procurement_requests": 0,
        "budget_status": {}
    }


async def get_hr_stats(db: AsyncIOMotorDatabase) -> Dict[str, Any]:
    """Get HR dashboard statistics"""
    # TODO: Implement HR-specific statistics
    return {
        "employee_overview": {},
        "leave_requests": 0,
        "recruitment_status": {},
        "performance_metrics": {}
    }


@router.get("/activities")
async def get_recent_activities(
    current_user: Dict[str, Any] = Depends(get_current_user_token),
    db: AsyncIOMotorDatabase = Depends(get_database)
):
    """Get recent activities for the user"""
    user_role = current_user.get("role")
    user_department = current_user.get("department")
    
    try:
        # Build filter based on role
        filter_query = {}
        
        if user_role not in [UserRoles.ADMIN, UserRoles.HR]:
            # Non-admin users see only their department's activities
            filter_query["department"] = user_department
        
        activities = await db[Collections.AUDIT_LOGS].find(filter_query).sort("timestamp", -1).limit(20).to_list(length=None)
        
        formatted_activities = []
        for activity in activities:
            user = await db[Collections.USERS].find_one({"_id": activity.get("user_id")})
            formatted_activities.append({
                "id": str(activity["_id"]),
                "type": activity.get("action", "unknown"),
                "title": activity.get("action", "").replace("_", " ").title(),
                "description": f"{activity.get('resource_type', '')} {activity.get('action', '')}",
                "user_name": f"{user['profile']['first_name']} {user['profile']['last_name']}" if user else "Unknown",
                "timestamp": activity.get("timestamp", datetime.utcnow()).isoformat(),
                "metadata": activity.get("details", {})
            })
        
        return {"activities": formatted_activities}
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve activities"
        )


@router.get("/notifications")
async def get_notifications(
    current_user: Dict[str, Any] = Depends(get_current_user_token),
    db: AsyncIOMotorDatabase = Depends(get_database)
):
    """Get notifications for the current user"""
    user_id = current_user.get("sub")
    
    try:
        notifications = await db[Collections.NOTIFICATIONS].find({
            "user_id": ObjectId(user_id)
        }).sort("created_at", -1).limit(10).to_list(length=None)
        
        formatted_notifications = []
        for notification in notifications:
            formatted_notifications.append({
                "id": str(notification["_id"]),
                "type": notification.get("type", "info"),
                "title": notification.get("title", ""),
                "message": notification.get("message", ""),
                "is_read": notification.get("is_read", False),
                "created_at": notification.get("created_at", datetime.utcnow()).isoformat(),
                "data": notification.get("data", {})
            })
        
        return {"notifications": formatted_notifications}
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve notifications"
        )
