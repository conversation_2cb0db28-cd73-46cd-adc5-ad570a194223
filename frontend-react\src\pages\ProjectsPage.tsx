/**
 * Projects Management Page Component
 * Features: Create, manage, and track projects with team collaboration
 */
import React, { useState, useEffect, useCallback } from 'react'
import { logger, <PERSON>rror<PERSON><PERSON>ler, PerformanceMonitor } from '../utils/logger'
import type { User, Project, Task } from '../types'

interface ProjectsPageProps {
  user: User
  onNavigateBack?: () => void
}

interface ProjectsState {
  isLoading: boolean
  projects: Project[]
  filteredProjects: Project[]
  showCreateModal: boolean
  showViewModal: boolean
  selectedProject: Project | null
  newProject: {
    name: string
    description: string
    status: 'planning' | 'active' | 'on-hold' | 'completed' | 'cancelled'
    priority: 'low' | 'medium' | 'high'
    start_date: string
    end_date: string
    budget: number
    team_members: string[]
    tags: string[]
  }
  filters: {
    status: string
    priority: string
    search: string
  }
  projectTasks: { [projectId: string]: Task[] }
  error: string | null
}

export default function ProjectsPage({ user, onNavigateBack }: ProjectsPageProps) {
  const [state, setState] = useState<ProjectsState>({
    isLoading: true,
    projects: [],
    filteredProjects: [],
    showCreateModal: false,
    showViewModal: false,
    selectedProject: null,
    newProject: {
      name: '',
      description: '',
      status: 'planning',
      priority: 'medium',
      start_date: '',
      end_date: '',
      budget: 0,
      team_members: [],
      tags: []
    },
    filters: {
      status: 'all',
      priority: 'all',
      search: ''
    },
    projectTasks: {},
    error: null
  })

  useEffect(() => {
    logger.componentMount('ProjectsPage')
    loadProjectsData()
    
    return () => {
      logger.componentUnmount('ProjectsPage')
    }
  }, [])

  useEffect(() => {
    applyFilters()
  }, [state.projects, state.filters])

  const loadProjectsData = useCallback(async () => {
    try {
      setState(prev => ({ ...prev, isLoading: true, error: null }))
      logger.info('Loading projects data', 'ProjectsPage')
      
      PerformanceMonitor.startTimer('projects_load')

      const response = await fetch('http://localhost:8002/api/projects', {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('authToken')}`,
          'Content-Type': 'application/json'
        }
      })

      const duration = PerformanceMonitor.endTimer('projects_load')

      if (response.ok) {
        const data = await response.json()
        setState(prev => ({
          ...prev,
          isLoading: false,
          projects: data.projects || []
        }))

        logger.info(`Projects loaded in ${duration.toFixed(2)}ms`, 'ProjectsPage', {
          projectCount: data.projects?.length || 0
        })
      } else {
        // Mock data if API fails
        const mockProjects: Project[] = [
          {
            id: '1',
            name: 'Website Redesign',
            description: 'Complete redesign of company website with modern UI/UX',
            status: 'active',
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString()
          },
          {
            id: '2',
            name: 'Mobile App Development',
            description: 'Native mobile application for iOS and Android',
            status: 'planning',
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString()
          }
        ]

        setState(prev => ({
          ...prev,
          isLoading: false,
          projects: mockProjects
        }))

        logger.info('Using mock projects data', 'ProjectsPage')
      }
      
    } catch (error) {
      const handledError = ErrorHandler.handleApiError(error, 'ProjectsPage')
      setState(prev => ({ 
        ...prev, 
        isLoading: false, 
        error: handledError.message 
      }))
      logger.error('Failed to load projects data', 'ProjectsPage', error)
    }
  }, [])

  const applyFilters = useCallback(() => {
    let filtered = [...state.projects]

    if (state.filters.status !== 'all') {
      filtered = filtered.filter(project => project.status === state.filters.status)
    }
    if (state.filters.search) {
      const searchLower = state.filters.search.toLowerCase()
      filtered = filtered.filter(project => 
        project.name.toLowerCase().includes(searchLower) ||
        project.description?.toLowerCase().includes(searchLower)
      )
    }

    setState(prev => ({ ...prev, filteredProjects: filtered }))
  }, [state.projects, state.filters])

  const handleCreateProject = async () => {
    if (!state.newProject.name.trim()) {
      alert('Please enter a project name')
      return
    }

    try {
      setState(prev => ({ ...prev, isLoading: true }))
      
      const response = await fetch('http://localhost:8002/api/projects', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('authToken')}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(state.newProject)
      })

      if (response.ok) {
        const createdProject = await response.json()
        setState(prev => ({
          ...prev,
          projects: [createdProject, ...prev.projects],
          showCreateModal: false,
          isLoading: false
        }))
        
        // Reset form
        setState(prev => ({
          ...prev,
          newProject: {
            name: '',
            description: '',
            status: 'planning',
            priority: 'medium',
            start_date: '',
            end_date: '',
            budget: 0,
            team_members: [],
            tags: []
          }
        }))
        
        logger.info('Project created successfully', 'ProjectsPage', { projectId: createdProject.id })
      } else {
        throw new Error('Failed to create project')
      }
    } catch (error) {
      logger.error('Failed to create project', 'ProjectsPage', error)
      setState(prev => ({ ...prev, isLoading: false }))
      alert('Failed to create project. Please try again.')
    }
  }

  const loadProjectTasks = async (projectId: string) => {
    try {
      const response = await fetch(`http://localhost:8002/api/tasks?project_id=${projectId}`, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('authToken')}`,
          'Content-Type': 'application/json'
        }
      })

      if (response.ok) {
        const data = await response.json()
        setState(prev => ({
          ...prev,
          projectTasks: {
            ...prev.projectTasks,
            [projectId]: data.tasks || []
          }
        }))
      }
    } catch (error) {
      logger.error('Failed to load project tasks', 'ProjectsPage', error)
    }
  }

  const getStatusColor = (status: string): string => {
    switch (status) {
      case 'active': return '#10b981'
      case 'planning': return '#ff0000'
      case 'on-hold': return '#f59e0b'
      case 'completed': return '#ff0000'
      case 'cancelled': return '#ef4444'
      default: return '#6b7280'
    }
  }

  const getPriorityColor = (priority: string): string => {
    switch (priority) {
      case 'high': return '#ef4444'
      case 'medium': return '#f59e0b'
      case 'low': return '#10b981'
      default: return '#6b7280'
    }
  }

  if (state.error) {
    return (
      <div style={{
        minHeight: '100vh',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        backgroundColor: '#f9fafb',
        padding: '20px'
      }}>
        <div style={{
          maxWidth: '500px',
          width: '100%',
          backgroundColor: 'white',
          padding: '40px',
          borderRadius: '16px',
          boxShadow: '0 10px 15px -3px rgba(0, 0, 0, 0.1)',
          textAlign: 'center'
        }}>
          <div style={{ fontSize: '48px', marginBottom: '20px' }}>⚠️</div>
          <h2 style={{ color: '#ef4444', marginBottom: '16px' }}>Projects Error</h2>
          <p style={{ color: '#6b7280', marginBottom: '24px' }}>{state.error}</p>
          <button
            onClick={loadProjectsData}
            style={{
              padding: '12px 24px',
              backgroundColor: '#667eea',
              color: 'white',
              border: 'none',
              borderRadius: '8px',
              cursor: 'pointer',
              marginRight: '12px'
            }}
          >
            Try Again
          </button>
          <button
            onClick={onNavigateBack}
            style={{
              padding: '12px 24px',
              backgroundColor: '#6b7280',
              color: 'white',
              border: 'none',
              borderRadius: '8px',
              cursor: 'pointer'
            }}
          >
            Go Back
          </button>
        </div>
      </div>
    )
  }

  return (
    <div style={{ minHeight: '100vh', backgroundColor: '#f9fafb' }}>
      {/* Header */}
      <header style={{
        backgroundColor: 'white',
        boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1)',
        padding: '16px 0'
      }}>
        <div style={{
          maxWidth: '1400px',
          margin: '0 auto',
          padding: '0 24px',
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center'
        }}>
          <div style={{ display: 'flex', alignItems: 'center', gap: '16px' }}>
            <button
              onClick={onNavigateBack}
              style={{
                padding: '8px 12px',
                backgroundColor: '#f3f4f6',
                border: 'none',
                borderRadius: '6px',
                cursor: 'pointer',
                fontSize: '14px'
              }}
            >
              ← Back
            </button>
            <div>
              <h1 style={{ 
                margin: 0, 
                color: '#1f2937', 
                fontSize: '24px',
                fontWeight: 'bold'
              }}>
                📁 Projects Management
              </h1>
              <p style={{ 
                margin: '4px 0 0 0', 
                color: '#6b7280', 
                fontSize: '14px' 
              }}>
                Create and manage projects with your team
              </p>
            </div>
          </div>
          
          <button
            onClick={() => setState(prev => ({ ...prev, showCreateModal: true }))}
            style={{
              padding: '12px 20px',
              backgroundColor: '#667eea',
              color: 'white',
              border: 'none',
              borderRadius: '8px',
              cursor: 'pointer',
              fontSize: '14px',
              fontWeight: '500',
              display: 'flex',
              alignItems: 'center',
              gap: '8px'
            }}
          >
            ➕ New Project
          </button>
        </div>
      </header>

      {/* Main Content */}
      <main style={{ maxWidth: '1400px', margin: '0 auto', padding: '24px' }}>
        {/* Filters */}
        <div style={{
          backgroundColor: 'white',
          borderRadius: '12px',
          padding: '24px',
          marginBottom: '24px',
          boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)'
        }}>
          <h2 style={{ 
            fontSize: '18px', 
            fontWeight: 'bold', 
            color: '#1f2937', 
            marginBottom: '20px' 
          }}>
            🔍 Filters & Search
          </h2>
          
          <div style={{
            display: 'grid',
            gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))',
            gap: '16px'
          }}>
            <div>
              <label style={{ 
                display: 'block', 
                fontSize: '14px', 
                fontWeight: '500', 
                color: '#374151', 
                marginBottom: '6px' 
              }}>
                Status
              </label>
              <select
                value={state.filters.status}
                onChange={(e) => setState(prev => ({
                  ...prev,
                  filters: { ...prev.filters, status: e.target.value }
                }))}
                style={{
                  width: '100%',
                  padding: '8px 12px',
                  border: '1px solid #d1d5db',
                  borderRadius: '6px',
                  fontSize: '14px',
                  backgroundColor: 'white'
                }}
              >
                <option value="all">All Status</option>
                <option value="planning">Planning</option>
                <option value="active">Active</option>
                <option value="on-hold">On Hold</option>
                <option value="completed">Completed</option>
                <option value="cancelled">Cancelled</option>
              </select>
            </div>

            <div>
              <label style={{ 
                display: 'block', 
                fontSize: '14px', 
                fontWeight: '500', 
                color: '#374151', 
                marginBottom: '6px' 
              }}>
                Search
              </label>
              <input
                type="text"
                placeholder="Search projects..."
                value={state.filters.search}
                onChange={(e) => setState(prev => ({
                  ...prev,
                  filters: { ...prev.filters, search: e.target.value }
                }))}
                style={{
                  width: '100%',
                  padding: '8px 12px',
                  border: '1px solid #d1d5db',
                  borderRadius: '6px',
                  fontSize: '14px'
                }}
              />
            </div>
          </div>
        </div>

        {/* Projects Grid */}
        <div style={{
          display: 'grid',
          gridTemplateColumns: 'repeat(auto-fill, minmax(350px, 1fr))',
          gap: '24px'
        }}>
          {state.isLoading ? (
            <div style={{
              gridColumn: '1 / -1',
              display: 'flex',
              justifyContent: 'center',
              alignItems: 'center',
              height: '200px'
            }}>
              <div style={{
                width: '40px',
                height: '40px',
                border: '4px solid #e5e7eb',
                borderTop: '4px solid #667eea',
                borderRadius: '50%',
                animation: 'spin 1s linear infinite'
              }} />
            </div>
          ) : state.filteredProjects.length > 0 ? (
            state.filteredProjects.map((project) => (
              <div
                key={project.id}
                style={{
                  backgroundColor: 'white',
                  borderRadius: '12px',
                  padding: '24px',
                  boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)',
                  transition: 'all 0.2s',
                  cursor: 'pointer'
                }}
                onClick={() => {
                  setState(prev => ({ ...prev, selectedProject: project, showViewModal: true }))
                  loadProjectTasks(project.id)
                }}
                onMouseEnter={(e) => {
                  e.currentTarget.style.transform = 'translateY(-2px)'
                  e.currentTarget.style.boxShadow = '0 8px 15px -3px rgba(0, 0, 0, 0.1)'
                }}
                onMouseLeave={(e) => {
                  e.currentTarget.style.transform = 'translateY(0)'
                  e.currentTarget.style.boxShadow = '0 4px 6px -1px rgba(0, 0, 0, 0.1)'
                }}
              >
                <div style={{
                  display: 'flex',
                  justifyContent: 'space-between',
                  alignItems: 'flex-start',
                  marginBottom: '16px'
                }}>
                  <h3 style={{
                    margin: 0,
                    fontSize: '18px',
                    fontWeight: '600',
                    color: '#1f2937'
                  }}>
                    {project.name}
                  </h3>
                  
                  <span style={{
                    padding: '4px 12px',
                    borderRadius: '12px',
                    fontSize: '12px',
                    fontWeight: '500',
                    backgroundColor: getStatusColor(project.status) + '20',
                    color: getStatusColor(project.status)
                  }}>
                    {project.status}
                  </span>
                </div>
                
                <p style={{
                  margin: '0 0 16px 0',
                  fontSize: '14px',
                  color: '#6b7280',
                  lineHeight: '1.5'
                }}>
                  {project.description && project.description.length > 100 
                    ? project.description.substring(0, 100) + '...' 
                    : project.description || 'No description available'}
                </p>
                
                <div style={{
                  display: 'flex',
                  justifyContent: 'space-between',
                  alignItems: 'center',
                  fontSize: '12px',
                  color: '#6b7280'
                }}>
                  <span>Created: {new Date(project.created_at).toLocaleDateString()}</span>
                  <span>Updated: {new Date(project.updated_at).toLocaleDateString()}</span>
                </div>
              </div>
            ))
          ) : (
            <div style={{
              gridColumn: '1 / -1',
              textAlign: 'center',
              color: '#6b7280',
              fontStyle: 'italic',
              padding: '60px 0'
            }}>
              <div style={{ fontSize: '48px', marginBottom: '16px' }}>📁</div>
              <p>No projects found matching your criteria</p>
              <button
                onClick={() => setState(prev => ({ ...prev, showCreateModal: true }))}
                style={{
                  marginTop: '16px',
                  padding: '12px 20px',
                  backgroundColor: '#667eea',
                  color: 'white',
                  border: 'none',
                  borderRadius: '8px',
                  cursor: 'pointer',
                  fontSize: '14px'
                }}
              >
                Create Your First Project
              </button>
            </div>
          )}
        </div>
      </main>

      {/* Create Project Modal */}
      {state.showCreateModal && (
        <div style={{
          position: 'fixed',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          backgroundColor: 'rgba(0, 0, 0, 0.5)',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          zIndex: 1000
        }}>
          <div style={{
            backgroundColor: 'white',
            borderRadius: '12px',
            padding: '32px',
            maxWidth: '600px',
            width: '90%',
            maxHeight: '90vh',
            overflowY: 'auto'
          }}>
            <h2 style={{
              fontSize: '20px',
              fontWeight: 'bold',
              color: '#1f2937',
              marginBottom: '24px'
            }}>
              ➕ Create New Project
            </h2>
            
            <div style={{ display: 'flex', flexDirection: 'column', gap: '16px' }}>
              <div>
                <label style={{
                  display: 'block',
                  fontSize: '14px',
                  fontWeight: '500',
                  color: '#374151',
                  marginBottom: '6px'
                }}>
                  Project Name *
                </label>
                <input
                  type="text"
                  value={state.newProject.name}
                  onChange={(e) => setState(prev => ({
                    ...prev,
                    newProject: { ...prev.newProject, name: e.target.value }
                  }))}
                  placeholder="Enter project name"
                  style={{
                    width: '100%',
                    padding: '12px',
                    border: '1px solid #d1d5db',
                    borderRadius: '6px',
                    fontSize: '14px',
                    boxSizing: 'border-box'
                  }}
                />
              </div>

              <div>
                <label style={{
                  display: 'block',
                  fontSize: '14px',
                  fontWeight: '500',
                  color: '#374151',
                  marginBottom: '6px'
                }}>
                  Description
                </label>
                <textarea
                  value={state.newProject.description}
                  onChange={(e) => setState(prev => ({
                    ...prev,
                    newProject: { ...prev.newProject, description: e.target.value }
                  }))}
                  placeholder="Enter project description"
                  rows={4}
                  style={{
                    width: '100%',
                    padding: '12px',
                    border: '1px solid #d1d5db',
                    borderRadius: '6px',
                    fontSize: '14px',
                    resize: 'vertical',
                    boxSizing: 'border-box'
                  }}
                />
              </div>

              <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '16px' }}>
                <div>
                  <label style={{
                    display: 'block',
                    fontSize: '14px',
                    fontWeight: '500',
                    color: '#374151',
                    marginBottom: '6px'
                  }}>
                    Status
                  </label>
                  <select
                    value={state.newProject.status}
                    onChange={(e) => setState(prev => ({
                      ...prev,
                      newProject: { 
                        ...prev.newProject, 
                        status: e.target.value as 'planning' | 'active' | 'on-hold' | 'completed' | 'cancelled'
                      }
                    }))}
                    style={{
                      width: '100%',
                      padding: '12px',
                      border: '1px solid #d1d5db',
                      borderRadius: '6px',
                      fontSize: '14px',
                      backgroundColor: 'white'
                    }}
                  >
                    <option value="planning">Planning</option>
                    <option value="active">Active</option>
                    <option value="on-hold">On Hold</option>
                    <option value="completed">Completed</option>
                    <option value="cancelled">Cancelled</option>
                  </select>
                </div>

                <div>
                  <label style={{
                    display: 'block',
                    fontSize: '14px',
                    fontWeight: '500',
                    color: '#374151',
                    marginBottom: '6px'
                  }}>
                    Priority
                  </label>
                  <select
                    value={state.newProject.priority}
                    onChange={(e) => setState(prev => ({
                      ...prev,
                      newProject: { 
                        ...prev.newProject, 
                        priority: e.target.value as 'low' | 'medium' | 'high'
                      }
                    }))}
                    style={{
                      width: '100%',
                      padding: '12px',
                      border: '1px solid #d1d5db',
                      borderRadius: '6px',
                      fontSize: '14px',
                      backgroundColor: 'white'
                    }}
                  >
                    <option value="low">Low</option>
                    <option value="medium">Medium</option>
                    <option value="high">High</option>
                  </select>
                </div>
              </div>
            </div>

            <div style={{
              display: 'flex',
              justifyContent: 'flex-end',
              gap: '12px',
              marginTop: '24px'
            }}>
              <button
                onClick={() => setState(prev => ({ ...prev, showCreateModal: false }))}
                style={{
                  padding: '12px 20px',
                  backgroundColor: '#f3f4f6',
                  border: '1px solid #d1d5db',
                  borderRadius: '6px',
                  cursor: 'pointer',
                  fontSize: '14px'
                }}
              >
                Cancel
              </button>
              <button
                onClick={handleCreateProject}
                disabled={!state.newProject.name.trim() || state.isLoading}
                style={{
                  padding: '12px 20px',
                  backgroundColor: (!state.newProject.name.trim() || state.isLoading) ? '#9ca3af' : '#667eea',
                  color: 'white',
                  border: 'none',
                  borderRadius: '6px',
                  cursor: (!state.newProject.name.trim() || state.isLoading) ? 'not-allowed' : 'pointer',
                  fontSize: '14px',
                  fontWeight: '500'
                }}
              >
                {state.isLoading ? 'Creating...' : 'Create Project'}
              </button>
            </div>
          </div>
        </div>
      )}

      {/* View Project Modal */}
      {state.showViewModal && state.selectedProject && (
        <div style={{
          position: 'fixed',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          backgroundColor: 'rgba(0, 0, 0, 0.5)',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          zIndex: 1000
        }}>
          <div style={{
            backgroundColor: 'white',
            borderRadius: '12px',
            padding: '32px',
            maxWidth: '800px',
            width: '90%',
            maxHeight: '90vh',
            overflowY: 'auto'
          }}>
            <div style={{
              display: 'flex',
              justifyContent: 'space-between',
              alignItems: 'flex-start',
              marginBottom: '24px'
            }}>
              <div>
                <h2 style={{
                  fontSize: '24px',
                  fontWeight: 'bold',
                  color: '#1f2937',
                  margin: '0 0 8px 0'
                }}>
                  {state.selectedProject.name}
                </h2>
                
                <span style={{
                  padding: '4px 12px',
                  borderRadius: '12px',
                  fontSize: '12px',
                  fontWeight: '500',
                  backgroundColor: getStatusColor(state.selectedProject.status) + '20',
                  color: getStatusColor(state.selectedProject.status)
                }}>
                  {state.selectedProject.status}
                </span>
              </div>
              
              <button
                onClick={() => setState(prev => ({ ...prev, showViewModal: false, selectedProject: null }))}
                style={{
                  padding: '8px',
                  backgroundColor: '#f3f4f6',
                  border: 'none',
                  borderRadius: '6px',
                  cursor: 'pointer',
                  fontSize: '18px'
                }}
              >
                ✕
              </button>
            </div>
            
            <div style={{
              padding: '20px',
              backgroundColor: '#f9fafb',
              borderRadius: '8px',
              marginBottom: '24px'
            }}>
              <h3 style={{
                fontSize: '16px',
                fontWeight: '600',
                color: '#1f2937',
                marginBottom: '12px'
              }}>
                Description
              </h3>
              <p style={{
                fontSize: '14px',
                color: '#374151',
                lineHeight: '1.6',
                margin: 0
              }}>
                {state.selectedProject.description || 'No description available'}
              </p>
            </div>

            {/* Project Tasks */}
            <div>
              <h3 style={{
                fontSize: '16px',
                fontWeight: '600',
                color: '#1f2937',
                marginBottom: '12px'
              }}>
                Project Tasks
              </h3>
              
              {state.projectTasks[state.selectedProject.id] ? (
                state.projectTasks[state.selectedProject.id].length > 0 ? (
                  <div style={{ display: 'flex', flexDirection: 'column', gap: '8px' }}>
                    {state.projectTasks[state.selectedProject.id].map((task) => (
                      <div
                        key={task.id}
                        style={{
                          padding: '12px',
                          backgroundColor: '#f9fafb',
                          borderRadius: '6px',
                          border: '1px solid #e5e7eb'
                        }}
                      >
                        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                          <span style={{ fontSize: '14px', fontWeight: '500', color: '#1f2937' }}>
                            {task.title}
                          </span>
                          <span style={{
                            padding: '2px 8px',
                            borderRadius: '8px',
                            fontSize: '12px',
                            fontWeight: '500',
                            backgroundColor: task.status === 'completed' ? '#d1fae5' : 
                                           task.status === 'in-progress' ? '#dbeafe' : '#fef3c7',
                            color: task.status === 'completed' ? '#065f46' : 
                                   task.status === 'in-progress' ? '#1e40af' : '#92400e'
                          }}>
                            {task.status}
                          </span>
                        </div>
                      </div>
                    ))}
                  </div>
                ) : (
                  <p style={{ fontSize: '14px', color: '#6b7280', fontStyle: 'italic' }}>
                    No tasks found for this project
                  </p>
                )
              ) : (
                <p style={{ fontSize: '14px', color: '#6b7280', fontStyle: 'italic' }}>
                  Loading tasks...
                </p>
              )}
            </div>
          </div>
        </div>
      )}

      {/* CSS Animation */}
      <style>{`
        @keyframes spin {
          0% { transform: rotate(0deg); }
          100% { transform: rotate(360deg); }
        }
      `}</style>
    </div>
  )
}
