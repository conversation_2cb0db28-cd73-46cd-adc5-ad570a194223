/**
 * Roles & Permissions Management Page Component
 * Features: User role management, permission assignment, access control
 */
import React, { useState, useEffect, useCallback } from 'react'
import { logger, <PERSON>rror<PERSON>andler, PerformanceMonitor } from '../utils/logger'
import type { User, Role, Permission } from '../types'

interface RolesPageProps {
  user: User
  onNavigateBack?: () => void
}

interface RolesState {
  isLoading: boolean
  users: User[]
  roles: Role[]
  permissions: Permission[]
  selectedUser: User | null
  showEditModal: boolean
  showCreateRoleModal: boolean
  newRole: {
    name: string
    description: string
    permissions: string[]
  }
  error: string | null
}

const defaultPermissions = [
  { id: 'tasks.create', name: 'Create Tasks', category: 'Tasks' },
  { id: 'tasks.edit', name: 'Edit Tasks', category: 'Tasks' },
  { id: 'tasks.delete', name: 'Delete Tasks', category: 'Tasks' },
  { id: 'tasks.assign', name: 'Assign Tasks', category: 'Tasks' },
  { id: 'projects.create', name: 'Create Projects', category: 'Projects' },
  { id: 'projects.edit', name: 'Edit Projects', category: 'Projects' },
  { id: 'projects.delete', name: 'Delete Projects', category: 'Projects' },
  { id: 'users.view', name: 'View Users', category: 'Users' },
  { id: 'users.edit', name: 'Edit Users', category: 'Users' },
  { id: 'users.delete', name: 'Delete Users', category: 'Users' },
  { id: 'reports.view', name: 'View Reports', category: 'Reports' },
  { id: 'reports.export', name: 'Export Reports', category: 'Reports' },
  { id: 'admin.settings', name: 'Admin Settings', category: 'Administration' },
  { id: 'admin.roles', name: 'Manage Roles', category: 'Administration' }
]

export default function RolesPage({ user, onNavigateBack }: RolesPageProps) {
  const [state, setState] = useState<RolesState>({
    isLoading: true,
    users: [],
    roles: [],
    permissions: defaultPermissions,
    selectedUser: null,
    showEditModal: false,
    showCreateRoleModal: false,
    newRole: {
      name: '',
      description: '',
      permissions: []
    },
    error: null
  })

  useEffect(() => {
    logger.componentMount('RolesPage')
    loadRolesData()
    
    return () => {
      logger.componentUnmount('RolesPage')
    }
  }, [])

  const loadRolesData = useCallback(async () => {
    try {
      setState(prev => ({ ...prev, isLoading: true, error: null }))
      logger.info('Loading roles data', 'RolesPage')
      
      PerformanceMonitor.startTimer('roles_load')

      // Load users and roles in parallel
      const [usersResponse, rolesResponse] = await Promise.all([
        fetch('http://localhost:8002/api/users', {
          headers: {
            'Authorization': `Bearer ${localStorage.getItem('authToken')}`,
            'Content-Type': 'application/json'
          }
        }),
        fetch('http://localhost:8002/api/roles', {
          headers: {
            'Authorization': `Bearer ${localStorage.getItem('authToken')}`,
            'Content-Type': 'application/json'
          }
        })
      ])

      const duration = PerformanceMonitor.endTimer('roles_load')

      const users = usersResponse.ok ? await usersResponse.json() : { users: [] }
      const roles = rolesResponse.ok ? await rolesResponse.json() : { roles: [] }

      setState(prev => ({
        ...prev,
        isLoading: false,
        users: users.users || [],
        roles: roles.roles || [
          { id: '1', name: 'admin', description: 'Full system access', permissions: defaultPermissions.map(p => p.id) },
          { id: '2', name: 'manager', description: 'Team management access', permissions: ['tasks.create', 'tasks.edit', 'tasks.assign', 'projects.view', 'users.view', 'reports.view'] },
          { id: '3', name: 'user', description: 'Basic user access', permissions: ['tasks.create', 'tasks.edit'] }
        ]
      }))

      logger.info(`Roles loaded in ${duration.toFixed(2)}ms`, 'RolesPage', {
        userCount: users.users?.length || 0,
        roleCount: roles.roles?.length || 0
      })
      
    } catch (error) {
      const handledError = ErrorHandler.handleApiError(error, 'RolesPage')
      setState(prev => ({ 
        ...prev, 
        isLoading: false, 
        error: handledError.message 
      }))
      logger.error('Failed to load roles data', 'RolesPage', error)
    }
  }, [])

  const handleUpdateUserRole = async (userId: string, newRole: string) => {
    try {
      const response = await fetch(`http://localhost:8002/api/users/${userId}/role`, {
        method: 'PATCH',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('authToken')}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ role: newRole })
      })

      if (response.ok) {
        setState(prev => ({
          ...prev,
          users: prev.users.map(u => 
            u.id === userId ? { ...u, role: newRole } : u
          )
        }))
        logger.info('User role updated', 'RolesPage', { userId, newRole })
      } else {
        throw new Error('Failed to update user role')
      }
    } catch (error) {
      logger.error('Failed to update user role', 'RolesPage', error)
      alert('Failed to update user role. Please try again.')
    }
  }

  const handleCreateRole = async () => {
    if (!state.newRole.name.trim()) {
      alert('Please enter a role name')
      return
    }

    try {
      setState(prev => ({ ...prev, isLoading: true }))
      
      const response = await fetch('http://localhost:8002/api/roles', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('authToken')}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(state.newRole)
      })

      if (response.ok) {
        const createdRole = await response.json()
        setState(prev => ({
          ...prev,
          roles: [...prev.roles, createdRole],
          showCreateRoleModal: false,
          isLoading: false,
          newRole: { name: '', description: '', permissions: [] }
        }))
        
        logger.info('Role created successfully', 'RolesPage', { roleId: createdRole.id })
      } else {
        throw new Error('Failed to create role')
      }
    } catch (error) {
      logger.error('Failed to create role', 'RolesPage', error)
      setState(prev => ({ ...prev, isLoading: false }))
      alert('Failed to create role. Please try again.')
    }
  }

  const getRolePermissions = (roleName: string): string[] => {
    const role = state.roles.find(r => r.name === roleName)
    return role ? role.permissions : []
  }

  const getPermissionsByCategory = () => {
    const categories: { [key: string]: Permission[] } = {}
    state.permissions.forEach(permission => {
      if (!categories[permission.category]) {
        categories[permission.category] = []
      }
      categories[permission.category].push(permission)
    })
    return categories
  }

  const getRoleColor = (roleName: string): string => {
    switch (roleName.toLowerCase()) {
      case 'admin': return '#ef4444'
      case 'manager': return '#f59e0b'
      case 'user': return '#10b981'
      default: return '#6b7280'
    }
  }

  if (state.error) {
    return (
      <div style={{
        minHeight: '100vh',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        backgroundColor: '#f9fafb',
        padding: '20px'
      }}>
        <div style={{
          maxWidth: '500px',
          width: '100%',
          backgroundColor: 'white',
          padding: '40px',
          borderRadius: '16px',
          boxShadow: '0 10px 15px -3px rgba(0, 0, 0, 0.1)',
          textAlign: 'center'
        }}>
          <div style={{ fontSize: '48px', marginBottom: '20px' }}>⚠️</div>
          <h2 style={{ color: '#ef4444', marginBottom: '16px' }}>Roles Error</h2>
          <p style={{ color: '#6b7280', marginBottom: '24px' }}>{state.error}</p>
          <button
            onClick={loadRolesData}
            style={{
              padding: '12px 24px',
              backgroundColor: '#667eea',
              color: 'white',
              border: 'none',
              borderRadius: '8px',
              cursor: 'pointer',
              marginRight: '12px'
            }}
          >
            Try Again
          </button>
          <button
            onClick={onNavigateBack}
            style={{
              padding: '12px 24px',
              backgroundColor: '#6b7280',
              color: 'white',
              border: 'none',
              borderRadius: '8px',
              cursor: 'pointer'
            }}
          >
            Go Back
          </button>
        </div>
      </div>
    )
  }

  return (
    <div style={{ minHeight: '100vh', backgroundColor: '#f9fafb' }}>
      {/* Header */}
      <header style={{
        backgroundColor: 'white',
        boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1)',
        padding: '16px 0'
      }}>
        <div style={{
          maxWidth: '1400px',
          margin: '0 auto',
          padding: '0 24px',
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center'
        }}>
          <div style={{ display: 'flex', alignItems: 'center', gap: '16px' }}>
            <button
              onClick={onNavigateBack}
              style={{
                padding: '8px 12px',
                backgroundColor: '#f3f4f6',
                border: 'none',
                borderRadius: '6px',
                cursor: 'pointer',
                fontSize: '14px'
              }}
            >
              ← Back
            </button>
            <div>
              <h1 style={{ 
                margin: 0, 
                color: '#1f2937', 
                fontSize: '24px',
                fontWeight: 'bold'
              }}>
                👥 Roles & Permissions
              </h1>
              <p style={{ 
                margin: '4px 0 0 0', 
                color: '#6b7280', 
                fontSize: '14px' 
              }}>
                Manage user roles and access permissions
              </p>
            </div>
          </div>
          
          {user.role === 'admin' && (
            <button
              onClick={() => setState(prev => ({ ...prev, showCreateRoleModal: true }))}
              style={{
                padding: '12px 20px',
                backgroundColor: '#667eea',
                color: 'white',
                border: 'none',
                borderRadius: '8px',
                cursor: 'pointer',
                fontSize: '14px',
                fontWeight: '500',
                display: 'flex',
                alignItems: 'center',
                gap: '8px'
              }}
            >
              ➕ New Role
            </button>
          )}
        </div>
      </header>

      {/* Main Content */}
      <main style={{ maxWidth: '1400px', margin: '0 auto', padding: '24px' }}>
        {state.isLoading ? (
          <div style={{
            display: 'flex',
            justifyContent: 'center',
            alignItems: 'center',
            height: '400px'
          }}>
            <div style={{
              width: '40px',
              height: '40px',
              border: '4px solid #e5e7eb',
              borderTop: '4px solid #667eea',
              borderRadius: '50%',
              animation: 'spin 1s linear infinite'
            }} />
          </div>
        ) : (
          <div style={{
            display: 'grid',
            gridTemplateColumns: '1fr 1fr',
            gap: '24px'
          }}>
            {/* Users List */}
            <div style={{
              backgroundColor: 'white',
              borderRadius: '12px',
              padding: '24px',
              boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)'
            }}>
              <h2 style={{ 
                fontSize: '18px', 
                fontWeight: 'bold', 
                color: '#1f2937', 
                marginBottom: '20px' 
              }}>
                👤 Users ({state.users.length})
              </h2>
              
              {state.users.length > 0 ? (
                <div style={{ display: 'flex', flexDirection: 'column', gap: '12px' }}>
                  {state.users.map((userItem) => (
                    <div
                      key={userItem.id}
                      style={{
                        padding: '16px',
                        border: '1px solid #e5e7eb',
                        borderRadius: '8px',
                        backgroundColor: '#fafafa',
                        display: 'flex',
                        justifyContent: 'space-between',
                        alignItems: 'center'
                      }}
                    >
                      <div>
                        <h3 style={{
                          margin: '0 0 4px 0',
                          fontSize: '16px',
                          fontWeight: '600',
                          color: '#1f2937'
                        }}>
                          {userItem.username}
                        </h3>
                        <p style={{
                          margin: '0 0 4px 0',
                          fontSize: '14px',
                          color: '#6b7280'
                        }}>
                          {userItem.email}
                        </p>
                        <p style={{
                          margin: 0,
                          fontSize: '12px',
                          color: '#6b7280'
                        }}>
                          Department: {userItem.department}
                        </p>
                      </div>
                      
                      <div style={{ display: 'flex', flexDirection: 'column', alignItems: 'flex-end', gap: '8px' }}>
                        <span style={{
                          padding: '4px 12px',
                          borderRadius: '12px',
                          fontSize: '12px',
                          fontWeight: '500',
                          backgroundColor: getRoleColor(userItem.role) + '20',
                          color: getRoleColor(userItem.role)
                        }}>
                          {userItem.role}
                        </span>
                        
                        {user.role === 'admin' && userItem.id !== user.id && (
                          <select
                            value={userItem.role}
                            onChange={(e) => handleUpdateUserRole(userItem.id, e.target.value)}
                            style={{
                              padding: '4px 8px',
                              borderRadius: '6px',
                              fontSize: '12px',
                              border: '1px solid #d1d5db',
                              backgroundColor: 'white'
                            }}
                          >
                            {state.roles.map(role => (
                              <option key={role.id} value={role.name}>
                                {role.name}
                              </option>
                            ))}
                          </select>
                        )}
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div style={{
                  textAlign: 'center',
                  color: '#6b7280',
                  fontStyle: 'italic',
                  padding: '40px 0'
                }}>
                  No users found
                </div>
              )}
            </div>

            {/* Roles & Permissions */}
            <div style={{
              backgroundColor: 'white',
              borderRadius: '12px',
              padding: '24px',
              boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)'
            }}>
              <h2 style={{ 
                fontSize: '18px', 
                fontWeight: 'bold', 
                color: '#1f2937', 
                marginBottom: '20px' 
              }}>
                🔐 Roles & Permissions
              </h2>
              
              {state.roles.map((role) => (
                <div
                  key={role.id}
                  style={{
                    marginBottom: '24px',
                    padding: '20px',
                    border: '1px solid #e5e7eb',
                    borderRadius: '8px',
                    backgroundColor: '#fafafa'
                  }}
                >
                  <div style={{
                    display: 'flex',
                    justifyContent: 'space-between',
                    alignItems: 'center',
                    marginBottom: '12px'
                  }}>
                    <div>
                      <h3 style={{
                        margin: '0 0 4px 0',
                        fontSize: '16px',
                        fontWeight: '600',
                        color: '#1f2937',
                        textTransform: 'capitalize'
                      }}>
                        {role.name}
                      </h3>
                      <p style={{
                        margin: 0,
                        fontSize: '14px',
                        color: '#6b7280'
                      }}>
                        {role.description}
                      </p>
                    </div>
                    
                    <span style={{
                      padding: '4px 12px',
                      borderRadius: '12px',
                      fontSize: '12px',
                      fontWeight: '500',
                      backgroundColor: getRoleColor(role.name) + '20',
                      color: getRoleColor(role.name)
                    }}>
                      {role.permissions.length} permissions
                    </span>
                  </div>
                  
                  <div style={{
                    display: 'grid',
                    gridTemplateColumns: 'repeat(auto-fill, minmax(200px, 1fr))',
                    gap: '8px'
                  }}>
                    {Object.entries(getPermissionsByCategory()).map(([category, permissions]) => (
                      <div key={category} style={{ marginBottom: '12px' }}>
                        <h4 style={{
                          margin: '0 0 6px 0',
                          fontSize: '12px',
                          fontWeight: '600',
                          color: '#374151',
                          textTransform: 'uppercase',
                          letterSpacing: '0.05em'
                        }}>
                          {category}
                        </h4>
                        {permissions.map(permission => (
                          <div
                            key={permission.id}
                            style={{
                              display: 'flex',
                              alignItems: 'center',
                              gap: '6px',
                              marginBottom: '4px'
                            }}
                          >
                            <span style={{
                              fontSize: '12px',
                              color: role.permissions.includes(permission.id) ? '#10b981' : '#ef4444'
                            }}>
                              {role.permissions.includes(permission.id) ? '✅' : '❌'}
                            </span>
                            <span style={{
                              fontSize: '12px',
                              color: role.permissions.includes(permission.id) ? '#1f2937' : '#9ca3af'
                            }}>
                              {permission.name}
                            </span>
                          </div>
                        ))}
                      </div>
                    ))}
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}
      </main>

      {/* Create Role Modal */}
      {state.showCreateRoleModal && (
        <div style={{
          position: 'fixed',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          backgroundColor: 'rgba(0, 0, 0, 0.5)',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          zIndex: 1000
        }}>
          <div style={{
            backgroundColor: 'white',
            borderRadius: '12px',
            padding: '32px',
            maxWidth: '600px',
            width: '90%',
            maxHeight: '90vh',
            overflowY: 'auto'
          }}>
            <h2 style={{
              fontSize: '20px',
              fontWeight: 'bold',
              color: '#1f2937',
              marginBottom: '24px'
            }}>
              ➕ Create New Role
            </h2>
            
            <div style={{ display: 'flex', flexDirection: 'column', gap: '16px' }}>
              <div>
                <label style={{
                  display: 'block',
                  fontSize: '14px',
                  fontWeight: '500',
                  color: '#374151',
                  marginBottom: '6px'
                }}>
                  Role Name *
                </label>
                <input
                  type="text"
                  value={state.newRole.name}
                  onChange={(e) => setState(prev => ({
                    ...prev,
                    newRole: { ...prev.newRole, name: e.target.value }
                  }))}
                  placeholder="Enter role name"
                  style={{
                    width: '100%',
                    padding: '12px',
                    border: '1px solid #d1d5db',
                    borderRadius: '6px',
                    fontSize: '14px',
                    boxSizing: 'border-box'
                  }}
                />
              </div>

              <div>
                <label style={{
                  display: 'block',
                  fontSize: '14px',
                  fontWeight: '500',
                  color: '#374151',
                  marginBottom: '6px'
                }}>
                  Description
                </label>
                <textarea
                  value={state.newRole.description}
                  onChange={(e) => setState(prev => ({
                    ...prev,
                    newRole: { ...prev.newRole, description: e.target.value }
                  }))}
                  placeholder="Enter role description"
                  rows={3}
                  style={{
                    width: '100%',
                    padding: '12px',
                    border: '1px solid #d1d5db',
                    borderRadius: '6px',
                    fontSize: '14px',
                    resize: 'vertical',
                    boxSizing: 'border-box'
                  }}
                />
              </div>

              <div>
                <label style={{
                  display: 'block',
                  fontSize: '14px',
                  fontWeight: '500',
                  color: '#374151',
                  marginBottom: '12px'
                }}>
                  Permissions
                </label>
                
                {Object.entries(getPermissionsByCategory()).map(([category, permissions]) => (
                  <div key={category} style={{ marginBottom: '16px' }}>
                    <h4 style={{
                      margin: '0 0 8px 0',
                      fontSize: '14px',
                      fontWeight: '600',
                      color: '#374151'
                    }}>
                      {category}
                    </h4>
                    <div style={{
                      display: 'grid',
                      gridTemplateColumns: 'repeat(auto-fill, minmax(200px, 1fr))',
                      gap: '8px',
                      padding: '12px',
                      backgroundColor: '#f9fafb',
                      borderRadius: '6px'
                    }}>
                      {permissions.map(permission => (
                        <label
                          key={permission.id}
                          style={{
                            display: 'flex',
                            alignItems: 'center',
                            gap: '8px',
                            cursor: 'pointer',
                            fontSize: '14px'
                          }}
                        >
                          <input
                            type="checkbox"
                            checked={state.newRole.permissions.includes(permission.id)}
                            onChange={(e) => {
                              const permissions = e.target.checked
                                ? [...state.newRole.permissions, permission.id]
                                : state.newRole.permissions.filter(p => p !== permission.id)
                              
                              setState(prev => ({
                                ...prev,
                                newRole: { ...prev.newRole, permissions }
                              }))
                            }}
                            style={{ cursor: 'pointer' }}
                          />
                          {permission.name}
                        </label>
                      ))}
                    </div>
                  </div>
                ))}
              </div>
            </div>

            <div style={{
              display: 'flex',
              justifyContent: 'flex-end',
              gap: '12px',
              marginTop: '24px'
            }}>
              <button
                onClick={() => setState(prev => ({ 
                  ...prev, 
                  showCreateRoleModal: false,
                  newRole: { name: '', description: '', permissions: [] }
                }))}
                style={{
                  padding: '12px 20px',
                  backgroundColor: '#f3f4f6',
                  border: '1px solid #d1d5db',
                  borderRadius: '6px',
                  cursor: 'pointer',
                  fontSize: '14px'
                }}
              >
                Cancel
              </button>
              <button
                onClick={handleCreateRole}
                disabled={!state.newRole.name.trim() || state.isLoading}
                style={{
                  padding: '12px 20px',
                  backgroundColor: !state.newRole.name.trim() || state.isLoading ? '#9ca3af' : '#667eea',
                  color: 'white',
                  border: 'none',
                  borderRadius: '6px',
                  cursor: !state.newRole.name.trim() || state.isLoading ? 'not-allowed' : 'pointer',
                  fontSize: '14px',
                  fontWeight: '500'
                }}
              >
                {state.isLoading ? 'Creating...' : 'Create Role'}
              </button>
            </div>
          </div>
        </div>
      )}

      {/* CSS Animation */}
      <style>{`
        @keyframes spin {
          0% { transform: rotate(0deg); }
          100% { transform: rotate(360deg); }
        }
      `}</style>
    </div>
  )
}
