/**
 * Authentication Context
 */
import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react'
import { authAPI } from '../services/api'
import websocketService from '../services/websocket'
import { User } from '../types'

interface AuthContextType {
  user: User | null
  isAuthenticated: boolean
  isLoading: boolean
  login: (username: string, password: string) => Promise<void>
  register: (userData: {
    username: string
    email: string
    password: string
    role?: string
    department?: string
  }) => Promise<void>
  logout: () => Promise<void>
}

const AuthContext = createContext<AuthContextType | undefined>(undefined)

interface AuthProviderProps {
  children: ReactNode
}

export function AuthProvider({ children }: AuthProviderProps) {
  const [user, setUser] = useState<User | null>(null)
  const [isLoading, setIsLoading] = useState(true)

  useEffect(() => {
    // Check for existing auth token on mount
    const initAuth = async () => {
      const token = localStorage.getItem('authToken')
      const savedUser = localStorage.getItem('user')
      
      if (token && savedUser) {
        try {
          const parsedUser = JSON.parse(savedUser)
          setUser(parsedUser)
          
          // Verify token is still valid
          const currentUser = await authAPI.getCurrentUser()
          setUser(currentUser)
          
          // Connect to WebSocket
          await websocketService.connect(currentUser)
          
        } catch (error) {
          console.error('Auth initialization failed:', error)
          // Clear invalid auth data
          localStorage.removeItem('authToken')
          localStorage.removeItem('user')
          setUser(null)
        }
      }
      
      setIsLoading(false)
    }

    initAuth()
  }, [])

  const login = async (username: string, password: string) => {
    try {
      setIsLoading(true)
      const response = await authAPI.login({ username, password })
      
      // Store auth data
      localStorage.setItem('authToken', response.access_token)
      localStorage.setItem('user', JSON.stringify(response.user))
      
      setUser(response.user)
      
      // Connect to WebSocket
      await websocketService.connect(response.user)
      
      console.log('✅ Login successful:', response.user)
    } catch (error) {
      console.error('❌ Login failed:', error)
      throw error
    } finally {
      setIsLoading(false)
    }
  }

  const register = async (userData: {
    username: string
    email: string
    password: string
    role?: string
    department?: string
  }) => {
    try {
      setIsLoading(true)
      const response = await authAPI.register(userData)
      
      // Store auth data
      localStorage.setItem('authToken', response.access_token)
      localStorage.setItem('user', JSON.stringify(response.user))
      
      setUser(response.user)
      
      // Connect to WebSocket
      await websocketService.connect(response.user)
      
      console.log('✅ Registration successful:', response.user)
    } catch (error) {
      console.error('❌ Registration failed:', error)
      throw error
    } finally {
      setIsLoading(false)
    }
  }

  const logout = async () => {
    try {
      setIsLoading(true)
      
      // Disconnect WebSocket
      websocketService.disconnect()
      
      // Call logout API
      await authAPI.logout()
      
      // Clear auth data
      localStorage.removeItem('authToken')
      localStorage.removeItem('user')
      
      setUser(null)
      
      console.log('✅ Logout successful')
    } catch (error) {
      console.error('❌ Logout failed:', error)
      // Clear auth data anyway
      localStorage.removeItem('authToken')
      localStorage.removeItem('user')
      setUser(null)
    } finally {
      setIsLoading(false)
    }
  }

  const value: AuthContextType = {
    user,
    isAuthenticated: !!user,
    isLoading,
    login,
    register,
    logout,
  }

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  )
}

export function useAuth() {
  const context = useContext(AuthContext)
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider')
  }
  return context
}

export default AuthContext
