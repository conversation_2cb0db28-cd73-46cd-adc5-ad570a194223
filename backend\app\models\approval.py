"""
Approval workflow models for MongoDB
"""
from typing import Optional, List, Dict, Any
from datetime import datetime
from pydantic import BaseModel, Field
from bson import ObjectId
from enum import Enum

from .user import PyObjectId


class ApprovalType(str, Enum):
    PROCUREMENT = "procurement"
    LEAVE = "leave"
    PROJECT = "project"
    DOCUMENT = "document"
    INVOICE = "invoice"
    EXPENSE = "expense"
    BUDGET = "budget"
    HIRING = "hiring"
    POLICY_CHANGE = "policy_change"
    SYSTEM_ACCESS = "system_access"


class ApprovalStatus(str, Enum):
    PENDING = "pending"
    APPROVED = "approved"
    REJECTED = "rejected"
    IN_REVIEW = "in_review"
    CANCELLED = "cancelled"
    EXPIRED = "expired"


class Priority(str, Enum):
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    URGENT = "urgent"


class ApprovalStep(BaseModel):
    """Individual approval step in workflow"""
    step_number: int
    approver_id: PyObjectId
    approver_name: str
    approver_role: str
    status: ApprovalStatus = ApprovalStatus.PENDING
    approved_at: Optional[datetime] = None
    rejected_at: Optional[datetime] = None
    comments: Optional[str] = None
    is_required: bool = True
    can_delegate: bool = False
    delegated_to: Optional[PyObjectId] = None


class ApprovalWorkflow(BaseModel):
    """Approval workflow template"""
    id: PyObjectId = Field(default_factory=PyObjectId, alias="_id")
    name: str
    description: Optional[str] = None
    approval_type: ApprovalType
    
    # Workflow Configuration
    steps: List[Dict[str, Any]] = []  # Template steps
    is_sequential: bool = True  # Sequential vs parallel approval
    requires_all_approvers: bool = True
    auto_approve_threshold: Optional[float] = None  # Auto-approve if amount below threshold
    
    # Conditions
    conditions: Dict[str, Any] = {}  # Conditions for triggering this workflow
    department_specific: Optional[str] = None
    role_specific: Optional[str] = None
    
    # Settings
    is_active: bool = True
    expiry_hours: Optional[int] = None  # Auto-expire after X hours
    reminder_hours: List[int] = [24, 48, 72]  # Send reminders
    
    # Metadata
    created_at: datetime = Field(default_factory=datetime.utcnow)
    updated_at: datetime = Field(default_factory=datetime.utcnow)
    created_by: PyObjectId
    
    class Config:
        allow_population_by_field_name = True
        arbitrary_types_allowed = True
        json_encoders = {ObjectId: str}


class Approval(BaseModel):
    """Main approval request model"""
    id: PyObjectId = Field(default_factory=PyObjectId, alias="_id")
    
    # Basic Information
    title: str
    description: Optional[str] = None
    approval_type: ApprovalType
    priority: Priority = Priority.MEDIUM
    
    # Request Details
    requester_id: PyObjectId
    requester_name: str
    department: str
    
    # Workflow
    workflow_id: Optional[PyObjectId] = None
    workflow_name: Optional[str] = None
    current_step: int = 1
    total_steps: int = 1
    steps: List[ApprovalStep] = []
    
    # Status
    status: ApprovalStatus = ApprovalStatus.PENDING
    final_approved_at: Optional[datetime] = None
    final_rejected_at: Optional[datetime] = None
    final_approver_id: Optional[PyObjectId] = None
    
    # Data
    request_data: Dict[str, Any] = {}  # Specific request data
    attachments: List[str] = []  # File IDs
    
    # Financial (if applicable)
    amount: Optional[float] = None
    currency: str = "USD"
    budget_code: Optional[str] = None
    
    # Timeline
    requested_at: datetime = Field(default_factory=datetime.utcnow)
    required_by: Optional[datetime] = None
    expires_at: Optional[datetime] = None
    
    # Audit Trail
    history: List[Dict[str, Any]] = []
    comments: List[Dict[str, Any]] = []
    
    # Metadata
    created_at: datetime = Field(default_factory=datetime.utcnow)
    updated_at: datetime = Field(default_factory=datetime.utcnow)
    
    # Settings
    is_urgent: bool = False
    requires_justification: bool = False
    
    class Config:
        allow_population_by_field_name = True
        arbitrary_types_allowed = True
        json_encoders = {ObjectId: str}
        schema_extra = {
            "example": {
                "title": "Office Equipment Purchase",
                "description": "Request for new laptops and monitors for development team",
                "approval_type": "procurement",
                "priority": "medium",
                "amount": 15000.00,
                "currency": "USD",
                "request_data": {
                    "items": [
                        {"name": "MacBook Pro", "quantity": 5, "unit_price": 2500},
                        {"name": "4K Monitor", "quantity": 5, "unit_price": 500}
                    ],
                    "vendor": "Tech Supplier Inc",
                    "justification": "Team expansion requires additional equipment"
                }
            }
        }


class ProcurementRequest(BaseModel):
    """Specific procurement request model"""
    id: PyObjectId = Field(default_factory=PyObjectId, alias="_id")
    approval_id: PyObjectId  # Reference to main approval
    
    # Procurement Details
    vendor: Optional[str] = None
    vendor_contact: Optional[str] = None
    items: List[Dict[str, Any]] = []
    total_amount: float = 0.0
    currency: str = "USD"
    
    # Delivery
    delivery_address: Optional[str] = None
    required_by: Optional[datetime] = None
    
    # Budget
    budget_code: Optional[str] = None
    cost_center: Optional[str] = None
    
    # Justification
    business_justification: str
    alternatives_considered: Optional[str] = None
    
    # Quotes
    quotes: List[Dict[str, Any]] = []  # Vendor quotes
    selected_quote_id: Optional[str] = None
    
    class Config:
        allow_population_by_field_name = True
        arbitrary_types_allowed = True
        json_encoders = {ObjectId: str}


class LeaveRequest(BaseModel):
    """Specific leave request model"""
    id: PyObjectId = Field(default_factory=PyObjectId, alias="_id")
    approval_id: PyObjectId  # Reference to main approval
    
    # Leave Details
    leave_type: str  # annual, sick, maternity, paternity, emergency, unpaid
    start_date: datetime
    end_date: datetime
    total_days: float
    
    # Coverage
    coverage_arranged: bool = False
    coverage_person_id: Optional[PyObjectId] = None
    coverage_notes: Optional[str] = None
    
    # Medical (if applicable)
    medical_certificate: Optional[str] = None  # File ID
    
    # Emergency Contact
    emergency_contact: Optional[Dict[str, str]] = None
    
    # Balance Check
    available_balance: Optional[float] = None
    balance_after: Optional[float] = None
    
    class Config:
        allow_population_by_field_name = True
        arbitrary_types_allowed = True
        json_encoders = {ObjectId: str}


class ExpenseRequest(BaseModel):
    """Expense reimbursement request model"""
    id: PyObjectId = Field(default_factory=PyObjectId, alias="_id")
    approval_id: PyObjectId  # Reference to main approval
    
    # Expense Details
    expense_date: datetime
    category: str  # travel, meals, office_supplies, training, etc.
    merchant: str
    total_amount: float = 0.0
    currency: str = "USD"
    
    # Business Purpose
    business_purpose: str
    project_id: Optional[PyObjectId] = None
    client_billable: bool = False
    
    # Receipts
    receipts: List[str] = []  # File IDs
    
    # Mileage (if applicable)
    mileage_distance: Optional[float] = None
    mileage_rate: Optional[float] = None
    
    # Per Diem (if applicable)
    per_diem_days: Optional[int] = None
    per_diem_rate: Optional[float] = None
    
    class Config:
        allow_population_by_field_name = True
        arbitrary_types_allowed = True
        json_encoders = {ObjectId: str}


class ApprovalTemplate(BaseModel):
    """Template for common approval requests"""
    id: PyObjectId = Field(default_factory=PyObjectId, alias="_id")
    name: str
    description: Optional[str] = None
    approval_type: ApprovalType
    
    # Template Structure
    form_fields: List[Dict[str, Any]] = []
    default_workflow_id: Optional[PyObjectId] = None
    
    # Settings
    is_active: bool = True
    department_specific: Optional[str] = None
    role_specific: Optional[str] = None
    
    # Usage
    usage_count: int = 0
    
    # Metadata
    created_at: datetime = Field(default_factory=datetime.utcnow)
    updated_at: datetime = Field(default_factory=datetime.utcnow)
    created_by: PyObjectId
    
    class Config:
        allow_population_by_field_name = True
        arbitrary_types_allowed = True
        json_encoders = {ObjectId: str}


class ApprovalNotification(BaseModel):
    """Approval notification model"""
    id: PyObjectId = Field(default_factory=PyObjectId, alias="_id")
    approval_id: PyObjectId
    recipient_id: PyObjectId
    
    # Notification Details
    notification_type: str  # request, reminder, approved, rejected, escalation
    title: str
    message: str
    
    # Status
    is_sent: bool = False
    sent_at: Optional[datetime] = None
    is_read: bool = False
    read_at: Optional[datetime] = None
    
    # Delivery
    delivery_methods: List[str] = ["in_app"]  # in_app, email, sms
    
    # Metadata
    created_at: datetime = Field(default_factory=datetime.utcnow)
    
    class Config:
        allow_population_by_field_name = True
        arbitrary_types_allowed = True
        json_encoders = {ObjectId: str}


class ApprovalReport(BaseModel):
    """Approval analytics and reporting"""
    id: PyObjectId = Field(default_factory=PyObjectId, alias="_id")
    
    # Report Details
    report_type: str  # summary, detailed, trend_analysis
    period_start: datetime
    period_end: datetime
    
    # Filters
    department: Optional[str] = None
    approval_type: Optional[ApprovalType] = None
    status: Optional[ApprovalStatus] = None
    
    # Data
    total_requests: int = 0
    approved_requests: int = 0
    rejected_requests: int = 0
    pending_requests: int = 0
    avg_approval_time: Optional[float] = None  # in hours
    
    # Detailed Data
    data: Dict[str, Any] = {}
    
    # Metadata
    generated_at: datetime = Field(default_factory=datetime.utcnow)
    generated_by: PyObjectId
    
    class Config:
        allow_population_by_field_name = True
        arbitrary_types_allowed = True
        json_encoders = {ObjectId: str}
