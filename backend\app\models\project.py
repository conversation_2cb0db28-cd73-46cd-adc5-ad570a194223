"""
Project and Task models for MongoDB
"""
from typing import Optional, List, Dict, Any
from datetime import datetime
from pydantic import BaseModel, Field
from bson import ObjectId
from enum import Enum

from .user import PyObjectId


class ProjectStatus(str, Enum):
    PLANNING = "planning"
    ACTIVE = "active"
    ON_HOLD = "on_hold"
    COMPLETED = "completed"
    CANCELLED = "cancelled"


class TaskStatus(str, Enum):
    TODO = "todo"
    IN_PROGRESS = "in_progress"
    REVIEW = "review"
    COMPLETED = "completed"
    CANCELLED = "cancelled"


class Priority(str, Enum):
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    URGENT = "urgent"


class ProjectMilestone(BaseModel):
    """Project milestone model"""
    id: PyObjectId = Field(default_factory=PyObjectId)
    title: str
    description: Optional[str] = None
    due_date: Optional[datetime] = None
    status: str = "pending"  # pending, completed, overdue
    completion_date: Optional[datetime] = None
    deliverables: List[str] = []
    dependencies: List[PyObjectId] = []


class ProjectResource(BaseModel):
    """Project resource allocation"""
    user_id: PyObjectId
    role: str  # project_manager, developer, designer, tester, etc.
    allocation_percentage: float = 100.0  # Percentage of time allocated
    start_date: Optional[datetime] = None
    end_date: Optional[datetime] = None
    hourly_rate: Optional[float] = None


class ProjectBudget(BaseModel):
    """Project budget tracking"""
    total_budget: float = 0.0
    allocated_budget: float = 0.0
    spent_budget: float = 0.0
    currency: str = "USD"
    budget_categories: Dict[str, float] = {}  # development, design, testing, etc.


class Project(BaseModel):
    """Main Project model"""
    id: PyObjectId = Field(default_factory=PyObjectId, alias="_id")
    name: str = Field(..., min_length=1, max_length=200)
    description: Optional[str] = None
    
    # Status and Priority
    status: ProjectStatus = ProjectStatus.PLANNING
    priority: Priority = Priority.MEDIUM
    progress: float = Field(0.0, ge=0.0, le=100.0)
    
    # Team and Management
    manager_id: PyObjectId
    team_members: List[PyObjectId] = []
    resources: List[ProjectResource] = []
    
    # Organization
    department: str
    client: Optional[str] = None
    
    # Timeline
    start_date: Optional[datetime] = None
    end_date: Optional[datetime] = None
    estimated_hours: Optional[float] = None
    actual_hours: float = 0.0
    
    # Budget
    budget: Optional[ProjectBudget] = None
    
    # Project Structure
    milestones: List[ProjectMilestone] = []
    tags: List[str] = []
    categories: List[str] = []
    
    # Files and Documentation
    attachments: List[str] = []  # File IDs
    documentation_url: Optional[str] = None
    repository_url: Optional[str] = None
    
    # Risk Management
    risks: List[Dict[str, Any]] = []
    issues: List[Dict[str, Any]] = []
    
    # Metadata
    created_at: datetime = Field(default_factory=datetime.utcnow)
    updated_at: datetime = Field(default_factory=datetime.utcnow)
    created_by: PyObjectId
    
    # Settings
    is_template: bool = False
    is_archived: bool = False
    visibility: str = "team"  # public, team, private
    
    class Config:
        allow_population_by_field_name = True
        arbitrary_types_allowed = True
        json_encoders = {ObjectId: str}
        schema_extra = {
            "example": {
                "name": "Website Redesign Project",
                "description": "Complete redesign of company website with modern UI/UX",
                "status": "active",
                "priority": "high",
                "progress": 45.5,
                "department": "Engineering",
                "client": "Internal",
                "start_date": "2024-01-15T00:00:00Z",
                "end_date": "2024-06-30T00:00:00Z",
                "estimated_hours": 800,
                "tags": ["web", "design", "frontend"],
                "categories": ["development", "design"]
            }
        }


class TaskComment(BaseModel):
    """Task comment model"""
    id: PyObjectId = Field(default_factory=PyObjectId)
    content: str
    author_id: PyObjectId
    author_name: str
    created_at: datetime = Field(default_factory=datetime.utcnow)
    updated_at: Optional[datetime] = None
    is_edited: bool = False
    attachments: List[str] = []


class TaskTimeLog(BaseModel):
    """Task-specific time tracking"""
    id: PyObjectId = Field(default_factory=PyObjectId)
    user_id: PyObjectId
    start_time: datetime
    end_time: Optional[datetime] = None
    duration: Optional[float] = None  # in seconds
    description: Optional[str] = None
    is_billable: bool = True


class TaskDependency(BaseModel):
    """Task dependency model"""
    task_id: PyObjectId
    dependency_type: str = "finish_to_start"  # finish_to_start, start_to_start, etc.
    lag_time: int = 0  # in hours


class Task(BaseModel):
    """Main Task model"""
    id: PyObjectId = Field(default_factory=PyObjectId, alias="_id")
    title: str = Field(..., min_length=1, max_length=200)
    description: Optional[str] = None
    
    # Status and Priority
    status: TaskStatus = TaskStatus.TODO
    priority: Priority = Priority.MEDIUM
    progress: float = Field(0.0, ge=0.0, le=100.0)
    
    # Assignment
    assignee_id: Optional[PyObjectId] = None
    assignee_name: Optional[str] = None
    reviewer_id: Optional[PyObjectId] = None
    
    # Project Association
    project_id: Optional[PyObjectId] = None
    project_name: Optional[str] = None
    milestone_id: Optional[PyObjectId] = None
    
    # Timeline
    due_date: Optional[datetime] = None
    start_date: Optional[datetime] = None
    completion_date: Optional[datetime] = None
    
    # Time Tracking
    estimated_hours: Optional[float] = None
    actual_hours: float = 0.0
    time_logs: List[TaskTimeLog] = []
    
    # Task Structure
    parent_task_id: Optional[PyObjectId] = None
    subtasks: List[PyObjectId] = []
    dependencies: List[TaskDependency] = []
    
    # Content
    tags: List[str] = []
    labels: List[str] = []
    attachments: List[str] = []  # File IDs
    checklist: List[Dict[str, Any]] = []
    
    # Collaboration
    comments: List[TaskComment] = []
    watchers: List[PyObjectId] = []
    
    # Metadata
    created_at: datetime = Field(default_factory=datetime.utcnow)
    updated_at: datetime = Field(default_factory=datetime.utcnow)
    created_by: PyObjectId
    
    # Settings
    is_recurring: bool = False
    recurrence_pattern: Optional[Dict[str, Any]] = None
    is_template: bool = False
    
    class Config:
        allow_population_by_field_name = True
        arbitrary_types_allowed = True
        json_encoders = {ObjectId: str}
        schema_extra = {
            "example": {
                "title": "Implement user authentication",
                "description": "Create secure login/logout functionality with JWT tokens",
                "status": "in_progress",
                "priority": "high",
                "progress": 60.0,
                "due_date": "2024-02-15T17:00:00Z",
                "estimated_hours": 16,
                "actual_hours": 10.5,
                "tags": ["backend", "security", "authentication"],
                "labels": ["feature", "critical"]
            }
        }


class ProjectTemplate(BaseModel):
    """Project template model"""
    id: PyObjectId = Field(default_factory=PyObjectId, alias="_id")
    name: str
    description: Optional[str] = None
    category: str
    
    # Template Structure
    project_structure: Dict[str, Any]  # Project configuration
    default_tasks: List[Dict[str, Any]] = []  # Default task templates
    default_milestones: List[Dict[str, Any]] = []
    
    # Settings
    is_public: bool = False
    usage_count: int = 0
    
    # Metadata
    created_at: datetime = Field(default_factory=datetime.utcnow)
    updated_at: datetime = Field(default_factory=datetime.utcnow)
    created_by: PyObjectId
    
    class Config:
        allow_population_by_field_name = True
        arbitrary_types_allowed = True
        json_encoders = {ObjectId: str}


class ProjectReport(BaseModel):
    """Project reporting model"""
    id: PyObjectId = Field(default_factory=PyObjectId, alias="_id")
    project_id: PyObjectId
    report_type: str  # progress, budget, time, risk, etc.
    
    # Report Data
    data: Dict[str, Any]
    summary: str
    recommendations: List[str] = []
    
    # Report Period
    start_date: datetime
    end_date: datetime
    
    # Metadata
    generated_at: datetime = Field(default_factory=datetime.utcnow)
    generated_by: PyObjectId
    
    class Config:
        allow_population_by_field_name = True
        arbitrary_types_allowed = True
        json_encoders = {ObjectId: str}


class Kanban(BaseModel):
    """Kanban board model"""
    id: PyObjectId = Field(default_factory=PyObjectId, alias="_id")
    name: str
    description: Optional[str] = None
    
    # Board Structure
    columns: List[Dict[str, Any]] = [
        {"id": "todo", "name": "To Do", "limit": None},
        {"id": "in_progress", "name": "In Progress", "limit": 3},
        {"id": "review", "name": "Review", "limit": None},
        {"id": "done", "name": "Done", "limit": None}
    ]
    
    # Association
    project_id: Optional[PyObjectId] = None
    team_id: Optional[PyObjectId] = None
    
    # Settings
    is_public: bool = False
    
    # Metadata
    created_at: datetime = Field(default_factory=datetime.utcnow)
    updated_at: datetime = Field(default_factory=datetime.utcnow)
    created_by: PyObjectId
    
    class Config:
        allow_population_by_field_name = True
        arbitrary_types_allowed = True
        json_encoders = {ObjectId: str}
