"""
Notifications API routes
"""
from fastapi import APIRouter, Depends, HTTPException, status, Query
from motor.motor_asyncio import AsyncIOMotorDatabase
from typing import Dict, Any, List, Optional
from datetime import datetime, timedelta
from bson import ObjectId
from pydantic import BaseModel

from ..core.database import get_database, create_audit_log
from ..core.security import get_current_user_token
from ..core.config import Collections

router = APIRouter()


class NotificationCreate(BaseModel):
    title: str
    message: str
    notification_type: str = "info"  # info, success, warning, error
    recipient_ids: List[str] = []
    data: Dict[str, Any] = {}
    expires_at: Optional[datetime] = None


class NotificationUpdate(BaseModel):
    is_read: Optional[bool] = None


@router.get("/")
async def get_notifications(
    page: int = Query(1, ge=1),
    size: int = Query(20, ge=1, le=100),
    unread_only: bool = Query(False),
    notification_type: Optional[str] = Query(None),
    current_user: Dict[str, Any] = Depends(get_current_user_token),
    db: AsyncIOMotorDatabase = Depends(get_database)
):
    """Get user notifications"""
    
    user_id = current_user.get("sub")
    
    try:
        # Build filter query
        filter_query = {"user_id": ObjectId(user_id)}
        
        if unread_only:
            filter_query["is_read"] = False
        
        if notification_type:
            filter_query["type"] = notification_type
        
        # Filter out expired notifications
        filter_query["$or"] = [
            {"expires_at": {"$exists": False}},
            {"expires_at": None},
            {"expires_at": {"$gt": datetime.utcnow()}}
        ]
        
        # Get total count
        total = await db[Collections.NOTIFICATIONS].count_documents(filter_query)
        
        # Calculate pagination
        skip = (page - 1) * size
        pages = (total + size - 1) // size
        
        # Get notifications
        notifications = await db[Collections.NOTIFICATIONS].find(filter_query).sort("created_at", -1).skip(skip).limit(size).to_list(length=None)
        
        # Format response
        formatted_notifications = []
        for notification in notifications:
            formatted_notifications.append({
                "id": str(notification["_id"]),
                "title": notification["title"],
                "message": notification["message"],
                "type": notification.get("type", "info"),
                "is_read": notification.get("is_read", False),
                "data": notification.get("data", {}),
                "created_at": notification["created_at"],
                "read_at": notification.get("read_at"),
                "expires_at": notification.get("expires_at")
            })
        
        return {
            "notifications": formatted_notifications,
            "total": total,
            "unread_count": await db[Collections.NOTIFICATIONS].count_documents({
                "user_id": ObjectId(user_id),
                "is_read": False,
                "$or": [
                    {"expires_at": {"$exists": False}},
                    {"expires_at": None},
                    {"expires_at": {"$gt": datetime.utcnow()}}
                ]
            }),
            "page": page,
            "size": size,
            "pages": pages
        }
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve notifications"
        )


@router.get("/unread-count")
async def get_unread_count(
    current_user: Dict[str, Any] = Depends(get_current_user_token),
    db: AsyncIOMotorDatabase = Depends(get_database)
):
    """Get unread notification count"""
    
    user_id = current_user.get("sub")
    
    try:
        count = await db[Collections.NOTIFICATIONS].count_documents({
            "user_id": ObjectId(user_id),
            "is_read": False,
            "$or": [
                {"expires_at": {"$exists": False}},
                {"expires_at": None},
                {"expires_at": {"$gt": datetime.utcnow()}}
            ]
        })
        
        return {"unread_count": count}
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to get unread count"
        )


@router.put("/{notification_id}")
async def update_notification(
    notification_id: str,
    update_data: NotificationUpdate,
    current_user: Dict[str, Any] = Depends(get_current_user_token),
    db: AsyncIOMotorDatabase = Depends(get_database)
):
    """Update notification (mark as read/unread)"""
    
    user_id = current_user.get("sub")
    
    try:
        notification = await db[Collections.NOTIFICATIONS].find_one({
            "_id": ObjectId(notification_id),
            "user_id": ObjectId(user_id)
        })
        
        if not notification:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Notification not found"
            )
        
        # Build update document
        update_doc = {"updated_at": datetime.utcnow()}
        
        if update_data.is_read is not None:
            update_doc["is_read"] = update_data.is_read
            if update_data.is_read:
                update_doc["read_at"] = datetime.utcnow()
            else:
                update_doc["read_at"] = None
        
        # Update notification
        await db[Collections.NOTIFICATIONS].update_one(
            {"_id": ObjectId(notification_id)},
            {"$set": update_doc}
        )
        
        return {"message": "Notification updated successfully"}
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to update notification"
        )


@router.put("/mark-all-read")
async def mark_all_read(
    current_user: Dict[str, Any] = Depends(get_current_user_token),
    db: AsyncIOMotorDatabase = Depends(get_database)
):
    """Mark all notifications as read"""
    
    user_id = current_user.get("sub")
    
    try:
        result = await db[Collections.NOTIFICATIONS].update_many(
            {
                "user_id": ObjectId(user_id),
                "is_read": False
            },
            {
                "$set": {
                    "is_read": True,
                    "read_at": datetime.utcnow(),
                    "updated_at": datetime.utcnow()
                }
            }
        )
        
        return {
            "message": f"Marked {result.modified_count} notifications as read"
        }
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to mark notifications as read"
        )


@router.delete("/{notification_id}")
async def delete_notification(
    notification_id: str,
    current_user: Dict[str, Any] = Depends(get_current_user_token),
    db: AsyncIOMotorDatabase = Depends(get_database)
):
    """Delete a notification"""
    
    user_id = current_user.get("sub")
    
    try:
        result = await db[Collections.NOTIFICATIONS].delete_one({
            "_id": ObjectId(notification_id),
            "user_id": ObjectId(user_id)
        })
        
        if result.deleted_count == 0:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Notification not found"
            )
        
        return {"message": "Notification deleted successfully"}
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to delete notification"
        )


@router.delete("/clear-all")
async def clear_all_notifications(
    current_user: Dict[str, Any] = Depends(get_current_user_token),
    db: AsyncIOMotorDatabase = Depends(get_database)
):
    """Clear all notifications for user"""
    
    user_id = current_user.get("sub")
    
    try:
        result = await db[Collections.NOTIFICATIONS].delete_many({
            "user_id": ObjectId(user_id)
        })
        
        return {
            "message": f"Cleared {result.deleted_count} notifications"
        }
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to clear notifications"
        )


@router.post("/send")
async def send_notification(
    notification_data: NotificationCreate,
    current_user: Dict[str, Any] = Depends(get_current_user_token),
    db: AsyncIOMotorDatabase = Depends(get_database)
):
    """Send notification to users (Admin/Manager only)"""
    
    user_id = current_user.get("sub")
    user_role = current_user.get("role")
    
    # Check permissions
    if user_role not in ["admin", "manager"]:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Permission denied"
        )
    
    try:
        # Get sender info
        sender = await db[Collections.USERS].find_one({"_id": ObjectId(user_id)})
        sender_name = f"{sender['profile']['first_name']} {sender['profile']['last_name']}"
        
        # Validate recipients
        recipient_ids = []
        for recipient_id in notification_data.recipient_ids:
            recipient = await db[Collections.USERS].find_one({"_id": ObjectId(recipient_id)})
            if recipient:
                recipient_ids.append(ObjectId(recipient_id))
        
        if not recipient_ids:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="No valid recipients found"
            )
        
        # Create notifications for each recipient
        notifications = []
        for recipient_id in recipient_ids:
            notification_doc = {
                "user_id": recipient_id,
                "title": notification_data.title,
                "message": notification_data.message,
                "type": notification_data.notification_type,
                "is_read": False,
                "read_at": None,
                "data": {
                    **notification_data.data,
                    "sender_id": user_id,
                    "sender_name": sender_name
                },
                "expires_at": notification_data.expires_at,
                "created_at": datetime.utcnow(),
                "updated_at": datetime.utcnow()
            }
            notifications.append(notification_doc)
        
        # Insert notifications
        result = await db[Collections.NOTIFICATIONS].insert_many(notifications)
        
        # Create audit log
        await create_audit_log(
            user_id=user_id,
            action="send_notification",
            resource_type="notification",
            resource_id="bulk",
            details={
                "title": notification_data.title,
                "recipient_count": len(recipient_ids),
                "type": notification_data.notification_type
            }
        )
        
        return {
            "message": f"Notification sent to {len(recipient_ids)} users",
            "notification_ids": [str(id) for id in result.inserted_ids]
        }
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to send notification"
        )


@router.post("/broadcast")
async def broadcast_notification(
    notification_data: NotificationCreate,
    department: Optional[str] = Query(None),
    role: Optional[str] = Query(None),
    current_user: Dict[str, Any] = Depends(get_current_user_token),
    db: AsyncIOMotorDatabase = Depends(get_database)
):
    """Broadcast notification to all users or filtered by department/role (Admin only)"""
    
    user_id = current_user.get("sub")
    user_role = current_user.get("role")
    
    # Check permissions
    if user_role != "admin":
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Admin access required"
        )
    
    try:
        # Build user filter
        user_filter = {"is_active": True}
        
        if department:
            user_filter["department"] = department
        
        if role:
            user_filter["role"] = role
        
        # Get target users
        users = await db[Collections.USERS].find(user_filter).to_list(length=None)
        
        if not users:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="No users found matching criteria"
            )
        
        # Get sender info
        sender = await db[Collections.USERS].find_one({"_id": ObjectId(user_id)})
        sender_name = f"{sender['profile']['first_name']} {sender['profile']['last_name']}"
        
        # Create notifications for each user
        notifications = []
        for user_doc in users:
            notification_doc = {
                "user_id": user_doc["_id"],
                "title": notification_data.title,
                "message": notification_data.message,
                "type": notification_data.notification_type,
                "is_read": False,
                "read_at": None,
                "data": {
                    **notification_data.data,
                    "sender_id": user_id,
                    "sender_name": sender_name,
                    "is_broadcast": True
                },
                "expires_at": notification_data.expires_at,
                "created_at": datetime.utcnow(),
                "updated_at": datetime.utcnow()
            }
            notifications.append(notification_doc)
        
        # Insert notifications
        result = await db[Collections.NOTIFICATIONS].insert_many(notifications)
        
        # Create audit log
        await create_audit_log(
            user_id=user_id,
            action="broadcast_notification",
            resource_type="notification",
            resource_id="broadcast",
            details={
                "title": notification_data.title,
                "recipient_count": len(users),
                "type": notification_data.notification_type,
                "department": department,
                "role": role
            }
        )
        
        return {
            "message": f"Notification broadcast to {len(users)} users",
            "notification_ids": [str(id) for id in result.inserted_ids]
        }
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to broadcast notification"
        )


# Helper function to create system notifications
async def create_system_notification(
    user_id: str,
    title: str,
    message: str,
    notification_type: str = "info",
    data: Dict[str, Any] = None,
    db: AsyncIOMotorDatabase = None
):
    """Create a system notification for a user"""
    
    if not db:
        return
    
    try:
        notification_doc = {
            "user_id": ObjectId(user_id),
            "title": title,
            "message": message,
            "type": notification_type,
            "is_read": False,
            "read_at": None,
            "data": data or {},
            "created_at": datetime.utcnow(),
            "updated_at": datetime.utcnow()
        }
        
        await db[Collections.NOTIFICATIONS].insert_one(notification_doc)
        
    except Exception as e:
        # Log error but don't raise - notifications shouldn't break main functionality
        print(f"Failed to create system notification: {e}")


# Helper function to create task notifications
async def create_task_notification(
    task_id: str,
    assignee_id: str,
    action: str,
    actor_name: str,
    db: AsyncIOMotorDatabase
):
    """Create task-related notification"""
    
    action_messages = {
        "assigned": f"You have been assigned a new task by {actor_name}",
        "updated": f"A task assigned to you has been updated by {actor_name}",
        "completed": f"A task has been marked as completed by {actor_name}",
        "commented": f"{actor_name} commented on your task"
    }
    
    message = action_messages.get(action, f"Task action: {action}")
    
    await create_system_notification(
        user_id=assignee_id,
        title="Task Update",
        message=message,
        notification_type="info",
        data={
            "task_id": task_id,
            "action": action,
            "actor_name": actor_name
        },
        db=db
    )
