"""
Projects API routes
"""
from fastapi import APIRouter, Depends, HTTPException, status, Query
from motor.motor_asyncio import AsyncIOMotorDatabase
from typing import Dict, Any, List, Optional
from datetime import datetime
from bson import ObjectId
from pydantic import BaseModel

from ..core.database import get_database, create_audit_log
from ..core.security import get_current_user_token, require_manager_or_admin
from ..core.config import Collections
from ..models.project import Project, ProjectStatus, Priority

router = APIRouter()


class ProjectCreate(BaseModel):
    name: str
    description: Optional[str] = None
    status: ProjectStatus = ProjectStatus.PLANNING
    priority: Priority = Priority.MEDIUM
    department: str
    client: Optional[str] = None
    start_date: Optional[datetime] = None
    end_date: Optional[datetime] = None
    estimated_hours: Optional[float] = None
    team_members: List[str] = []
    tags: List[str] = []
    categories: List[str] = []


class ProjectUpdate(BaseModel):
    name: Optional[str] = None
    description: Optional[str] = None
    status: Optional[ProjectStatus] = None
    priority: Optional[Priority] = None
    progress: Optional[float] = None
    start_date: Optional[datetime] = None
    end_date: Optional[datetime] = None
    estimated_hours: Optional[float] = None
    team_members: Optional[List[str]] = None
    tags: Optional[List[str]] = None
    categories: Optional[List[str]] = None


@router.post("/")
async def create_project(
    project_data: ProjectCreate,
    current_user: Dict[str, Any] = Depends(require_manager_or_admin),
    db: AsyncIOMotorDatabase = Depends(get_database)
):
    """Create a new project (Manager/Admin only)"""
    
    user_id = current_user.get("sub")
    user_role = current_user.get("role")
    user_department = current_user.get("department")
    
    try:
        # Validate department access
        if user_role != "admin" and project_data.department != user_department:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Cannot create projects outside your department"
            )
        
        # Validate team members exist
        team_member_ids = []
        if project_data.team_members:
            for member_id in project_data.team_members:
                member = await db[Collections.USERS].find_one({"_id": ObjectId(member_id)})
                if not member:
                    raise HTTPException(
                        status_code=status.HTTP_400_BAD_REQUEST,
                        detail=f"Team member {member_id} not found"
                    )
                team_member_ids.append(ObjectId(member_id))
        
        # Create project document
        project_doc = {
            "name": project_data.name,
            "description": project_data.description,
            "status": project_data.status.value,
            "priority": project_data.priority.value,
            "progress": 0.0,
            "manager_id": ObjectId(user_id),
            "team_members": team_member_ids,
            "department": project_data.department,
            "client": project_data.client,
            "start_date": project_data.start_date,
            "end_date": project_data.end_date,
            "estimated_hours": project_data.estimated_hours,
            "actual_hours": 0.0,
            "tags": project_data.tags,
            "categories": project_data.categories,
            "milestones": [],
            "attachments": [],
            "risks": [],
            "issues": [],
            "created_at": datetime.utcnow(),
            "updated_at": datetime.utcnow(),
            "created_by": ObjectId(user_id),
            "is_template": False,
            "is_archived": False,
            "visibility": "team"
        }
        
        result = await db[Collections.PROJECTS].insert_one(project_doc)
        
        # Create audit log
        await create_audit_log(
            user_id=user_id,
            action="create_project",
            resource_type="project",
            resource_id=str(result.inserted_id),
            details={"name": project_data.name, "department": project_data.department}
        )
        
        # Get created project
        created_project = await db[Collections.PROJECTS].find_one({"_id": result.inserted_id})
        created_project["id"] = str(created_project["_id"])
        del created_project["_id"]
        
        # Convert ObjectIds to strings
        created_project["manager_id"] = str(created_project["manager_id"])
        created_project["team_members"] = [str(member_id) for member_id in created_project["team_members"]]
        created_project["created_by"] = str(created_project["created_by"])
        
        return created_project
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to create project"
        )


@router.get("/")
async def get_projects(
    page: int = Query(1, ge=1),
    size: int = Query(20, ge=1, le=100),
    status: Optional[str] = Query(None),
    priority: Optional[str] = Query(None),
    department: Optional[str] = Query(None),
    search: Optional[str] = Query(None),
    current_user: Dict[str, Any] = Depends(get_current_user_token),
    db: AsyncIOMotorDatabase = Depends(get_database)
):
    """Get projects with pagination and filtering"""
    
    user_id = current_user.get("sub")
    user_role = current_user.get("role")
    user_department = current_user.get("department")
    
    try:
        # Build filter query
        filter_query = {"is_archived": False}
        
        # Role-based filtering
        if user_role not in ["admin", "hr"]:
            # Non-admin users see only their department's projects or projects they're assigned to
            filter_query["$or"] = [
                {"department": user_department},
                {"team_members": ObjectId(user_id)},
                {"manager_id": ObjectId(user_id)}
            ]
        
        if status:
            filter_query["status"] = status
        
        if priority:
            filter_query["priority"] = priority
        
        if department and user_role in ["admin", "hr"]:
            filter_query["department"] = department
        
        if search:
            filter_query["$or"] = [
                {"name": {"$regex": search, "$options": "i"}},
                {"description": {"$regex": search, "$options": "i"}},
                {"tags": {"$in": [{"$regex": search, "$options": "i"}]}},
                {"client": {"$regex": search, "$options": "i"}}
            ]
        
        # Get total count
        total = await db[Collections.PROJECTS].count_documents(filter_query)
        
        # Calculate pagination
        skip = (page - 1) * size
        pages = (total + size - 1) // size
        
        # Get projects
        projects = await db[Collections.PROJECTS].find(filter_query).sort("updated_at", -1).skip(skip).limit(size).to_list(length=None)
        
        # Format response
        formatted_projects = []
        for project in projects:
            # Get manager name
            manager = await db[Collections.USERS].find_one({"_id": project["manager_id"]})
            manager_name = f"{manager['profile']['first_name']} {manager['profile']['last_name']}" if manager else "Unknown"
            
            # Get team member count
            team_count = len(project.get("team_members", []))
            
            # Calculate task statistics
            task_stats = await db[Collections.TASKS].aggregate([
                {"$match": {"project_id": project["_id"]}},
                {"$group": {
                    "_id": "$status",
                    "count": {"$sum": 1}
                }}
            ]).to_list(length=None)
            
            task_summary = {stat["_id"]: stat["count"] for stat in task_stats}
            total_tasks = sum(task_summary.values())
            
            formatted_projects.append({
                "id": str(project["_id"]),
                "name": project["name"],
                "description": project.get("description"),
                "status": project["status"],
                "priority": project["priority"],
                "progress": project.get("progress", 0.0),
                "manager_id": str(project["manager_id"]),
                "manager_name": manager_name,
                "department": project["department"],
                "client": project.get("client"),
                "start_date": project.get("start_date"),
                "end_date": project.get("end_date"),
                "estimated_hours": project.get("estimated_hours"),
                "actual_hours": project.get("actual_hours", 0.0),
                "team_count": team_count,
                "task_summary": task_summary,
                "total_tasks": total_tasks,
                "tags": project.get("tags", []),
                "created_at": project["created_at"],
                "updated_at": project["updated_at"]
            })
        
        return {
            "projects": formatted_projects,
            "total": total,
            "page": page,
            "size": size,
            "pages": pages
        }
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve projects"
        )


@router.get("/{project_id}")
async def get_project(
    project_id: str,
    current_user: Dict[str, Any] = Depends(get_current_user_token),
    db: AsyncIOMotorDatabase = Depends(get_database)
):
    """Get project by ID"""
    
    user_id = current_user.get("sub")
    user_role = current_user.get("role")
    user_department = current_user.get("department")
    
    try:
        project = await db[Collections.PROJECTS].find_one({"_id": ObjectId(project_id)})
        
        if not project:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Project not found"
            )
        
        # Check access permissions
        has_access = (
            user_role in ["admin", "hr"] or
            project["department"] == user_department or
            ObjectId(user_id) in project.get("team_members", []) or
            project["manager_id"] == ObjectId(user_id)
        )
        
        if not has_access:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Access denied"
            )
        
        # Get detailed project information
        # Manager details
        manager = await db[Collections.USERS].find_one({"_id": project["manager_id"]})
        manager_info = {
            "id": str(manager["_id"]),
            "name": f"{manager['profile']['first_name']} {manager['profile']['last_name']}",
            "email": manager["email"]
        } if manager else None
        
        # Team member details
        team_members = []
        for member_id in project.get("team_members", []):
            member = await db[Collections.USERS].find_one({"_id": member_id})
            if member:
                team_members.append({
                    "id": str(member["_id"]),
                    "name": f"{member['profile']['first_name']} {member['profile']['last_name']}",
                    "email": member["email"],
                    "role": member.get("position", member["role"]),
                    "avatar_url": member.get("avatar_url")
                })
        
        # Task statistics
        task_pipeline = [
            {"$match": {"project_id": project["_id"]}},
            {"$group": {
                "_id": "$status",
                "count": {"$sum": 1},
                "total_hours": {"$sum": "$actual_hours"}
            }}
        ]
        task_stats = await db[Collections.TASKS].aggregate(task_pipeline).to_list(length=None)
        
        task_summary = {}
        total_task_hours = 0
        for stat in task_stats:
            task_summary[stat["_id"]] = {
                "count": stat["count"],
                "hours": stat.get("total_hours", 0)
            }
            total_task_hours += stat.get("total_hours", 0)
        
        # Recent activities
        recent_tasks = await db[Collections.TASKS].find({
            "project_id": project["_id"]
        }).sort("updated_at", -1).limit(5).to_list(length=None)
        
        recent_activities = []
        for task in recent_tasks:
            assignee = await db[Collections.USERS].find_one({"_id": task.get("assignee_id")}) if task.get("assignee_id") else None
            recent_activities.append({
                "id": str(task["_id"]),
                "title": task["title"],
                "status": task["status"],
                "assignee_name": f"{assignee['profile']['first_name']} {assignee['profile']['last_name']}" if assignee else None,
                "updated_at": task["updated_at"]
            })
        
        # Format response
        project_response = {
            "id": str(project["_id"]),
            "name": project["name"],
            "description": project.get("description"),
            "status": project["status"],
            "priority": project["priority"],
            "progress": project.get("progress", 0.0),
            "manager": manager_info,
            "team_members": team_members,
            "department": project["department"],
            "client": project.get("client"),
            "start_date": project.get("start_date"),
            "end_date": project.get("end_date"),
            "estimated_hours": project.get("estimated_hours"),
            "actual_hours": project.get("actual_hours", 0.0),
            "task_hours": total_task_hours,
            "tags": project.get("tags", []),
            "categories": project.get("categories", []),
            "milestones": project.get("milestones", []),
            "task_summary": task_summary,
            "recent_activities": recent_activities,
            "attachments": project.get("attachments", []),
            "risks": project.get("risks", []),
            "issues": project.get("issues", []),
            "created_at": project["created_at"],
            "updated_at": project["updated_at"],
            "created_by": str(project["created_by"]),
            "visibility": project.get("visibility", "team")
        }
        
        return project_response
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve project"
        )


@router.put("/{project_id}")
async def update_project(
    project_id: str,
    update_data: ProjectUpdate,
    current_user: Dict[str, Any] = Depends(get_current_user_token),
    db: AsyncIOMotorDatabase = Depends(get_database)
):
    """Update project"""
    
    user_id = current_user.get("sub")
    user_role = current_user.get("role")
    
    try:
        project = await db[Collections.PROJECTS].find_one({"_id": ObjectId(project_id)})
        
        if not project:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Project not found"
            )
        
        # Check permissions (manager or admin)
        can_update = (
            user_role in ["admin"] or
            project["manager_id"] == ObjectId(user_id)
        )
        
        if not can_update:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Permission denied"
            )
        
        # Build update document
        update_doc = {"updated_at": datetime.utcnow()}
        
        if update_data.name is not None:
            update_doc["name"] = update_data.name
        
        if update_data.description is not None:
            update_doc["description"] = update_data.description
        
        if update_data.status is not None:
            update_doc["status"] = update_data.status.value
        
        if update_data.priority is not None:
            update_doc["priority"] = update_data.priority.value
        
        if update_data.progress is not None:
            update_doc["progress"] = max(0.0, min(100.0, update_data.progress))
        
        if update_data.start_date is not None:
            update_doc["start_date"] = update_data.start_date
        
        if update_data.end_date is not None:
            update_doc["end_date"] = update_data.end_date
        
        if update_data.estimated_hours is not None:
            update_doc["estimated_hours"] = update_data.estimated_hours
        
        if update_data.team_members is not None:
            # Validate team members
            team_member_ids = []
            for member_id in update_data.team_members:
                member = await db[Collections.USERS].find_one({"_id": ObjectId(member_id)})
                if not member:
                    raise HTTPException(
                        status_code=status.HTTP_400_BAD_REQUEST,
                        detail=f"Team member {member_id} not found"
                    )
                team_member_ids.append(ObjectId(member_id))
            update_doc["team_members"] = team_member_ids
        
        if update_data.tags is not None:
            update_doc["tags"] = update_data.tags
        
        if update_data.categories is not None:
            update_doc["categories"] = update_data.categories
        
        # Update project
        await db[Collections.PROJECTS].update_one(
            {"_id": ObjectId(project_id)},
            {"$set": update_doc}
        )
        
        # Create audit log
        await create_audit_log(
            user_id=user_id,
            action="update_project",
            resource_type="project",
            resource_id=project_id,
            details=update_doc
        )
        
        # Return updated project
        return await get_project(project_id, current_user, db)
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to update project"
        )


@router.delete("/{project_id}")
async def delete_project(
    project_id: str,
    current_user: Dict[str, Any] = Depends(require_manager_or_admin),
    db: AsyncIOMotorDatabase = Depends(get_database)
):
    """Archive project (soft delete)"""
    
    user_id = current_user.get("sub")
    user_role = current_user.get("role")
    
    try:
        project = await db[Collections.PROJECTS].find_one({"_id": ObjectId(project_id)})
        
        if not project:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Project not found"
            )
        
        # Check permissions
        can_delete = (
            user_role == "admin" or
            project["manager_id"] == ObjectId(user_id)
        )
        
        if not can_delete:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Permission denied"
            )
        
        # Archive project (soft delete)
        await db[Collections.PROJECTS].update_one(
            {"_id": ObjectId(project_id)},
            {
                "$set": {
                    "is_archived": True,
                    "archived_at": datetime.utcnow(),
                    "archived_by": ObjectId(user_id),
                    "updated_at": datetime.utcnow()
                }
            }
        )
        
        # Archive associated tasks
        await db[Collections.TASKS].update_many(
            {"project_id": ObjectId(project_id)},
            {
                "$set": {
                    "is_archived": True,
                    "archived_at": datetime.utcnow(),
                    "updated_at": datetime.utcnow()
                }
            }
        )
        
        # Create audit log
        await create_audit_log(
            user_id=user_id,
            action="archive_project",
            resource_type="project",
            resource_id=project_id,
            details={"name": project["name"]}
        )
        
        return {"message": "Project archived successfully"}
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to archive project"
        )
