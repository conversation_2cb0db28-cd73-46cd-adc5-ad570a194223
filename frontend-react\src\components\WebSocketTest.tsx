/**
 * WebSocket Test Component with comprehensive testing
 */
import React, { useState, useEffect, useCallback } from 'react'
import { logger } from '../utils/logger'
import type { User } from '../types'

interface WebSocketTestProps {
  user?: User | null
}

export default function WebSocketTest() {
  const { user, isAuthenticated } = useAuth()
  const { isConnected, send, subscribe, joinRoom, leaveRoom, sendPing } = useWebSocket()
  const [messages, setMessages] = useState<string[]>([])
  const [testMessage, setTestMessage] = useState('')

  useEffect(() => {
    if (isConnected) {
      addMessage('✅ WebSocket connected successfully!')
      
      // Subscribe to various message types
      const unsubscribeConnection = subscribe('connection_established', (data: any) => {
        addMessage(`🎉 Connection established: ${JSON.stringify(data)}`)
      })
      
      const unsubscribePong = subscribe('pong', (data: any) => {
        addMessage(`🏓 Pong received: ${JSON.stringify(data)}`)
      })
      
      const unsubscribeRoomJoined = subscribe('room_joined', (data: any) => {
        addMessage(`🚪 Room joined: ${JSON.stringify(data)}`)
      })
      
      const unsubscribeRoomLeft = subscribe('room_left', (data: any) => {
        addMessage(`🚪 Room left: ${JSON.stringify(data)}`)
      })

      const unsubscribeTaskUpdate = subscribe('task_update', (data: any) => {
        addMessage(`📋 Task update: ${JSON.stringify(data)}`)
      })

      const unsubscribeNotification = subscribe('notification', (data: any) => {
        addMessage(`🔔 Notification: ${JSON.stringify(data)}`)
      })
      
      return () => {
        unsubscribeConnection()
        unsubscribePong()
        unsubscribeRoomJoined()
        unsubscribeRoomLeft()
        unsubscribeTaskUpdate()
        unsubscribeNotification()
      }
    } else {
      addMessage('❌ WebSocket disconnected')
    }
  }, [isConnected, subscribe])

  const addMessage = (message: string) => {
    const timestamp = new Date().toLocaleTimeString()
    setMessages(prev => [`[${timestamp}] ${message}`, ...prev.slice(0, 19)]) // Keep last 20 messages
  }

  const handleSendPing = () => {
    if (isConnected) {
      sendPing()
      addMessage('🏓 Ping sent')
    } else {
      addMessage('❌ Cannot send ping - not connected')
    }
  }

  const sendTestMessage = () => {
    if (isConnected && testMessage.trim()) {
      send({ type: 'test_message', message: testMessage })
      addMessage(`📤 Sent test message: ${testMessage}`)
      setTestMessage('')
    }
  }

  const joinTestRoom = () => {
    if (isConnected) {
      joinRoom('test_room')
      addMessage('🚪 Joining test room')
    }
  }

  const leaveTestRoom = () => {
    if (isConnected) {
      leaveRoom('test_room')
      addMessage('🚪 Leaving test room')
    }
  }

  const clearMessages = () => {
    setMessages([])
  }

  if (!isAuthenticated) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="bg-white p-8 rounded-lg shadow-md">
          <h1 className="text-2xl font-bold text-gray-900 mb-4">WebSocket Test</h1>
          <p className="text-gray-600">Please log in to test WebSocket connection.</p>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50 p-8">
      <div className="max-w-4xl mx-auto">
        <h1 className="text-3xl font-bold text-gray-900 mb-8">WebSocket Connection Test</h1>
        
        {/* Connection Status */}
        <div className="bg-white rounded-lg shadow-md p-6 mb-6">
          <h2 className="text-xl font-semibold mb-4">Connection Status</h2>
          <div className="flex items-center space-x-4">
            <div className={`w-4 h-4 rounded-full ${isConnected ? 'bg-green-500' : 'bg-red-500'}`}></div>
            <span className={`font-medium ${isConnected ? 'text-green-700' : 'text-red-700'}`}>
              {isConnected ? 'Connected' : 'Disconnected'}
            </span>
          </div>
          {user && (
            <div className="mt-4 text-sm text-gray-600">
              <p>User: {user.username} ({user.role})</p>
              <p>Department: {user.department}</p>
            </div>
          )}
        </div>

        {/* Test Controls */}
        <div className="bg-white rounded-lg shadow-md p-6 mb-6">
          <h2 className="text-xl font-semibold mb-4">Test Controls</h2>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <button
              onClick={handleSendPing}
              disabled={!isConnected}
              className="px-4 py-2 bg-red-500 text-white rounded hover:bg-red-600 disabled:bg-gray-300"
            >
              Send Ping
            </button>
            <button
              onClick={joinTestRoom}
              disabled={!isConnected}
              className="px-4 py-2 bg-green-500 text-white rounded hover:bg-green-600 disabled:bg-gray-300"
            >
              Join Test Room
            </button>
            <button
              onClick={leaveTestRoom}
              disabled={!isConnected}
              className="px-4 py-2 bg-yellow-500 text-white rounded hover:bg-yellow-600 disabled:bg-gray-300"
            >
              Leave Test Room
            </button>
            <button
              onClick={clearMessages}
              className="px-4 py-2 bg-red-500 text-white rounded hover:bg-red-600"
            >
              Clear Messages
            </button>
          </div>
          
          <div className="mt-4 flex space-x-2">
            <input
              type="text"
              value={testMessage}
              onChange={(e) => setTestMessage(e.target.value)}
              placeholder="Enter test message"
              className="flex-1 px-3 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-red-500"
              onKeyPress={(e) => e.key === 'Enter' && sendTestMessage()}
            />
            <button
              onClick={sendTestMessage}
              disabled={!isConnected || !testMessage.trim()}
              className="px-4 py-2 bg-red-500 text-white rounded hover:bg-red-600 disabled:bg-gray-300"
            >
              Send Message
            </button>
          </div>
        </div>

        {/* Message Log */}
        <div className="bg-white rounded-lg shadow-md p-6">
          <h2 className="text-xl font-semibold mb-4">Message Log</h2>
          <div className="bg-gray-100 rounded p-4 h-96 overflow-y-auto">
            {messages.length === 0 ? (
              <p className="text-gray-500 italic">No messages yet...</p>
            ) : (
              messages.map((message, index) => (
                <div key={index} className="mb-2 font-mono text-sm">
                  {message}
                </div>
              ))
            )}
          </div>
        </div>
      </div>
    </div>
  )
}
