import React, { useState, useEffect } from 'react'
import ErrorBoundary from './components/ErrorBoundary'

import Dashboard from './components/Dashboard'
import WebSocketTest from './components/WebSocketTest'
import LoginPage from './pages/LoginPage'
import SignupPage from './pages/SignupPage'
import ClockInPage from './pages/ClockInPage'
import DashboardPage from './pages/DashboardPage'
import TasksPage from './pages/TasksPage'
import RolesPage from './pages/RolesPage'
import MemosPage from './pages/MemosPage'
import ReportsPage from './pages/ReportsPage'
import ProjectsPage from './pages/ProjectsPage'
import FilesPage from './pages/FilesPage'
import NotificationsPage from './pages/NotificationsPage'
import AIAssistantPage from './pages/AIAssistantPage'
import { logger } from './utils/logger'
import type { User } from './types'
import './App.css'

function App() {
  const [currentView, setCurrentView] = useState<'login' | 'signup' | 'clockin' | 'logs' | 'dashboard' | 'websocket' | 'enhanced-dashboard' | 'tasks' | 'roles' | 'memos' | 'reports' | 'projects' | 'files' | 'notifications' | 'ai-assistant'>('clockin')
  const [currentUser, setCurrentUser] = useState<User | null>({
    id: 'demo-user',
    email: '<EMAIL>',
    name: 'Demo User',
    role: 'employee'
  })

  useEffect(() => {
    logger.info('Application started', 'App', {
      timestamp: new Date().toISOString(),
      userAgent: navigator.userAgent,
      url: window.location.href
    })

    // Check for existing user session
    const savedUser = localStorage.getItem('user')
    if (savedUser) {
      try {
        const user = JSON.parse(savedUser)
        setCurrentUser(user)
        logger.info('User session restored', 'App', { username: user.username })
      } catch (error) {
        logger.error('Failed to parse saved user', 'App', error)
        localStorage.removeItem('user')
        localStorage.removeItem('authToken')
      }
    }

    // Global error handler for unhandled promise rejections
    const handleUnhandledRejection = (event: PromiseRejectionEvent) => {
      logger.error('Unhandled promise rejection', 'App', event.reason)
      event.preventDefault()
    }

    window.addEventListener('unhandledrejection', handleUnhandledRejection)

    return () => {
      window.removeEventListener('unhandledrejection', handleUnhandledRejection)
      logger.info('Application unmounted', 'App')
    }
  }, [])



  const handleViewLogs = () => {
    setCurrentView('logs')
  }

  const handleViewDashboard = () => {
    setCurrentView('dashboard')
  }

  const handleViewWebSocket = () => {
    setCurrentView('websocket')
  }

  const handleViewSignup = () => {
    setCurrentView('signup')
  }

  const handleViewClockIn = () => {
    setCurrentView('clockin')
  }

  const handleLoginSuccess = (user: User) => {
    setCurrentUser(user)
    setCurrentView('enhanced-dashboard')
    logger.info('User logged in successfully', 'App', { username: user.username })
  }

  const handleSignupSuccess = (user: User) => {
    setCurrentUser(user)
    setCurrentView('enhanced-dashboard')
    logger.info('User signed up successfully', 'App', { username: user.username })
  }

  const handleNavigateToTasks = () => {
    setCurrentView('tasks')
  }

  const handleNavigateToRoles = () => {
    setCurrentView('roles')
  }

  const handleNavigateToMemos = () => {
    setCurrentView('memos')
  }

  const handleNavigateToReports = () => {
    setCurrentView('reports')
  }

  const handleNavigateToProjects = () => {
    setCurrentView('projects')
  }

  const handleNavigateToFiles = () => {
    setCurrentView('files')
  }

  const handleNavigateToNotifications = () => {
    setCurrentView('notifications')
  }

  const handleNavigateToAI = () => {
    setCurrentView('ai-assistant')
  }

  const handleLogout = () => {
    logger.info('User logout', 'App')
    localStorage.removeItem('authToken')
    localStorage.removeItem('user')
    setCurrentUser(null)
    setCurrentView('login')
  }

  const renderLogsView = () => {
    const logs = logger.getLogs()
    return (
      <div style={{ padding: '20px', fontFamily: 'monospace', fontSize: '12px' }}>
        <div style={{ marginBottom: '20px' }}>
          <button
            onClick={() => setCurrentView('login')}
            style={{
              padding: '8px 16px',
              backgroundColor: '#6c757d',
              color: 'white',
              border: 'none',
              borderRadius: '4px',
              cursor: 'pointer',
              marginRight: '10px'
            }}
          >
            ← Back to Login
          </button>
          <button
            onClick={() => logger.clearLogs()}
            style={{
              padding: '8px 16px',
              backgroundColor: '#dc3545',
              color: 'white',
              border: 'none',
              borderRadius: '4px',
              cursor: 'pointer'
            }}
          >
            Clear Logs
          </button>
        </div>

        <h2>Application Logs ({logs.length} entries)</h2>
        <div style={{
          backgroundColor: '#f8f9fa',
          border: '1px solid #dee2e6',
          borderRadius: '4px',
          padding: '16px',
          maxHeight: '70vh',
          overflow: 'auto'
        }}>
          {logs.map((log, index) => (
            <div
              key={index}
              style={{
                marginBottom: '8px',
                padding: '4px',
                backgroundColor: log.level >= 2 ? '#fff3cd' : 'transparent',
                borderLeft: `3px solid ${
                  log.level === 0 ? '#6c757d' :
                  log.level === 1 ? '#007bff' :
                  log.level === 2 ? '#ffc107' : '#dc3545'
                }`
              }}
            >
              <span style={{ color: '#6c757d' }}>
                {log.timestamp.toLocaleTimeString()}
              </span>
              {log.context && (
                <span style={{ color: '#007bff', marginLeft: '8px' }}>
                  [{log.context}]
                </span>
              )}
              <span style={{ marginLeft: '8px' }}>{log.message}</span>
              {log.data && (
                <pre style={{
                  margin: '4px 0 0 0',
                  fontSize: '10px',
                  color: '#6c757d'
                }}>
                  {JSON.stringify(log.data, null, 2)}
                </pre>
              )}
            </div>
          ))}
        </div>
      </div>
    )
  }

  return (
    <ErrorBoundary>
      <div className="App">
        {currentView === 'login' ? (
          <LoginPage
            onLoginSuccess={handleLoginSuccess}
            onNavigateToSignup={handleViewSignup}
            onNavigateToClockIn={handleViewClockIn}
          />
        ) : currentView === 'signup' ? (
          <SignupPage
            onSignupSuccess={handleSignupSuccess}
            onNavigateToLogin={() => setCurrentView('login')}
          />
        ) : currentView === 'clockin' ? (
          <ClockInPage
            user={currentUser}
            onNavigateToLogin={() => setCurrentView('login')}
            onNavigateToDashboard={handleViewDashboard}
          />
        ) : currentView === 'enhanced-dashboard' ? (
          <DashboardPage
            user={currentUser!}
            onNavigateToTasks={handleNavigateToTasks}
            onNavigateToReports={handleNavigateToReports}
            onNavigateToMemos={handleNavigateToMemos}
            onNavigateToProjects={handleNavigateToProjects}
            onNavigateToFiles={handleNavigateToFiles}
            onNavigateToNotifications={handleNavigateToNotifications}
            onNavigateToAI={handleNavigateToAI}
            onLogout={handleLogout}
          />
        ) : currentView === 'tasks' ? (
          <TasksPage
            user={currentUser!}
            onNavigateBack={() => setCurrentView('enhanced-dashboard')}
          />
        ) : currentView === 'roles' ? (
          <RolesPage
            user={currentUser!}
            onNavigateBack={() => setCurrentView('enhanced-dashboard')}
          />
        ) : currentView === 'memos' ? (
          <MemosPage
            user={currentUser!}
            onNavigateBack={() => setCurrentView('enhanced-dashboard')}
          />
        ) : currentView === 'reports' ? (
          <ReportsPage
            user={currentUser!}
            onNavigateBack={() => setCurrentView('enhanced-dashboard')}
          />
        ) : currentView === 'projects' ? (
          <ProjectsPage
            user={currentUser!}
            onNavigateBack={() => setCurrentView('enhanced-dashboard')}
          />
        ) : currentView === 'files' ? (
          <FilesPage
            user={currentUser!}
            onNavigateBack={() => setCurrentView('enhanced-dashboard')}
          />
        ) : currentView === 'notifications' ? (
          <NotificationsPage
            user={currentUser!}
            onNavigateBack={() => setCurrentView('enhanced-dashboard')}
          />
        ) : currentView === 'ai-assistant' ? (
          <AIAssistantPage
            user={currentUser!}
            onNavigateBack={() => setCurrentView('enhanced-dashboard')}
          />
        ) : currentView === 'dashboard' ? (
          <Dashboard user={currentUser} onLogout={handleLogout} />
        ) : currentView === 'websocket' ? (
          <div>
            <div style={{ position: 'absolute', top: '20px', left: '20px', zIndex: 1000 }}>
              <button
                onClick={() => setCurrentView('test')}
                style={{
                  padding: '8px 16px',
                  backgroundColor: '#6c757d',
                  color: 'white',
                  border: 'none',
                  borderRadius: '4px',
                  cursor: 'pointer'
                }}
              >
                ← Back to Test
              </button>
            </div>
            <WebSocketTest user={currentUser} />
          </div>
        ) : (
          renderLogsView()
        )}
      </div>
    </ErrorBoundary>
  )
}

export default App
