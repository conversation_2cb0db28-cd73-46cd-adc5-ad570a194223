#!/usr/bin/env python3
"""
Test the simplified database connection
"""

import asyncio
import sys
import os

# Add the app directory to the path
sys.path.append(os.path.join(os.path.dirname(__file__), 'app'))

from app.core.database_simple import connect_to_mongo, test_connection, close_mongo_connection

async def main():
    print("🧪 Testing Simplified MongoDB Connection...")
    
    # Test connection
    success = await connect_to_mongo()
    
    if success:
        print("✅ Connection successful!")
        
        # Test database operations
        test_result = await test_connection()
        
        if test_result:
            print("✅ Database operations working!")
        else:
            print("❌ Database operations failed")
            
        # Close connection
        await close_mongo_connection()
        print("✅ Connection closed")
        
    else:
        print("❌ Connection failed")

if __name__ == "__main__":
    asyncio.run(main())
