"""
Users API routes
"""
from fastapi import APIRouter, Depends, HTTPException, status, Query
from motor.motor_asyncio import AsyncIOMotorDatabase
from typing import Dict, Any, List, Optional
from bson import ObjectId

from ..core.database import get_database
from ..core.security import (
    get_current_user_token, require_admin, require_manager_or_admin,
    check_department_access, check_resource_ownership
)
from ..services.user_service import UserService
from ..schemas.user import (
    UserCreate, UserUpdate, UserResponse, UserListResponse,
    PaginatedUserResponse, UserPreferencesUpdate, UserStats
)

router = APIRouter()


@router.post("/", response_model=UserResponse)
async def create_user(
    user_data: UserCreate,
    current_user: Dict[str, Any] = Depends(require_admin),
    db: AsyncIOMotorDatabase = Depends(get_database)
):
    """Create a new user (Admin only)"""
    user_service = UserService(db)
    created_by = current_user.get("sub")
    
    try:
        user = await user_service.create_user(user_data, created_by)
        return user
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to create user"
        )


@router.get("/", response_model=PaginatedUserResponse)
async def get_users(
    page: int = Query(1, ge=1),
    size: int = Query(20, ge=1, le=100),
    department: Optional[str] = Query(None),
    role: Optional[str] = Query(None),
    is_active: Optional[bool] = Query(None),
    search: Optional[str] = Query(None),
    current_user: Dict[str, Any] = Depends(get_current_user_token),
    db: AsyncIOMotorDatabase = Depends(get_database)
):
    """Get users with pagination and filtering"""
    user_service = UserService(db)
    
    # Build filter query
    filter_query = {}
    
    if department:
        # Check if user has access to this department
        if not check_department_access(current_user, department):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Access to this department not permitted"
            )
        filter_query["department"] = department
    
    if role:
        filter_query["role"] = role
    
    if is_active is not None:
        filter_query["is_active"] = is_active
    
    if search:
        filter_query["$or"] = [
            {"profile.first_name": {"$regex": search, "$options": "i"}},
            {"profile.last_name": {"$regex": search, "$options": "i"}},
            {"email": {"$regex": search, "$options": "i"}},
            {"username": {"$regex": search, "$options": "i"}},
            {"employee_id": {"$regex": search, "$options": "i"}}
        ]
    
    # If not admin, limit to user's department
    user_role = current_user.get("role")
    if user_role not in ["admin", "hr"]:
        user_department = current_user.get("department")
        filter_query["department"] = user_department
    
    try:
        # Get total count
        total = await db.users.count_documents(filter_query)
        
        # Calculate pagination
        skip = (page - 1) * size
        pages = (total + size - 1) // size
        
        # Get users
        cursor = db.users.find(filter_query, {
            "hashed_password": 0,
            "security": 0
        }).skip(skip).limit(size).sort("created_at", -1)
        
        users = await cursor.to_list(length=None)
        
        # Format response
        user_list = []
        for user in users:
            user_list.append({
                "id": str(user["_id"]),
                "employee_id": user["employee_id"],
                "email": user["email"],
                "username": user["username"],
                "first_name": user["profile"]["first_name"],
                "last_name": user["profile"]["last_name"],
                "role": user["role"],
                "department": user["department"],
                "position": user.get("position"),
                "is_active": user["is_active"],
                "is_online": user.get("is_online", False),
                "avatar_url": user.get("avatar_url"),
                "last_login": user.get("activity", {}).get("last_login")
            })
        
        return {
            "users": user_list,
            "total": total,
            "page": page,
            "size": size,
            "pages": pages
        }
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve users"
        )


@router.get("/stats", response_model=UserStats)
async def get_user_stats(
    current_user: Dict[str, Any] = Depends(require_manager_or_admin),
    db: AsyncIOMotorDatabase = Depends(get_database)
):
    """Get user statistics"""
    try:
        # Build base filter for department access
        base_filter = {}
        user_role = current_user.get("role")
        if user_role not in ["admin", "hr"]:
            base_filter["department"] = current_user.get("department")
        
        # Get total users
        total_users = await db.users.count_documents(base_filter)
        
        # Get active users
        active_filter = {**base_filter, "is_active": True}
        active_users = await db.users.count_documents(active_filter)
        
        # Get online users
        online_filter = {**base_filter, "is_online": True}
        online_users = await db.users.count_documents(online_filter)
        
        # Get users by role
        role_pipeline = [
            {"$match": base_filter},
            {"$group": {"_id": "$role", "count": {"$sum": 1}}}
        ]
        role_stats = await db.users.aggregate(role_pipeline).to_list(length=None)
        users_by_role = {stat["_id"]: stat["count"] for stat in role_stats}
        
        # Get users by department
        dept_pipeline = [
            {"$match": base_filter},
            {"$group": {"_id": "$department", "count": {"$sum": 1}}}
        ]
        dept_stats = await db.users.aggregate(dept_pipeline).to_list(length=None)
        users_by_department = {stat["_id"]: stat["count"] for stat in dept_stats}
        
        # Get recent logins (last 24 hours)
        from datetime import datetime, timedelta
        yesterday = datetime.utcnow() - timedelta(days=1)
        recent_login_filter = {
            **base_filter,
            "activity.last_login": {"$gte": yesterday}
        }
        recent_logins = await db.users.count_documents(recent_login_filter)
        
        # Get new users this month
        month_start = datetime.utcnow().replace(day=1, hour=0, minute=0, second=0, microsecond=0)
        new_users_filter = {
            **base_filter,
            "created_at": {"$gte": month_start}
        }
        new_users_this_month = await db.users.count_documents(new_users_filter)
        
        return {
            "total_users": total_users,
            "active_users": active_users,
            "online_users": online_users,
            "users_by_role": users_by_role,
            "users_by_department": users_by_department,
            "recent_logins": recent_logins,
            "new_users_this_month": new_users_this_month
        }
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve user statistics"
        )


@router.get("/{user_id}", response_model=UserResponse)
async def get_user(
    user_id: str,
    current_user: Dict[str, Any] = Depends(get_current_user_token),
    db: AsyncIOMotorDatabase = Depends(get_database)
):
    """Get user by ID"""
    user_service = UserService(db)
    
    try:
        user = await user_service.get_user_by_id(user_id)
        if not user:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="User not found"
            )
        
        # Check access permissions
        if not check_resource_ownership(current_user, user_id):
            if not check_department_access(current_user, user["department"]):
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail="Access not permitted"
                )
        
        return user
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve user"
        )


@router.put("/{user_id}", response_model=UserResponse)
async def update_user(
    user_id: str,
    update_data: UserUpdate,
    current_user: Dict[str, Any] = Depends(get_current_user_token),
    db: AsyncIOMotorDatabase = Depends(get_database)
):
    """Update user information"""
    user_service = UserService(db)
    
    # Check permissions
    current_user_id = current_user.get("sub")
    current_user_role = current_user.get("role")
    
    # Users can update their own profile, managers can update their team, admins can update anyone
    if user_id != current_user_id and current_user_role not in ["admin", "manager"]:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Permission denied"
        )
    
    try:
        updated_by = current_user_id
        user = await user_service.update_user(user_id, update_data, updated_by)
        return user
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to update user"
        )


@router.delete("/{user_id}")
async def delete_user(
    user_id: str,
    current_user: Dict[str, Any] = Depends(require_admin),
    db: AsyncIOMotorDatabase = Depends(get_database)
):
    """Soft delete user (Admin only)"""
    try:
        # Check if user exists
        user = await db.users.find_one({"_id": ObjectId(user_id)})
        if not user:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="User not found"
            )
        
        # Soft delete by setting is_active to False
        await db.users.update_one(
            {"_id": ObjectId(user_id)},
            {
                "$set": {
                    "is_active": False,
                    "is_online": False,
                    "updated_at": datetime.utcnow()
                }
            }
        )
        
        # Deactivate all user sessions
        await db.user_sessions.update_many(
            {"user_id": ObjectId(user_id)},
            {"$set": {"is_active": False}}
        )
        
        # Create audit log
        from ..core.database import create_audit_log
        await create_audit_log(
            user_id=current_user.get("sub"),
            action="delete_user",
            resource_type="user",
            resource_id=user_id,
            details={"employee_id": user["employee_id"]}
        )
        
        return {"message": "User deleted successfully"}
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to delete user"
        )


@router.put("/{user_id}/preferences")
async def update_user_preferences(
    user_id: str,
    preferences: UserPreferencesUpdate,
    current_user: Dict[str, Any] = Depends(get_current_user_token),
    db: AsyncIOMotorDatabase = Depends(get_database)
):
    """Update user preferences"""
    # Users can only update their own preferences
    if user_id != current_user.get("sub"):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Can only update your own preferences"
        )
    
    try:
        update_doc = {"updated_at": datetime.utcnow()}
        
        if preferences.theme:
            update_doc["preferences.theme"] = preferences.theme
        if preferences.language:
            update_doc["preferences.language"] = preferences.language
        if preferences.timezone:
            update_doc["preferences.timezone"] = preferences.timezone
        if preferences.notifications:
            update_doc["preferences.notifications"] = preferences.notifications
        if preferences.dashboard_layout:
            update_doc["preferences.dashboard_layout"] = preferences.dashboard_layout
        
        await db.users.update_one(
            {"_id": ObjectId(user_id)},
            {"$set": update_doc}
        )
        
        return {"message": "Preferences updated successfully"}
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to update preferences"
        )
