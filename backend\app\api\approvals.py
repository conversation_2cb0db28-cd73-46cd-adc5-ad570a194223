"""
Approval Workflows API routes
"""
from fastapi import APIRouter, Depends, HTTPException, status, Query
from motor.motor_asyncio import AsyncIOMotorDatabase
from typing import Dict, Any, List, Optional
from datetime import datetime, timedelta
from bson import ObjectId
from pydantic import BaseModel

from ..core.database import get_database, create_audit_log
from ..core.security import get_current_user_token, require_manager_or_admin
from ..core.config import Collections
from ..models.approval import Approval, ApprovalType, ApprovalStatus, Priority

router = APIRouter()


class ApprovalCreate(BaseModel):
    title: str
    description: Optional[str] = None
    approval_type: ApprovalType
    priority: Priority = Priority.MEDIUM
    request_data: Dict[str, Any] = {}
    amount: Optional[float] = None
    currency: str = "USD"
    required_by: Optional[datetime] = None
    attachments: List[str] = []


class ApprovalAction(BaseModel):
    action: str  # approve, reject, request_changes
    comments: Optional[str] = None


class LeaveRequestCreate(BaseModel):
    title: str = "Leave Request"
    leave_type: str  # annual, sick, maternity, paternity, emergency, unpaid
    start_date: datetime
    end_date: datetime
    reason: str
    coverage_person_id: Optional[str] = None
    coverage_notes: Optional[str] = None
    emergency_contact: Optional[Dict[str, str]] = None


class ProcurementRequestCreate(BaseModel):
    title: str
    description: str
    items: List[Dict[str, Any]]
    vendor: Optional[str] = None
    total_amount: float
    currency: str = "USD"
    required_by: Optional[datetime] = None
    business_justification: str
    budget_code: Optional[str] = None


@router.post("/")
async def create_approval(
    approval_data: ApprovalCreate,
    current_user: Dict[str, Any] = Depends(get_current_user_token),
    db: AsyncIOMotorDatabase = Depends(get_database)
):
    """Create a new approval request"""
    
    user_id = current_user.get("sub")
    user_department = current_user.get("department")
    
    try:
        # Get user info
        user = await db[Collections.USERS].find_one({"_id": ObjectId(user_id)})
        requester_name = f"{user['profile']['first_name']} {user['profile']['last_name']}"
        
        # Determine workflow based on approval type and amount
        workflow_steps = await _get_approval_workflow(
            approval_data.approval_type,
            user_department,
            approval_data.amount,
            db
        )
        
        # Create approval document
        approval_doc = {
            "title": approval_data.title,
            "description": approval_data.description,
            "approval_type": approval_data.approval_type.value,
            "priority": approval_data.priority.value,
            "requester_id": ObjectId(user_id),
            "requester_name": requester_name,
            "department": user_department,
            "workflow_id": None,
            "workflow_name": "Default Workflow",
            "current_step": 1,
            "total_steps": len(workflow_steps),
            "steps": workflow_steps,
            "status": ApprovalStatus.PENDING.value,
            "final_approved_at": None,
            "final_rejected_at": None,
            "final_approver_id": None,
            "request_data": approval_data.request_data,
            "attachments": approval_data.attachments,
            "amount": approval_data.amount,
            "currency": approval_data.currency,
            "budget_code": None,
            "requested_at": datetime.utcnow(),
            "required_by": approval_data.required_by,
            "expires_at": datetime.utcnow() + timedelta(days=30) if not approval_data.required_by else approval_data.required_by,
            "history": [{
                "action": "created",
                "user_id": ObjectId(user_id),
                "user_name": requester_name,
                "timestamp": datetime.utcnow(),
                "comments": "Approval request created"
            }],
            "comments": [],
            "created_at": datetime.utcnow(),
            "updated_at": datetime.utcnow(),
            "is_urgent": approval_data.priority == Priority.URGENT,
            "requires_justification": approval_data.approval_type in [ApprovalType.PROCUREMENT, ApprovalType.BUDGET]
        }
        
        result = await db[Collections.APPROVALS].insert_one(approval_doc)
        
        # Send notifications to approvers
        await _send_approval_notifications(result.inserted_id, workflow_steps[0], db)
        
        # Create audit log
        await create_audit_log(
            user_id=user_id,
            action="create_approval",
            resource_type="approval",
            resource_id=str(result.inserted_id),
            details={
                "type": approval_data.approval_type.value,
                "amount": approval_data.amount,
                "title": approval_data.title
            }
        )
        
        return {
            "id": str(result.inserted_id),
            "message": "Approval request created successfully",
            "status": "pending",
            "next_approver": workflow_steps[0]["approver_name"] if workflow_steps else None
        }
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to create approval request"
        )


@router.post("/leave")
async def create_leave_request(
    leave_data: LeaveRequestCreate,
    current_user: Dict[str, Any] = Depends(get_current_user_token),
    db: AsyncIOMotorDatabase = Depends(get_database)
):
    """Create a leave request"""
    
    user_id = current_user.get("sub")
    
    try:
        # Calculate total days
        total_days = (leave_data.end_date - leave_data.start_date).days + 1
        
        # Check leave balance (simplified)
        available_balance = 25.0  # This should come from user profile or leave system
        
        if leave_data.leave_type == "annual" and total_days > available_balance:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Insufficient leave balance. Available: {available_balance} days"
            )
        
        # Create approval request
        approval_data = ApprovalCreate(
            title=f"{leave_data.leave_type.title()} Leave Request",
            description=f"Leave request from {leave_data.start_date.date()} to {leave_data.end_date.date()}",
            approval_type=ApprovalType.LEAVE,
            priority=Priority.URGENT if leave_data.leave_type in ["sick", "emergency"] else Priority.MEDIUM,
            request_data={
                "leave_type": leave_data.leave_type,
                "start_date": leave_data.start_date.isoformat(),
                "end_date": leave_data.end_date.isoformat(),
                "total_days": total_days,
                "reason": leave_data.reason,
                "coverage_person_id": leave_data.coverage_person_id,
                "coverage_notes": leave_data.coverage_notes,
                "emergency_contact": leave_data.emergency_contact,
                "available_balance": available_balance,
                "balance_after": available_balance - total_days if leave_data.leave_type == "annual" else available_balance
            },
            required_by=leave_data.start_date - timedelta(days=1)
        )
        
        return await create_approval(approval_data, current_user, db)
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to create leave request"
        )


@router.post("/procurement")
async def create_procurement_request(
    procurement_data: ProcurementRequestCreate,
    current_user: Dict[str, Any] = Depends(get_current_user_token),
    db: AsyncIOMotorDatabase = Depends(get_database)
):
    """Create a procurement request"""
    
    try:
        approval_data = ApprovalCreate(
            title=procurement_data.title,
            description=procurement_data.description,
            approval_type=ApprovalType.PROCUREMENT,
            priority=Priority.HIGH if procurement_data.total_amount > 10000 else Priority.MEDIUM,
            request_data={
                "items": procurement_data.items,
                "vendor": procurement_data.vendor,
                "business_justification": procurement_data.business_justification,
                "budget_code": procurement_data.budget_code,
                "delivery_requirements": "Standard delivery"
            },
            amount=procurement_data.total_amount,
            currency=procurement_data.currency,
            required_by=procurement_data.required_by
        )
        
        return await create_approval(approval_data, current_user, db)
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to create procurement request"
        )


@router.get("/")
async def get_approvals(
    page: int = Query(1, ge=1),
    size: int = Query(20, ge=1, le=100),
    status: Optional[str] = Query(None),
    approval_type: Optional[str] = Query(None),
    my_requests: bool = Query(False),
    pending_my_approval: bool = Query(False),
    current_user: Dict[str, Any] = Depends(get_current_user_token),
    db: AsyncIOMotorDatabase = Depends(get_database)
):
    """Get approvals with filtering"""
    
    user_id = current_user.get("sub")
    user_role = current_user.get("role")
    user_department = current_user.get("department")
    
    try:
        # Build filter query
        filter_query = {}
        
        if my_requests:
            filter_query["requester_id"] = ObjectId(user_id)
        elif pending_my_approval:
            filter_query["steps.approver_id"] = ObjectId(user_id)
            filter_query["steps.status"] = "pending"
        elif user_role not in ["admin", "hr"]:
            # Non-admin users see their department's requests or requests they're involved in
            filter_query["$or"] = [
                {"department": user_department},
                {"requester_id": ObjectId(user_id)},
                {"steps.approver_id": ObjectId(user_id)}
            ]
        
        if status:
            filter_query["status"] = status
        
        if approval_type:
            filter_query["approval_type"] = approval_type
        
        # Get total count
        total = await db[Collections.APPROVALS].count_documents(filter_query)
        
        # Calculate pagination
        skip = (page - 1) * size
        pages = (total + size - 1) // size
        
        # Get approvals
        approvals = await db[Collections.APPROVALS].find(filter_query).sort("created_at", -1).skip(skip).limit(size).to_list(length=None)
        
        # Format response
        formatted_approvals = []
        for approval in approvals:
            # Get current step info
            current_step_info = None
            if approval.get("steps") and approval.get("current_step", 1) <= len(approval["steps"]):
                current_step_info = approval["steps"][approval.get("current_step", 1) - 1]
            
            formatted_approvals.append({
                "id": str(approval["_id"]),
                "title": approval["title"],
                "description": approval.get("description"),
                "approval_type": approval["approval_type"],
                "priority": approval["priority"],
                "status": approval["status"],
                "requester_name": approval["requester_name"],
                "department": approval["department"],
                "amount": approval.get("amount"),
                "currency": approval.get("currency", "USD"),
                "current_step": approval.get("current_step", 1),
                "total_steps": approval.get("total_steps", 1),
                "current_approver": current_step_info.get("approver_name") if current_step_info else None,
                "requested_at": approval["requested_at"],
                "required_by": approval.get("required_by"),
                "is_urgent": approval.get("is_urgent", False),
                "created_at": approval["created_at"]
            })
        
        return {
            "approvals": formatted_approvals,
            "total": total,
            "page": page,
            "size": size,
            "pages": pages
        }
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve approvals"
        )


@router.get("/{approval_id}")
async def get_approval(
    approval_id: str,
    current_user: Dict[str, Any] = Depends(get_current_user_token),
    db: AsyncIOMotorDatabase = Depends(get_database)
):
    """Get approval by ID"""
    
    user_id = current_user.get("sub")
    user_role = current_user.get("role")
    user_department = current_user.get("department")
    
    try:
        approval = await db[Collections.APPROVALS].find_one({"_id": ObjectId(approval_id)})
        
        if not approval:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Approval not found"
            )
        
        # Check access permissions
        user_obj_id = ObjectId(user_id)
        has_access = (
            user_role in ["admin", "hr"] or
            approval["requester_id"] == user_obj_id or
            approval["department"] == user_department or
            any(step.get("approver_id") == user_obj_id for step in approval.get("steps", []))
        )
        
        if not has_access:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Access denied"
            )
        
        # Format response
        formatted_approval = {
            "id": str(approval["_id"]),
            "title": approval["title"],
            "description": approval.get("description"),
            "approval_type": approval["approval_type"],
            "priority": approval["priority"],
            "status": approval["status"],
            "requester_id": str(approval["requester_id"]),
            "requester_name": approval["requester_name"],
            "department": approval["department"],
            "current_step": approval.get("current_step", 1),
            "total_steps": approval.get("total_steps", 1),
            "steps": approval.get("steps", []),
            "request_data": approval.get("request_data", {}),
            "amount": approval.get("amount"),
            "currency": approval.get("currency", "USD"),
            "attachments": approval.get("attachments", []),
            "history": approval.get("history", []),
            "comments": approval.get("comments", []),
            "requested_at": approval["requested_at"],
            "required_by": approval.get("required_by"),
            "expires_at": approval.get("expires_at"),
            "is_urgent": approval.get("is_urgent", False),
            "created_at": approval["created_at"],
            "updated_at": approval["updated_at"]
        }
        
        return formatted_approval
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve approval"
        )


@router.post("/{approval_id}/action")
async def process_approval(
    approval_id: str,
    action_data: ApprovalAction,
    current_user: Dict[str, Any] = Depends(get_current_user_token),
    db: AsyncIOMotorDatabase = Depends(get_database)
):
    """Process approval action (approve/reject)"""
    
    user_id = current_user.get("sub")
    
    try:
        approval = await db[Collections.APPROVALS].find_one({"_id": ObjectId(approval_id)})
        
        if not approval:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Approval not found"
            )
        
        # Check if user can approve this step
        current_step = approval.get("current_step", 1)
        if current_step > len(approval.get("steps", [])):
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="No pending approval step"
            )
        
        current_step_info = approval["steps"][current_step - 1]
        if current_step_info.get("approver_id") != ObjectId(user_id):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="You are not authorized to approve this step"
            )
        
        # Get user info
        user = await db[Collections.USERS].find_one({"_id": ObjectId(user_id)})
        approver_name = f"{user['profile']['first_name']} {user['profile']['last_name']}"
        
        # Process action
        update_doc = {"updated_at": datetime.utcnow()}
        history_entry = {
            "action": action_data.action,
            "user_id": ObjectId(user_id),
            "user_name": approver_name,
            "timestamp": datetime.utcnow(),
            "comments": action_data.comments or ""
        }
        
        if action_data.action == "approve":
            # Update current step
            update_doc[f"steps.{current_step - 1}.status"] = "approved"
            update_doc[f"steps.{current_step - 1}.approved_at"] = datetime.utcnow()
            update_doc[f"steps.{current_step - 1}.comments"] = action_data.comments
            
            # Check if this is the final step
            if current_step >= approval.get("total_steps", 1):
                update_doc["status"] = "approved"
                update_doc["final_approved_at"] = datetime.utcnow()
                update_doc["final_approver_id"] = ObjectId(user_id)
            else:
                update_doc["current_step"] = current_step + 1
                # Send notification to next approver
                next_step = approval["steps"][current_step]
                await _send_approval_notifications(ObjectId(approval_id), next_step, db)
        
        elif action_data.action == "reject":
            update_doc[f"steps.{current_step - 1}.status"] = "rejected"
            update_doc[f"steps.{current_step - 1}.rejected_at"] = datetime.utcnow()
            update_doc[f"steps.{current_step - 1}.comments"] = action_data.comments
            update_doc["status"] = "rejected"
            update_doc["final_rejected_at"] = datetime.utcnow()
            update_doc["final_approver_id"] = ObjectId(user_id)
        
        # Add to history
        update_doc["$push"] = {"history": history_entry}
        
        # Update approval
        await db[Collections.APPROVALS].update_one(
            {"_id": ObjectId(approval_id)},
            {"$set": update_doc, "$push": {"history": history_entry}}
        )
        
        # Create audit log
        await create_audit_log(
            user_id=user_id,
            action=f"approval_{action_data.action}",
            resource_type="approval",
            resource_id=approval_id,
            details={
                "action": action_data.action,
                "step": current_step,
                "comments": action_data.comments
            }
        )
        
        return {
            "message": f"Approval {action_data.action}d successfully",
            "status": update_doc.get("status", approval["status"]),
            "next_step": update_doc.get("current_step")
        }
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to process approval"
        )


async def _get_approval_workflow(
    approval_type: ApprovalType,
    department: str,
    amount: Optional[float],
    db: AsyncIOMotorDatabase
) -> List[Dict[str, Any]]:
    """Get approval workflow steps based on type and amount"""
    
    # Simplified workflow logic - in production, this would be more sophisticated
    steps = []
    
    if approval_type == ApprovalType.LEAVE:
        # Leave requests go to direct manager then HR
        manager = await db[Collections.USERS].find_one({
            "department": department,
            "role": "manager"
        })
        hr_user = await db[Collections.USERS].find_one({"role": "hr"})
        
        if manager:
            steps.append({
                "step_number": 1,
                "approver_id": manager["_id"],
                "approver_name": f"{manager['profile']['first_name']} {manager['profile']['last_name']}",
                "approver_role": "manager",
                "status": "pending",
                "is_required": True,
                "can_delegate": False
            })
        
        if hr_user:
            steps.append({
                "step_number": 2,
                "approver_id": hr_user["_id"],
                "approver_name": f"{hr_user['profile']['first_name']} {hr_user['profile']['last_name']}",
                "approver_role": "hr",
                "status": "pending",
                "is_required": True,
                "can_delegate": False
            })
    
    elif approval_type == ApprovalType.PROCUREMENT:
        # Procurement workflow based on amount
        manager = await db[Collections.USERS].find_one({
            "department": department,
            "role": "manager"
        })
        admin = await db[Collections.USERS].find_one({"role": "admin"})
        
        if amount and amount > 5000:
            # High-value procurement needs manager + admin approval
            if manager:
                steps.append({
                    "step_number": 1,
                    "approver_id": manager["_id"],
                    "approver_name": f"{manager['profile']['first_name']} {manager['profile']['last_name']}",
                    "approver_role": "manager",
                    "status": "pending",
                    "is_required": True,
                    "can_delegate": False
                })
            
            if admin:
                steps.append({
                    "step_number": 2,
                    "approver_id": admin["_id"],
                    "approver_name": f"{admin['profile']['first_name']} {admin['profile']['last_name']}",
                    "approver_role": "admin",
                    "status": "pending",
                    "is_required": True,
                    "can_delegate": False
                })
        else:
            # Low-value procurement needs only manager approval
            if manager:
                steps.append({
                    "step_number": 1,
                    "approver_id": manager["_id"],
                    "approver_name": f"{manager['profile']['first_name']} {manager['profile']['last_name']}",
                    "approver_role": "manager",
                    "status": "pending",
                    "is_required": True,
                    "can_delegate": False
                })
    
    # Default to manager approval if no specific workflow
    if not steps:
        manager = await db[Collections.USERS].find_one({
            "department": department,
            "role": "manager"
        })
        
        if manager:
            steps.append({
                "step_number": 1,
                "approver_id": manager["_id"],
                "approver_name": f"{manager['profile']['first_name']} {manager['profile']['last_name']}",
                "approver_role": "manager",
                "status": "pending",
                "is_required": True,
                "can_delegate": False
            })
    
    return steps


async def _send_approval_notifications(
    approval_id: ObjectId,
    step_info: Dict[str, Any],
    db: AsyncIOMotorDatabase
):
    """Send notification to approver"""
    
    # Create notification document
    notification = {
        "approval_id": approval_id,
        "recipient_id": step_info["approver_id"],
        "notification_type": "approval_request",
        "title": "New Approval Request",
        "message": f"You have a new approval request requiring your attention.",
        "is_sent": False,
        "sent_at": None,
        "is_read": False,
        "read_at": None,
        "delivery_methods": ["in_app"],
        "created_at": datetime.utcnow()
    }
    
    await db["approval_notifications"].insert_one(notification)
    
    # In production, you would also send email/SMS notifications here
