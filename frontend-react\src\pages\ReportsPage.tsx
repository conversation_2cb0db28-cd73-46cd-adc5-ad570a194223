/**
 * Reports & Analytics Page Component
 * Features: Time tracking reports, task analytics, productivity insights
 */
import React, { useState, useEffect, useCallback } from 'react'
import { logger, <PERSON><PERSON>r<PERSON><PERSON><PERSON>, PerformanceMonitor } from '../utils/logger'
import type { User, TimeEntry, Task, Report } from '../types'

interface ReportsPageProps {
  user: User
  onNavigateBack?: () => void
}

interface ReportsState {
  isLoading: boolean
  selectedReport: string
  dateRange: {
    start: string
    end: string
  }
  reportData: {
    timeTracking: {
      totalHours: number
      dailyHours: { date: string; hours: number }[]
      weeklyHours: { week: string; hours: number }[]
      departmentHours: { department: string; hours: number }[]
    }
    tasks: {
      completed: number
      pending: number
      inProgress: number
      byPriority: { priority: string; count: number }[]
      completionRate: number
    }
    productivity: {
      averageTaskTime: number
      mostProductiveDay: string
      mostProductiveHour: number
      efficiency: number
    }
    users: {
      topPerformers: { name: string; score: number }[]
      departmentStats: { department: string; avgHours: number; taskCount: number }[]
    }
  }
  error: string | null
}

const reportTypes = [
  { value: 'time-tracking', label: 'Time Tracking', description: 'Hours worked, attendance, and time analysis' },
  { value: 'task-analytics', label: 'Task Analytics', description: 'Task completion, priorities, and performance' },
  { value: 'productivity', label: 'Productivity Insights', description: 'Efficiency metrics and productivity trends' },
  { value: 'team-performance', label: 'Team Performance', description: 'Team statistics and department comparisons' }
]

export default function ReportsPage({ user, onNavigateBack }: ReportsPageProps) {
  const [state, setState] = useState<ReportsState>({
    isLoading: true,
    selectedReport: 'time-tracking',
    dateRange: {
      start: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0], // 30 days ago
      end: new Date().toISOString().split('T')[0] // today
    },
    reportData: {
      timeTracking: {
        totalHours: 0,
        dailyHours: [],
        weeklyHours: [],
        departmentHours: []
      },
      tasks: {
        completed: 0,
        pending: 0,
        inProgress: 0,
        byPriority: [],
        completionRate: 0
      },
      productivity: {
        averageTaskTime: 0,
        mostProductiveDay: '',
        mostProductiveHour: 0,
        efficiency: 0
      },
      users: {
        topPerformers: [],
        departmentStats: []
      }
    },
    error: null
  })

  useEffect(() => {
    logger.componentMount('ReportsPage')
    loadReportData()
    
    return () => {
      logger.componentUnmount('ReportsPage')
    }
  }, [])

  useEffect(() => {
    // Reload data when report type or date range changes
    loadReportData()
  }, [state.selectedReport, state.dateRange])

  const loadReportData = useCallback(async () => {
    try {
      setState(prev => ({ ...prev, isLoading: true, error: null }))
      logger.info('Loading report data', 'ReportsPage', {
        reportType: state.selectedReport,
        dateRange: state.dateRange
      })
      
      PerformanceMonitor.startTimer('reports_load')

      // Try to load real data from API
      const response = await fetch(`http://localhost:8002/api/reports/${state.selectedReport}?start=${state.dateRange.start}&end=${state.dateRange.end}`, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('authToken')}`,
          'Content-Type': 'application/json'
        }
      })

      const duration = PerformanceMonitor.endTimer('reports_load')

      if (response.ok) {
        const data = await response.json()
        setState(prev => ({
          ...prev,
          isLoading: false,
          reportData: data
        }))

        logger.info(`Report data loaded in ${duration.toFixed(2)}ms`, 'ReportsPage')
      } else {
        // Use mock data if API fails
        const mockData = generateMockReportData()
        setState(prev => ({
          ...prev,
          isLoading: false,
          reportData: mockData
        }))

        logger.info('Using mock report data', 'ReportsPage')
      }
      
    } catch (error) {
      // Use mock data on error
      const mockData = generateMockReportData()
      setState(prev => ({
        ...prev,
        isLoading: false,
        reportData: mockData
      }))
      
      logger.warn('Failed to load report data, using mock data', 'ReportsPage', error)
    }
  }, [state.selectedReport, state.dateRange])

  const generateMockReportData = () => {
    const days = 30
    const dailyHours = []
    const weeklyHours = []
    
    // Generate daily hours for the last 30 days
    for (let i = days - 1; i >= 0; i--) {
      const date = new Date(Date.now() - i * 24 * 60 * 60 * 1000)
      const hours = Math.random() * 8 + 2 // 2-10 hours
      dailyHours.push({
        date: date.toISOString().split('T')[0],
        hours: Math.round(hours * 10) / 10
      })
    }

    // Generate weekly hours
    for (let i = 3; i >= 0; i--) {
      const weekStart = new Date(Date.now() - i * 7 * 24 * 60 * 60 * 1000)
      const hours = Math.random() * 40 + 20 // 20-60 hours per week
      weeklyHours.push({
        week: `Week of ${weekStart.toLocaleDateString()}`,
        hours: Math.round(hours * 10) / 10
      })
    }

    return {
      timeTracking: {
        totalHours: dailyHours.reduce((sum, day) => sum + day.hours, 0),
        dailyHours,
        weeklyHours,
        departmentHours: [
          { department: 'Engineering', hours: 245.5 },
          { department: 'Marketing', hours: 189.2 },
          { department: 'Sales', hours: 167.8 },
          { department: 'HR', hours: 134.6 },
          { department: 'Finance', hours: 156.3 }
        ]
      },
      tasks: {
        completed: 45,
        pending: 12,
        inProgress: 8,
        byPriority: [
          { priority: 'high', count: 15 },
          { priority: 'medium', count: 35 },
          { priority: 'low', count: 15 }
        ],
        completionRate: 69.2
      },
      productivity: {
        averageTaskTime: 4.2,
        mostProductiveDay: 'Tuesday',
        mostProductiveHour: 10,
        efficiency: 87.5
      },
      users: {
        topPerformers: [
          { name: 'Alice Johnson', score: 95.2 },
          { name: 'Bob Smith', score: 92.8 },
          { name: 'Carol Davis', score: 89.6 },
          { name: 'David Wilson', score: 87.3 },
          { name: 'Eva Brown', score: 85.1 }
        ],
        departmentStats: [
          { department: 'Engineering', avgHours: 8.2, taskCount: 156 },
          { department: 'Marketing', avgHours: 7.8, taskCount: 89 },
          { department: 'Sales', avgHours: 7.5, taskCount: 134 },
          { department: 'HR', avgHours: 7.2, taskCount: 67 },
          { department: 'Finance', avgHours: 7.6, taskCount: 78 }
        ]
      }
    }
  }

  const exportReport = async (format: 'pdf' | 'excel' | 'csv') => {
    try {
      logger.info(`Exporting report as ${format}`, 'ReportsPage')
      
      const response = await fetch(`http://localhost:8002/api/reports/export`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('authToken')}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          reportType: state.selectedReport,
          dateRange: state.dateRange,
          format
        })
      })

      if (response.ok) {
        const blob = await response.blob()
        const url = window.URL.createObjectURL(blob)
        const a = document.createElement('a')
        a.href = url
        a.download = `report-${state.selectedReport}-${state.dateRange.start}-to-${state.dateRange.end}.${format}`
        document.body.appendChild(a)
        a.click()
        window.URL.revokeObjectURL(url)
        document.body.removeChild(a)
        
        logger.info(`Report exported successfully as ${format}`, 'ReportsPage')
      } else {
        // Mock export behavior
        alert(`Report would be exported as ${format.toUpperCase()} file`)
        logger.info(`Mock export as ${format}`, 'ReportsPage')
      }
    } catch (error) {
      logger.error(`Failed to export report as ${format}`, 'ReportsPage', error)
      alert(`Failed to export report. Please try again.`)
    }
  }

  const renderTimeTrackingReport = () => (
    <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(300px, 1fr))', gap: '24px' }}>
      {/* Total Hours Card */}
      <div style={{
        backgroundColor: 'white',
        borderRadius: '12px',
        padding: '24px',
        boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)'
      }}>
        <h3 style={{ fontSize: '18px', fontWeight: 'bold', color: '#1f2937', marginBottom: '16px' }}>
          ⏰ Total Hours Worked
        </h3>
        <div style={{ fontSize: '36px', fontWeight: 'bold', color: '#3b82f6', marginBottom: '8px' }}>
          {state.reportData.timeTracking.totalHours.toFixed(1)}h
        </div>
        <p style={{ fontSize: '14px', color: '#6b7280', margin: 0 }}>
          In the last {Math.ceil((new Date(state.dateRange.end).getTime() - new Date(state.dateRange.start).getTime()) / (1000 * 60 * 60 * 24))} days
        </p>
      </div>

      {/* Daily Hours Chart */}
      <div style={{
        backgroundColor: 'white',
        borderRadius: '12px',
        padding: '24px',
        boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)',
        gridColumn: 'span 2'
      }}>
        <h3 style={{ fontSize: '18px', fontWeight: 'bold', color: '#1f2937', marginBottom: '16px' }}>
          📊 Daily Hours Trend
        </h3>
        <div style={{ height: '200px', display: 'flex', alignItems: 'end', gap: '4px', padding: '20px 0' }}>
          {state.reportData.timeTracking.dailyHours.slice(-14).map((day, index) => (
            <div
              key={day.date}
              style={{
                flex: 1,
                backgroundColor: '#3b82f6',
                height: `${(day.hours / 10) * 100}%`,
                minHeight: '4px',
                borderRadius: '2px',
                position: 'relative',
                cursor: 'pointer'
              }}
              title={`${day.date}: ${day.hours}h`}
            >
              <div style={{
                position: 'absolute',
                bottom: '-20px',
                left: '50%',
                transform: 'translateX(-50%)',
                fontSize: '10px',
                color: '#6b7280',
                whiteSpace: 'nowrap'
              }}>
                {new Date(day.date).toLocaleDateString('en-US', { month: 'short', day: 'numeric' })}
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Department Hours */}
      <div style={{
        backgroundColor: 'white',
        borderRadius: '12px',
        padding: '24px',
        boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)'
      }}>
        <h3 style={{ fontSize: '18px', fontWeight: 'bold', color: '#1f2937', marginBottom: '16px' }}>
          🏢 Hours by Department
        </h3>
        <div style={{ display: 'flex', flexDirection: 'column', gap: '12px' }}>
          {state.reportData.timeTracking.departmentHours.map((dept, index) => (
            <div key={dept.department} style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
              <span style={{ fontSize: '14px', color: '#374151' }}>{dept.department}</span>
              <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                <div style={{
                  width: '60px',
                  height: '8px',
                  backgroundColor: '#e5e7eb',
                  borderRadius: '4px',
                  overflow: 'hidden'
                }}>
                  <div style={{
                    width: `${(dept.hours / Math.max(...state.reportData.timeTracking.departmentHours.map(d => d.hours))) * 100}%`,
                    height: '100%',
                    backgroundColor: `hsl(${index * 60}, 70%, 50%)`
                  }} />
                </div>
                <span style={{ fontSize: '14px', fontWeight: '500', color: '#1f2937', minWidth: '40px' }}>
                  {dept.hours}h
                </span>
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  )

  const renderTaskAnalyticsReport = () => (
    <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(300px, 1fr))', gap: '24px' }}>
      {/* Task Status Overview */}
      <div style={{
        backgroundColor: 'white',
        borderRadius: '12px',
        padding: '24px',
        boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)'
      }}>
        <h3 style={{ fontSize: '18px', fontWeight: 'bold', color: '#1f2937', marginBottom: '16px' }}>
          📋 Task Status Overview
        </h3>
        <div style={{ display: 'flex', flexDirection: 'column', gap: '12px' }}>
          <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
            <span style={{ color: '#10b981' }}>✅ Completed</span>
            <span style={{ fontWeight: 'bold', color: '#10b981' }}>{state.reportData.tasks.completed}</span>
          </div>
          <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
            <span style={{ color: '#3b82f6' }}>🔄 In Progress</span>
            <span style={{ fontWeight: 'bold', color: '#3b82f6' }}>{state.reportData.tasks.inProgress}</span>
          </div>
          <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
            <span style={{ color: '#f59e0b' }}>⏳ Pending</span>
            <span style={{ fontWeight: 'bold', color: '#f59e0b' }}>{state.reportData.tasks.pending}</span>
          </div>
        </div>
      </div>

      {/* Completion Rate */}
      <div style={{
        backgroundColor: 'white',
        borderRadius: '12px',
        padding: '24px',
        boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)'
      }}>
        <h3 style={{ fontSize: '18px', fontWeight: 'bold', color: '#1f2937', marginBottom: '16px' }}>
          🎯 Completion Rate
        </h3>
        <div style={{ position: 'relative', width: '120px', height: '120px', margin: '0 auto' }}>
          <svg width="120" height="120" style={{ transform: 'rotate(-90deg)' }}>
            <circle
              cx="60"
              cy="60"
              r="50"
              fill="none"
              stroke="#e5e7eb"
              strokeWidth="10"
            />
            <circle
              cx="60"
              cy="60"
              r="50"
              fill="none"
              stroke="#10b981"
              strokeWidth="10"
              strokeDasharray={`${(state.reportData.tasks.completionRate / 100) * 314} 314`}
              strokeLinecap="round"
            />
          </svg>
          <div style={{
            position: 'absolute',
            top: '50%',
            left: '50%',
            transform: 'translate(-50%, -50%)',
            textAlign: 'center'
          }}>
            <div style={{ fontSize: '24px', fontWeight: 'bold', color: '#1f2937' }}>
              {state.reportData.tasks.completionRate}%
            </div>
          </div>
        </div>
      </div>

      {/* Priority Distribution */}
      <div style={{
        backgroundColor: 'white',
        borderRadius: '12px',
        padding: '24px',
        boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)'
      }}>
        <h3 style={{ fontSize: '18px', fontWeight: 'bold', color: '#1f2937', marginBottom: '16px' }}>
          🔥 Tasks by Priority
        </h3>
        <div style={{ display: 'flex', flexDirection: 'column', gap: '12px' }}>
          {state.reportData.tasks.byPriority.map((priority) => (
            <div key={priority.priority} style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
              <span style={{ 
                color: priority.priority === 'high' ? '#ef4444' : 
                       priority.priority === 'medium' ? '#f59e0b' : '#10b981',
                textTransform: 'capitalize'
              }}>
                {priority.priority === 'high' ? '🔴' : priority.priority === 'medium' ? '🟡' : '🟢'} {priority.priority}
              </span>
              <span style={{ fontWeight: 'bold', color: '#1f2937' }}>{priority.count}</span>
            </div>
          ))}
        </div>
      </div>
    </div>
  )

  const renderProductivityReport = () => (
    <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(300px, 1fr))', gap: '24px' }}>
      {/* Efficiency Score */}
      <div style={{
        backgroundColor: 'white',
        borderRadius: '12px',
        padding: '24px',
        boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)'
      }}>
        <h3 style={{ fontSize: '18px', fontWeight: 'bold', color: '#1f2937', marginBottom: '16px' }}>
          ⚡ Efficiency Score
        </h3>
        <div style={{ fontSize: '36px', fontWeight: 'bold', color: '#10b981', marginBottom: '8px' }}>
          {state.reportData.productivity.efficiency}%
        </div>
        <p style={{ fontSize: '14px', color: '#6b7280', margin: 0 }}>
          Above average performance
        </p>
      </div>

      {/* Average Task Time */}
      <div style={{
        backgroundColor: 'white',
        borderRadius: '12px',
        padding: '24px',
        boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)'
      }}>
        <h3 style={{ fontSize: '18px', fontWeight: 'bold', color: '#1f2937', marginBottom: '16px' }}>
          ⏱️ Avg Task Time
        </h3>
        <div style={{ fontSize: '36px', fontWeight: 'bold', color: '#3b82f6', marginBottom: '8px' }}>
          {state.reportData.productivity.averageTaskTime}h
        </div>
        <p style={{ fontSize: '14px', color: '#6b7280', margin: 0 }}>
          Per task completion
        </p>
      </div>

      {/* Most Productive Day */}
      <div style={{
        backgroundColor: 'white',
        borderRadius: '12px',
        padding: '24px',
        boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)'
      }}>
        <h3 style={{ fontSize: '18px', fontWeight: 'bold', color: '#1f2937', marginBottom: '16px' }}>
          📅 Most Productive Day
        </h3>
        <div style={{ fontSize: '36px', fontWeight: 'bold', color: '#f59e0b', marginBottom: '8px' }}>
          {state.reportData.productivity.mostProductiveDay}
        </div>
        <p style={{ fontSize: '14px', color: '#6b7280', margin: 0 }}>
          Peak performance day
        </p>
      </div>

      {/* Most Productive Hour */}
      <div style={{
        backgroundColor: 'white',
        borderRadius: '12px',
        padding: '24px',
        boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)'
      }}>
        <h3 style={{ fontSize: '18px', fontWeight: 'bold', color: '#1f2937', marginBottom: '16px' }}>
          🕐 Peak Hour
        </h3>
        <div style={{ fontSize: '36px', fontWeight: 'bold', color: '#6366f1', marginBottom: '8px' }}>
          {state.reportData.productivity.mostProductiveHour}:00
        </div>
        <p style={{ fontSize: '14px', color: '#6b7280', margin: 0 }}>
          Most productive hour
        </p>
      </div>
    </div>
  )

  const renderTeamPerformanceReport = () => (
    <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(300px, 1fr))', gap: '24px' }}>
      {/* Top Performers */}
      <div style={{
        backgroundColor: 'white',
        borderRadius: '12px',
        padding: '24px',
        boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)'
      }}>
        <h3 style={{ fontSize: '18px', fontWeight: 'bold', color: '#1f2937', marginBottom: '16px' }}>
          🏆 Top Performers
        </h3>
        <div style={{ display: 'flex', flexDirection: 'column', gap: '12px' }}>
          {state.reportData.users.topPerformers.map((performer, index) => (
            <div key={performer.name} style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
              <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                <span style={{ fontSize: '18px' }}>
                  {index === 0 ? '🥇' : index === 1 ? '🥈' : index === 2 ? '🥉' : '🏅'}
                </span>
                <span style={{ fontSize: '14px', color: '#374151' }}>{performer.name}</span>
              </div>
              <span style={{ fontSize: '14px', fontWeight: 'bold', color: '#1f2937' }}>
                {performer.score}
              </span>
            </div>
          ))}
        </div>
      </div>

      {/* Department Statistics */}
      <div style={{
        backgroundColor: 'white',
        borderRadius: '12px',
        padding: '24px',
        boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)',
        gridColumn: 'span 2'
      }}>
        <h3 style={{ fontSize: '18px', fontWeight: 'bold', color: '#1f2937', marginBottom: '16px' }}>
          🏢 Department Statistics
        </h3>
        <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))', gap: '16px' }}>
          {state.reportData.users.departmentStats.map((dept) => (
            <div
              key={dept.department}
              style={{
                padding: '16px',
                backgroundColor: '#f9fafb',
                borderRadius: '8px',
                border: '1px solid #e5e7eb'
              }}
            >
              <h4 style={{ margin: '0 0 8px 0', fontSize: '16px', fontWeight: '600', color: '#1f2937' }}>
                {dept.department}
              </h4>
              <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: '4px' }}>
                <span style={{ fontSize: '14px', color: '#6b7280' }}>Avg Hours:</span>
                <span style={{ fontSize: '14px', fontWeight: '500', color: '#1f2937' }}>{dept.avgHours}h</span>
              </div>
              <div style={{ display: 'flex', justifyContent: 'space-between' }}>
                <span style={{ fontSize: '14px', color: '#6b7280' }}>Tasks:</span>
                <span style={{ fontSize: '14px', fontWeight: '500', color: '#1f2937' }}>{dept.taskCount}</span>
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  )

  const renderReportContent = () => {
    switch (state.selectedReport) {
      case 'time-tracking':
        return renderTimeTrackingReport()
      case 'task-analytics':
        return renderTaskAnalyticsReport()
      case 'productivity':
        return renderProductivityReport()
      case 'team-performance':
        return renderTeamPerformanceReport()
      default:
        return renderTimeTrackingReport()
    }
  }

  if (state.error) {
    return (
      <div style={{
        minHeight: '100vh',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        backgroundColor: '#f9fafb',
        padding: '20px'
      }}>
        <div style={{
          maxWidth: '500px',
          width: '100%',
          backgroundColor: 'white',
          padding: '40px',
          borderRadius: '16px',
          boxShadow: '0 10px 15px -3px rgba(0, 0, 0, 0.1)',
          textAlign: 'center'
        }}>
          <div style={{ fontSize: '48px', marginBottom: '20px' }}>⚠️</div>
          <h2 style={{ color: '#ef4444', marginBottom: '16px' }}>Reports Error</h2>
          <p style={{ color: '#6b7280', marginBottom: '24px' }}>{state.error}</p>
          <button
            onClick={loadReportData}
            style={{
              padding: '12px 24px',
              backgroundColor: '#667eea',
              color: 'white',
              border: 'none',
              borderRadius: '8px',
              cursor: 'pointer',
              marginRight: '12px'
            }}
          >
            Try Again
          </button>
          <button
            onClick={onNavigateBack}
            style={{
              padding: '12px 24px',
              backgroundColor: '#6b7280',
              color: 'white',
              border: 'none',
              borderRadius: '8px',
              cursor: 'pointer'
            }}
          >
            Go Back
          </button>
        </div>
      </div>
    )
  }

  return (
    <div style={{ minHeight: '100vh', backgroundColor: '#f9fafb' }}>
      {/* Header */}
      <header style={{
        backgroundColor: 'white',
        boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1)',
        padding: '16px 0'
      }}>
        <div style={{
          maxWidth: '1400px',
          margin: '0 auto',
          padding: '0 24px',
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center'
        }}>
          <div style={{ display: 'flex', alignItems: 'center', gap: '16px' }}>
            <button
              onClick={onNavigateBack}
              style={{
                padding: '8px 12px',
                backgroundColor: '#f3f4f6',
                border: 'none',
                borderRadius: '6px',
                cursor: 'pointer',
                fontSize: '14px'
              }}
            >
              ← Back
            </button>
            <div>
              <h1 style={{ 
                margin: 0, 
                color: '#1f2937', 
                fontSize: '24px',
                fontWeight: 'bold'
              }}>
                📈 Reports & Analytics
              </h1>
              <p style={{ 
                margin: '4px 0 0 0', 
                color: '#6b7280', 
                fontSize: '14px' 
              }}>
                Insights and analytics for better decision making
              </p>
            </div>
          </div>
          
          <div style={{ display: 'flex', gap: '12px' }}>
            <button
              onClick={() => exportReport('pdf')}
              style={{
                padding: '8px 16px',
                backgroundColor: '#ef4444',
                color: 'white',
                border: 'none',
                borderRadius: '6px',
                cursor: 'pointer',
                fontSize: '14px'
              }}
            >
              📄 PDF
            </button>
            <button
              onClick={() => exportReport('excel')}
              style={{
                padding: '8px 16px',
                backgroundColor: '#10b981',
                color: 'white',
                border: 'none',
                borderRadius: '6px',
                cursor: 'pointer',
                fontSize: '14px'
              }}
            >
              📊 Excel
            </button>
            <button
              onClick={() => exportReport('csv')}
              style={{
                padding: '8px 16px',
                backgroundColor: '#3b82f6',
                color: 'white',
                border: 'none',
                borderRadius: '6px',
                cursor: 'pointer',
                fontSize: '14px'
              }}
            >
              📋 CSV
            </button>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main style={{ maxWidth: '1400px', margin: '0 auto', padding: '24px' }}>
        {/* Report Controls */}
        <div style={{
          backgroundColor: 'white',
          borderRadius: '12px',
          padding: '24px',
          marginBottom: '24px',
          boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)'
        }}>
          <h2 style={{ 
            fontSize: '18px', 
            fontWeight: 'bold', 
            color: '#1f2937', 
            marginBottom: '20px' 
          }}>
            🎛️ Report Configuration
          </h2>
          
          <div style={{
            display: 'grid',
            gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))',
            gap: '16px',
            alignItems: 'end'
          }}>
            <div>
              <label style={{ 
                display: 'block', 
                fontSize: '14px', 
                fontWeight: '500', 
                color: '#374151', 
                marginBottom: '6px' 
              }}>
                Report Type
              </label>
              <select
                value={state.selectedReport}
                onChange={(e) => setState(prev => ({ ...prev, selectedReport: e.target.value }))}
                style={{
                  width: '100%',
                  padding: '12px',
                  border: '1px solid #d1d5db',
                  borderRadius: '6px',
                  fontSize: '14px',
                  backgroundColor: 'white'
                }}
              >
                {reportTypes.map(type => (
                  <option key={type.value} value={type.value}>
                    {type.label}
                  </option>
                ))}
              </select>
              <p style={{
                margin: '4px 0 0 0',
                fontSize: '12px',
                color: '#6b7280'
              }}>
                {reportTypes.find(t => t.value === state.selectedReport)?.description}
              </p>
            </div>

            <div>
              <label style={{ 
                display: 'block', 
                fontSize: '14px', 
                fontWeight: '500', 
                color: '#374151', 
                marginBottom: '6px' 
              }}>
                Start Date
              </label>
              <input
                type="date"
                value={state.dateRange.start}
                onChange={(e) => setState(prev => ({
                  ...prev,
                  dateRange: { ...prev.dateRange, start: e.target.value }
                }))}
                style={{
                  width: '100%',
                  padding: '12px',
                  border: '1px solid #d1d5db',
                  borderRadius: '6px',
                  fontSize: '14px'
                }}
              />
            </div>

            <div>
              <label style={{ 
                display: 'block', 
                fontSize: '14px', 
                fontWeight: '500', 
                color: '#374151', 
                marginBottom: '6px' 
              }}>
                End Date
              </label>
              <input
                type="date"
                value={state.dateRange.end}
                onChange={(e) => setState(prev => ({
                  ...prev,
                  dateRange: { ...prev.dateRange, end: e.target.value }
                }))}
                style={{
                  width: '100%',
                  padding: '12px',
                  border: '1px solid #d1d5db',
                  borderRadius: '6px',
                  fontSize: '14px'
                }}
              />
            </div>

            <button
              onClick={loadReportData}
              disabled={state.isLoading}
              style={{
                padding: '12px 20px',
                backgroundColor: state.isLoading ? '#9ca3af' : '#667eea',
                color: 'white',
                border: 'none',
                borderRadius: '6px',
                cursor: state.isLoading ? 'not-allowed' : 'pointer',
                fontSize: '14px',
                fontWeight: '500',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                gap: '8px'
              }}
            >
              {state.isLoading ? (
                <div style={{
                  width: '16px',
                  height: '16px',
                  border: '2px solid transparent',
                  borderTop: '2px solid white',
                  borderRadius: '50%',
                  animation: 'spin 1s linear infinite'
                }} />
              ) : (
                '🔄'
              )}
              {state.isLoading ? 'Loading...' : 'Generate Report'}
            </button>
          </div>
        </div>

        {/* Report Content */}
        {state.isLoading ? (
          <div style={{
            display: 'flex',
            justifyContent: 'center',
            alignItems: 'center',
            height: '400px'
          }}>
            <div style={{
              width: '40px',
              height: '40px',
              border: '4px solid #e5e7eb',
              borderTop: '4px solid #667eea',
              borderRadius: '50%',
              animation: 'spin 1s linear infinite'
            }} />
          </div>
        ) : (
          renderReportContent()
        )}
      </main>

      {/* CSS Animation */}
      <style>{`
        @keyframes spin {
          0% { transform: rotate(0deg); }
          100% { transform: rotate(360deg); }
        }
      `}</style>
    </div>
  )
}
