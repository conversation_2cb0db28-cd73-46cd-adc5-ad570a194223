"""
Core configuration settings for CTNL AI Work Management System
"""
from typing import Optional, List
from pydantic_settings import BaseSettings
from pydantic import validator
import os


class Settings(BaseSettings):
    # Application
    APP_NAME: str = "CTNL AI Work Management System"
    APP_VERSION: str = "1.0.0"
    DEBUG: bool = False
    
    # Server
    HOST: str = "0.0.0.0"
    PORT: int = 8000
    
    # Database
    MONGODB_URL: str
    DATABASE_NAME: str = "ctnl_db"
    
    # Redis
    REDIS_URL: str = "redis://localhost:6379"
    
    # JWT Authentication
    JWT_SECRET: str
    JWT_ALGORITHM: str = "HS256"
    JWT_EXPIRE_MINUTES: int = 30
    REFRESH_TOKEN_EXPIRE_DAYS: int = 7
    
    # OpenAI
    OPENAI_API_KEY: str
    OPENAI_MODEL: str = "gpt-4"
    
    # AWS S3
    AWS_ACCESS_KEY_ID: str
    AWS_SECRET_ACCESS_KEY: str
    AWS_S3_BUCKET: str
    AWS_REGION: str = "us-east-1"
    
    # CORS
    CORS_ORIGINS: List[str] = [
        "http://localhost:3000",
        "http://localhost:3001",
        "https://ctnl-ai.vercel.app"
    ]
    
    # Email (Optional)
    SMTP_HOST: Optional[str] = None
    SMTP_PORT: Optional[int] = 587
    SMTP_USER: Optional[str] = None
    SMTP_PASSWORD: Optional[str] = None
    
    # File Upload
    MAX_FILE_SIZE: int = 10 * 1024 * 1024  # 10MB
    ALLOWED_FILE_TYPES: List[str] = [
        "image/jpeg", "image/png", "image/gif",
        "application/pdf", "text/plain",
        "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
        "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
    ]
    
    # Pagination
    DEFAULT_PAGE_SIZE: int = 20
    MAX_PAGE_SIZE: int = 100
    
    # Rate Limiting
    RATE_LIMIT_PER_MINUTE: int = 60
    
    # Logging
    LOG_LEVEL: str = "INFO"
    
    # Sentry (Optional)
    SENTRY_DSN: Optional[str] = None
    
    @validator("CORS_ORIGINS", pre=True)
    def assemble_cors_origins(cls, v):
        if isinstance(v, str) and not v.startswith("["):
            return [i.strip() for i in v.split(",")]
        elif isinstance(v, (list, str)):
            return v
        raise ValueError(v)
    
    class Config:
        env_file = ".env"
        case_sensitive = True


# Global settings instance
settings = Settings()


# Database Collections
class Collections:
    USERS = "users"
    DEPARTMENTS = "departments"
    PROJECTS = "projects"
    TASKS = "tasks"
    TIME_LOGS = "time_logs"
    LEAVE_REQUESTS = "leave_requests"
    PROCUREMENT_REQUESTS = "procurement_requests"
    INVOICES = "invoices"
    DOCUMENTS = "documents"
    NOTIFICATIONS = "notifications"
    AUDIT_LOGS = "audit_logs"
    AI_CONVERSATIONS = "ai_conversations"
    APPROVALS = "approvals"
    FILES = "files"
    REPORTS = "reports"
    SESSIONS = "sessions"
    ASSETS = "assets"
    FLEET = "fleet"
    CONSTRUCTION_PROJECTS = "construction_projects"
    BATTERY_INVENTORY = "battery_inventory"
    TELECOM_SITES = "telecom_sites"


# User Roles
class UserRoles:
    ADMIN = "admin"
    MANAGER = "manager"
    STAFF = "staff"
    ACCOUNTANT = "accountant"
    HR = "hr"
    STAFF_ADMIN = "staff_admin"
    
    @classmethod
    def all_roles(cls):
        return [cls.ADMIN, cls.MANAGER, cls.STAFF, cls.ACCOUNTANT, cls.HR, cls.STAFF_ADMIN]


# Approval Status
class ApprovalStatus:
    PENDING = "pending"
    APPROVED = "approved"
    REJECTED = "rejected"
    IN_REVIEW = "in_review"
    CANCELLED = "cancelled"


# Task Status
class TaskStatus:
    TODO = "todo"
    IN_PROGRESS = "in_progress"
    REVIEW = "review"
    COMPLETED = "completed"
    CANCELLED = "cancelled"


# Project Status
class ProjectStatus:
    PLANNING = "planning"
    ACTIVE = "active"
    ON_HOLD = "on_hold"
    COMPLETED = "completed"
    CANCELLED = "cancelled"


# Leave Types
class LeaveTypes:
    ANNUAL = "annual"
    SICK = "sick"
    MATERNITY = "maternity"
    PATERNITY = "paternity"
    EMERGENCY = "emergency"
    UNPAID = "unpaid"


# Priority Levels
class Priority:
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    URGENT = "urgent"


# Notification Types
class NotificationTypes:
    TASK_ASSIGNED = "task_assigned"
    APPROVAL_REQUEST = "approval_request"
    APPROVAL_APPROVED = "approval_approved"
    APPROVAL_REJECTED = "approval_rejected"
    PROJECT_UPDATE = "project_update"
    LEAVE_REQUEST = "leave_request"
    SYSTEM_ALERT = "system_alert"
    AI_INSIGHT = "ai_insight"
