"""
User schemas for API requests and responses
"""
from typing import Optional, List, Dict, Any
from datetime import datetime
from pydantic import BaseModel, EmailStr, Field, validator
from ..core.config import UserRoles


class UserProfileCreate(BaseModel):
    """Schema for creating user profile"""
    first_name: str = Field(..., min_length=1, max_length=50)
    last_name: str = Field(..., min_length=1, max_length=50)
    phone: Optional[str] = Field(None, pattern=r'^\+?1?\d{9,15}$')
    address: Optional[str] = Field(None, max_length=200)
    emergency_contact: Optional[Dict[str, str]] = None
    bio: Optional[str] = Field(None, max_length=500)
    skills: List[str] = []
    certifications: List[str] = []
    languages: List[str] = []


class UserCreate(BaseModel):
    """Schema for creating a new user"""
    employee_id: str = Field(..., min_length=3, max_length=20)
    email: EmailStr
    username: str = Field(..., min_length=3, max_length=30, pattern=r'^[a-zA-Z0-9_.-]+$')
    password: str = Field(..., min_length=8, max_length=100)
    
    # Profile information
    profile: UserProfileCreate
    
    # Role and Department
    role: str
    department: str
    position: Optional[str] = Field(None, max_length=100)
    manager_id: Optional[str] = None
    
    # Additional fields
    salary: Optional[float] = Field(None, ge=0)
    hire_date: Optional[datetime] = None
    contract_type: str = Field("full_time", pattern=r'^(full_time|part_time|contract|intern)$')
    work_location: str = Field("office", pattern=r'^(office|remote|hybrid)$')
    
    @validator('role')
    def validate_role(cls, v):
        if v not in UserRoles.all_roles():
            raise ValueError(f'Role must be one of: {", ".join(UserRoles.all_roles())}')
        return v
    
    @validator('password')
    def validate_password(cls, v):
        if len(v) < 8:
            raise ValueError('Password must be at least 8 characters long')
        if not any(c.isupper() for c in v):
            raise ValueError('Password must contain at least one uppercase letter')
        if not any(c.islower() for c in v):
            raise ValueError('Password must contain at least one lowercase letter')
        if not any(c.isdigit() for c in v):
            raise ValueError('Password must contain at least one digit')
        return v


class UserUpdate(BaseModel):
    """Schema for updating user information"""
    profile: Optional[UserProfileCreate] = None
    department: Optional[str] = None
    position: Optional[str] = Field(None, max_length=100)
    manager_id: Optional[str] = None
    salary: Optional[float] = Field(None, ge=0)
    contract_type: Optional[str] = Field(None, pattern=r'^(full_time|part_time|contract|intern)$')
    work_location: Optional[str] = Field(None, pattern=r'^(office|remote|hybrid)$')
    is_active: Optional[bool] = None


class UserResponse(BaseModel):
    """Schema for user response"""
    id: str
    employee_id: str
    email: EmailStr
    username: str
    profile: Dict[str, Any]
    role: str
    department: str
    position: Optional[str] = None
    manager_id: Optional[str] = None
    is_active: bool
    is_verified: bool
    is_online: bool
    avatar_url: Optional[str] = None
    contract_type: str
    work_location: str
    created_at: datetime
    updated_at: datetime
    last_login: Optional[datetime] = None


class UserListResponse(BaseModel):
    """Schema for user list response"""
    id: str
    employee_id: str
    email: EmailStr
    username: str
    first_name: str
    last_name: str
    role: str
    department: str
    position: Optional[str] = None
    is_active: bool
    is_online: bool
    avatar_url: Optional[str] = None
    last_login: Optional[datetime] = None


class UserLogin(BaseModel):
    """Schema for user login"""
    username: str = Field(..., min_length=3)
    password: str = Field(..., min_length=1)
    remember_me: bool = False


class UserLoginResponse(BaseModel):
    """Schema for login response"""
    access_token: str
    refresh_token: str
    token_type: str = "bearer"
    expires_in: int
    user: UserResponse


class TokenRefresh(BaseModel):
    """Schema for token refresh"""
    refresh_token: str


class PasswordChange(BaseModel):
    """Schema for password change"""
    current_password: str
    new_password: str = Field(..., min_length=8, max_length=100)
    
    @validator('new_password')
    def validate_new_password(cls, v):
        if len(v) < 8:
            raise ValueError('Password must be at least 8 characters long')
        if not any(c.isupper() for c in v):
            raise ValueError('Password must contain at least one uppercase letter')
        if not any(c.islower() for c in v):
            raise ValueError('Password must contain at least one lowercase letter')
        if not any(c.isdigit() for c in v):
            raise ValueError('Password must contain at least one digit')
        return v


class PasswordReset(BaseModel):
    """Schema for password reset request"""
    email: EmailStr


class PasswordResetConfirm(BaseModel):
    """Schema for password reset confirmation"""
    token: str
    new_password: str = Field(..., min_length=8, max_length=100)
    
    @validator('new_password')
    def validate_new_password(cls, v):
        if len(v) < 8:
            raise ValueError('Password must be at least 8 characters long')
        if not any(c.isupper() for c in v):
            raise ValueError('Password must contain at least one uppercase letter')
        if not any(c.islower() for c in v):
            raise ValueError('Password must contain at least one lowercase letter')
        if not any(c.isdigit() for c in v):
            raise ValueError('Password must contain at least one digit')
        return v


class UserPreferencesUpdate(BaseModel):
    """Schema for updating user preferences"""
    theme: Optional[str] = Field(None, pattern=r'^(light|dark|auto)$')
    language: Optional[str] = Field(None, min_length=2, max_length=5)
    timezone: Optional[str] = None
    notifications: Optional[Dict[str, bool]] = None
    dashboard_layout: Optional[Dict[str, Any]] = None


class DepartmentCreate(BaseModel):
    """Schema for creating a department"""
    name: str = Field(..., min_length=1, max_length=100)
    description: Optional[str] = Field(None, max_length=500)
    manager_id: Optional[str] = None
    parent_department_id: Optional[str] = None
    budget: Optional[float] = Field(None, ge=0)
    cost_center: Optional[str] = Field(None, max_length=20)
    location: Optional[str] = Field(None, max_length=100)


class DepartmentUpdate(BaseModel):
    """Schema for updating a department"""
    name: Optional[str] = Field(None, min_length=1, max_length=100)
    description: Optional[str] = Field(None, max_length=500)
    manager_id: Optional[str] = None
    parent_department_id: Optional[str] = None
    budget: Optional[float] = Field(None, ge=0)
    cost_center: Optional[str] = Field(None, max_length=20)
    location: Optional[str] = Field(None, max_length=100)
    is_active: Optional[bool] = None


class DepartmentResponse(BaseModel):
    """Schema for department response"""
    id: str
    name: str
    description: Optional[str] = None
    manager_id: Optional[str] = None
    manager_name: Optional[str] = None
    parent_department_id: Optional[str] = None
    parent_department_name: Optional[str] = None
    budget: Optional[float] = None
    cost_center: Optional[str] = None
    location: Optional[str] = None
    is_active: bool
    employee_count: int = 0
    created_at: datetime
    updated_at: datetime


class APIKeyCreate(BaseModel):
    """Schema for creating API key"""
    name: str = Field(..., min_length=1, max_length=100)
    scopes: List[str] = []
    rate_limit: int = Field(1000, ge=1, le=10000)
    expires_at: Optional[datetime] = None


class APIKeyResponse(BaseModel):
    """Schema for API key response"""
    id: str
    name: str
    key: Optional[str] = None  # Only returned on creation
    scopes: List[str]
    rate_limit: int
    is_active: bool
    last_used: Optional[datetime] = None
    usage_count: int
    expires_at: Optional[datetime] = None
    created_at: datetime


class UserStats(BaseModel):
    """Schema for user statistics"""
    total_users: int
    active_users: int
    online_users: int
    users_by_role: Dict[str, int]
    users_by_department: Dict[str, int]
    recent_logins: int
    new_users_this_month: int


class PaginatedUserResponse(BaseModel):
    """Schema for paginated user response"""
    users: List[UserListResponse]
    total: int
    page: int
    size: int
    pages: int
