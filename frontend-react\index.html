<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />

    <!-- PWA Meta Tags -->
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no" />
    <meta name="theme-color" content="#ff0000" />
    <meta name="background-color" content="#1a1a1a" />
    <meta name="display" content="standalone" />
    <meta name="orientation" content="portrait" />

    <!-- App Info -->
    <title>CTNL WORK-BOARD - AI Work Management System</title>
    <meta name="description" content="AI-powered work management and productivity system with advanced features" />
    <meta name="author" content="CTNL Systems" />
    <meta name="keywords" content="CTNL, work management, AI, productivity, project management, PWA, desktop app" />

    <!-- P<PERSON> Manifest -->
    <link rel="manifest" href="/manifest.json" />

    <!-- Icons -->
    <link rel="icon" type="image/png" sizes="32x32" href="/icons/icon-32x32.png" />
    <link rel="icon" type="image/png" sizes="16x16" href="/icons/icon-16x16.png" />
    <link rel="apple-touch-icon" sizes="180x180" href="/icons/icon-192x192.png" />

    <!-- iOS PWA Support -->
    <meta name="apple-mobile-web-app-capable" content="yes" />
    <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent" />
    <meta name="apple-mobile-web-app-title" content="CT Tracker" />

    <!-- Windows PWA Support -->
    <meta name="msapplication-TileColor" content="#ff0000" />
    <meta name="msapplication-TileImage" content="/icons/icon-144x144.png" />
    <meta name="msapplication-config" content="/browserconfig.xml" />

    <!-- Preload critical resources -->
    <link rel="preload" href="/src/main.tsx" as="script" />
    <link rel="preload" href="/src/index.css" as="style" />
  </head>
  <body>
    <div id="root"></div>

    <!-- PWA Installation Banner -->
    <div id="pwa-install-banner" style="display: none; position: fixed; bottom: 20px; left: 20px; right: 20px; background: #ff0000; color: white; padding: 15px; border-radius: 8px; z-index: 9999; text-align: center;">
      <p style="margin: 0 0 10px 0; font-weight: bold;">📱 Install CTNL WORK-BOARD as a Desktop App!</p>
      <button id="pwa-install-btn" style="background: white; color: #ff0000; border: none; padding: 8px 16px; border-radius: 4px; font-weight: bold; cursor: pointer; margin-right: 10px;">Install Now</button>
      <button id="pwa-dismiss-btn" style="background: transparent; color: white; border: 1px solid white; padding: 8px 16px; border-radius: 4px; cursor: pointer;">Maybe Later</button>
    </div>

    <script type="module" src="/src/main.tsx"></script>

    <!-- PWA Service Worker Registration -->
    <script>
      // Register service worker
      if ('serviceWorker' in navigator) {
        window.addEventListener('load', () => {
          navigator.serviceWorker.register('/sw.js')
            .then((registration) => {
              console.log('✅ SW registered: ', registration);
            })
            .catch((registrationError) => {
              console.log('❌ SW registration failed: ', registrationError);
            });
        });
      }

      // PWA Install Prompt
      let deferredPrompt;
      const installBanner = document.getElementById('pwa-install-banner');
      const installBtn = document.getElementById('pwa-install-btn');
      const dismissBtn = document.getElementById('pwa-dismiss-btn');

      window.addEventListener('beforeinstallprompt', (e) => {
        e.preventDefault();
        deferredPrompt = e;
        installBanner.style.display = 'block';
      });

      installBtn.addEventListener('click', async () => {
        if (deferredPrompt) {
          deferredPrompt.prompt();
          const { outcome } = await deferredPrompt.userChoice;
          console.log(`PWA install outcome: ${outcome}`);
          deferredPrompt = null;
          installBanner.style.display = 'none';
        }
      });

      dismissBtn.addEventListener('click', () => {
        installBanner.style.display = 'none';
      });

      // Hide banner when app is installed
      window.addEventListener('appinstalled', () => {
        installBanner.style.display = 'none';
        console.log('✅ PWA was installed');
      });
    </script>
  </body>
</html>
