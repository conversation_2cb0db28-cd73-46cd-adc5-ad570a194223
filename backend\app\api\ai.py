"""
AI Assistant API routes
"""
from fastapi import APIRouter, Depends, HTTPException, status, UploadFile, File
from motor.motor_asyncio import AsyncIOMotorDatabase
from typing import Dict, Any, List, Optional
from pydantic import BaseModel
from bson import ObjectId

from ..core.database import get_database
from ..core.security import get_current_user_token
from ..services.ai_service import AIService

router = APIRouter()


class ChatMessage(BaseModel):
    message: str
    context: Optional[Dict[str, Any]] = None
    conversation_id: Optional[str] = None


class DocumentAnalysisRequest(BaseModel):
    content: str
    document_type: str = "general"
    analysis_type: str = "summary"  # summary, extract_tasks, sentiment, compliance, financial


class TaskSuggestionRequest(BaseModel):
    project_context: Optional[Dict[str, Any]] = None


class SmartSchedulingRequest(BaseModel):
    tasks: List[Dict[str, Any]]
    constraints: Optional[Dict[str, Any]] = None


@router.post("/chat")
async def ai_chat(
    chat_data: ChatMessage,
    current_user: Dict[str, Any] = Depends(get_current_user_token),
    db: AsyncIOMotorDatabase = Depends(get_database)
):
    """AI chat completion with context awareness"""
    
    user_id = current_user.get("sub")
    ai_service = AIService(db)
    
    try:
        result = await ai_service.chat_completion(
            user_id=user_id,
            message=chat_data.message,
            context=chat_data.context,
            conversation_id=chat_data.conversation_id
        )
        
        return {
            "success": True,
            "data": result
        }
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"AI chat failed: {str(e)}"
        )


@router.post("/analyze-document")
async def analyze_document(
    analysis_request: DocumentAnalysisRequest,
    current_user: Dict[str, Any] = Depends(get_current_user_token),
    db: AsyncIOMotorDatabase = Depends(get_database)
):
    """Analyze document content using AI"""
    
    user_id = current_user.get("sub")
    ai_service = AIService(db)
    
    try:
        result = await ai_service.analyze_document(
            user_id=user_id,
            document_content=analysis_request.content,
            document_type=analysis_request.document_type,
            analysis_type=analysis_request.analysis_type
        )
        
        return {
            "success": True,
            "data": result
        }
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Document analysis failed: {str(e)}"
        )


@router.post("/analyze-file")
async def analyze_file(
    file: UploadFile = File(...),
    document_type: str = "general",
    analysis_type: str = "summary",
    current_user: Dict[str, Any] = Depends(get_current_user_token),
    db: AsyncIOMotorDatabase = Depends(get_database)
):
    """Analyze uploaded file using AI"""
    
    user_id = current_user.get("sub")
    ai_service = AIService(db)
    
    try:
        # Read file content
        content = await file.read()
        
        # Handle different file types
        if file.content_type == "text/plain":
            document_content = content.decode('utf-8')
        elif file.content_type == "application/pdf":
            # TODO: Implement PDF text extraction
            document_content = "PDF content extraction not implemented yet"
        elif file.content_type in ["application/vnd.openxmlformats-officedocument.wordprocessingml.document"]:
            # TODO: Implement DOCX text extraction
            document_content = "DOCX content extraction not implemented yet"
        else:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Unsupported file type"
            )
        
        result = await ai_service.analyze_document(
            user_id=user_id,
            document_content=document_content,
            document_type=document_type,
            analysis_type=analysis_type
        )
        
        return {
            "success": True,
            "data": {
                **result,
                "filename": file.filename,
                "file_type": file.content_type
            }
        }
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"File analysis failed: {str(e)}"
        )


@router.post("/suggest-tasks")
async def suggest_tasks(
    suggestion_request: TaskSuggestionRequest,
    current_user: Dict[str, Any] = Depends(get_current_user_token),
    db: AsyncIOMotorDatabase = Depends(get_database)
):
    """Generate intelligent task suggestions"""
    
    user_id = current_user.get("sub")
    ai_service = AIService(db)
    
    try:
        suggestions = await ai_service.generate_task_suggestions(
            user_id=user_id,
            project_context=suggestion_request.project_context
        )
        
        return {
            "success": True,
            "data": {
                "suggestions": suggestions,
                "generated_at": datetime.utcnow().isoformat()
            }
        }
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Task suggestion failed: {str(e)}"
        )


@router.post("/smart-schedule")
async def smart_schedule(
    scheduling_request: SmartSchedulingRequest,
    current_user: Dict[str, Any] = Depends(get_current_user_token),
    db: AsyncIOMotorDatabase = Depends(get_database)
):
    """AI-powered smart scheduling"""
    
    user_id = current_user.get("sub")
    ai_service = AIService(db)
    
    try:
        result = await ai_service.smart_scheduling(
            user_id=user_id,
            tasks=scheduling_request.tasks,
            constraints=scheduling_request.constraints
        )
        
        return {
            "success": True,
            "data": result
        }
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Smart scheduling failed: {str(e)}"
        )


@router.get("/conversations")
async def get_conversations(
    page: int = 1,
    size: int = 20,
    current_user: Dict[str, Any] = Depends(get_current_user_token),
    db: AsyncIOMotorDatabase = Depends(get_database)
):
    """Get user's AI conversations"""
    
    user_id = current_user.get("sub")
    
    try:
        # Get total count
        total = await db.ai_conversations.count_documents({"user_id": ObjectId(user_id)})
        
        # Calculate pagination
        skip = (page - 1) * size
        pages = (total + size - 1) // size
        
        # Get conversations
        conversations = await db.ai_conversations.find({
            "user_id": ObjectId(user_id)
        }).sort("updated_at", -1).skip(skip).limit(size).to_list(length=None)
        
        # Format response
        formatted_conversations = []
        for conv in conversations:
            # Get last message for preview
            last_message = conv.get("messages", [])[-1] if conv.get("messages") else None
            
            formatted_conversations.append({
                "id": str(conv["_id"]),
                "created_at": conv["created_at"].isoformat(),
                "updated_at": conv["updated_at"].isoformat(),
                "message_count": len(conv.get("messages", [])),
                "last_message": last_message.get("content", "")[:100] + "..." if last_message else "",
                "context": conv.get("context", {})
            })
        
        return {
            "conversations": formatted_conversations,
            "total": total,
            "page": page,
            "size": size,
            "pages": pages
        }
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve conversations"
        )


@router.get("/conversations/{conversation_id}")
async def get_conversation(
    conversation_id: str,
    current_user: Dict[str, Any] = Depends(get_current_user_token),
    db: AsyncIOMotorDatabase = Depends(get_database)
):
    """Get specific conversation details"""
    
    user_id = current_user.get("sub")
    
    try:
        conversation = await db.ai_conversations.find_one({
            "_id": ObjectId(conversation_id),
            "user_id": ObjectId(user_id)
        })
        
        if not conversation:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Conversation not found"
            )
        
        return {
            "id": str(conversation["_id"]),
            "messages": conversation.get("messages", []),
            "context": conversation.get("context", {}),
            "created_at": conversation["created_at"].isoformat(),
            "updated_at": conversation["updated_at"].isoformat()
        }
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve conversation"
        )


@router.delete("/conversations/{conversation_id}")
async def delete_conversation(
    conversation_id: str,
    current_user: Dict[str, Any] = Depends(get_current_user_token),
    db: AsyncIOMotorDatabase = Depends(get_database)
):
    """Delete a conversation"""
    
    user_id = current_user.get("sub")
    
    try:
        result = await db.ai_conversations.delete_one({
            "_id": ObjectId(conversation_id),
            "user_id": ObjectId(user_id)
        })
        
        if result.deleted_count == 0:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Conversation not found"
            )
        
        return {"message": "Conversation deleted successfully"}
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to delete conversation"
        )


@router.get("/insights")
async def get_ai_insights(
    current_user: Dict[str, Any] = Depends(get_current_user_token),
    db: AsyncIOMotorDatabase = Depends(get_database)
):
    """Get AI-generated insights for the user"""
    
    user_id = current_user.get("sub")
    ai_service = AIService(db)
    
    try:
        # Generate insights based on user's recent activity
        user_obj_id = ObjectId(user_id)
        
        # Get recent tasks
        recent_tasks = await db.tasks.find({
            "assignee_id": user_obj_id
        }).sort("updated_at", -1).limit(10).to_list(length=None)
        
        # Get time tracking data
        from datetime import datetime, timedelta
        week_ago = datetime.utcnow() - timedelta(days=7)
        time_logs = await db.time_logs.find({
            "user_id": user_obj_id,
            "clock_in": {"$gte": week_ago}
        }).to_list(length=None)
        
        # Generate insights using AI
        context = {
            "recent_tasks": [
                {
                    "title": task.get("title"),
                    "status": task.get("status"),
                    "priority": task.get("priority"),
                    "due_date": task.get("due_date").isoformat() if task.get("due_date") else None
                } for task in recent_tasks
            ],
            "time_tracking": {
                "total_sessions": len(time_logs),
                "total_hours": sum([log.get("duration", 0) / 3600 for log in time_logs if log.get("duration")])
            }
        }
        
        insights_response = await ai_service.chat_completion(
            user_id=user_id,
            message="Based on my recent work activity, provide 3-5 actionable insights to improve my productivity and work quality.",
            context=context
        )
        
        return {
            "insights": insights_response["response"],
            "generated_at": datetime.utcnow().isoformat(),
            "context_summary": {
                "tasks_analyzed": len(recent_tasks),
                "time_period": "Last 7 days",
                "total_work_hours": round(context["time_tracking"]["total_hours"], 1)
            }
        }
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to generate insights: {str(e)}"
        )
