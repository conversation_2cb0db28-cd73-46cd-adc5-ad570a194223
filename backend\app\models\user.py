"""
User model for MongoDB
"""
from typing import Optional, List, Dict, Any
from datetime import datetime
from pydantic import BaseModel, EmailStr, Field
from bson import ObjectId
from ..core.config import UserRoles


class PyObjectId(ObjectId):
    @classmethod
    def __get_validators__(cls):
        yield cls.validate

    @classmethod
    def validate(cls, v):
        if not ObjectId.is_valid(v):
            raise ValueError("Invalid objectid")
        return ObjectId(v)

    @classmethod
    def __get_pydantic_json_schema__(cls, field_schema, handler):
        json_schema = handler(field_schema)
        json_schema.update(type="string")
        return json_schema


class UserProfile(BaseModel):
    """User profile information"""
    first_name: str
    last_name: str
    phone: Optional[str] = None
    address: Optional[str] = None
    emergency_contact: Optional[Dict[str, str]] = None
    bio: Optional[str] = None
    skills: List[str] = []
    certifications: List[str] = []
    languages: List[str] = []


class UserPreferences(BaseModel):
    """User preferences and settings"""
    theme: str = "light"  # light, dark, auto
    language: str = "en"
    timezone: str = "UTC"
    notifications: Dict[str, bool] = {
        "email": True,
        "push": True,
        "sms": False,
        "task_assignments": True,
        "project_updates": True,
        "approval_requests": True,
        "system_alerts": True
    }
    dashboard_layout: Dict[str, Any] = {}


class UserSecurity(BaseModel):
    """User security settings"""
    two_factor_enabled: bool = False
    two_factor_secret: Optional[str] = None
    backup_codes: List[str] = []
    last_password_change: Optional[datetime] = None
    failed_login_attempts: int = 0
    account_locked_until: Optional[datetime] = None
    password_reset_token: Optional[str] = None
    password_reset_expires: Optional[datetime] = None


class UserActivity(BaseModel):
    """User activity tracking"""
    last_login: Optional[datetime] = None
    last_active: Optional[datetime] = None
    login_count: int = 0
    current_session_id: Optional[str] = None
    ip_addresses: List[str] = []
    devices: List[Dict[str, str]] = []


class User(BaseModel):
    """Main User model"""
    id: PyObjectId = Field(default_factory=PyObjectId, alias="_id")
    employee_id: str = Field(..., unique=True)
    email: EmailStr = Field(..., unique=True)
    username: str = Field(..., unique=True)
    hashed_password: str
    
    # Basic Information
    profile: UserProfile
    
    # Role and Department
    role: str = Field(..., pattern=f"^({'|'.join(UserRoles.all_roles())})$")
    department: str
    position: Optional[str] = None
    manager_id: Optional[PyObjectId] = None
    direct_reports: List[PyObjectId] = []
    
    # Status
    is_active: bool = True
    is_verified: bool = False
    is_online: bool = False
    
    # Settings
    preferences: UserPreferences = UserPreferences()
    security: UserSecurity = UserSecurity()
    activity: UserActivity = UserActivity()
    
    # Metadata
    created_at: datetime = Field(default_factory=datetime.utcnow)
    updated_at: datetime = Field(default_factory=datetime.utcnow)
    created_by: Optional[PyObjectId] = None
    
    # Additional fields
    avatar_url: Optional[str] = None
    salary: Optional[float] = None
    hire_date: Optional[datetime] = None
    contract_type: str = "full_time"  # full_time, part_time, contract, intern
    work_location: str = "office"  # office, remote, hybrid
    
    model_config = {
        "populate_by_name": True,
        "arbitrary_types_allowed": True,
        "json_encoders": {ObjectId: str}
    }



class Department(BaseModel):
    """Department model"""
    id: PyObjectId = Field(default_factory=PyObjectId, alias="_id")
    name: str = Field(..., unique=True)
    description: Optional[str] = None
    manager_id: Optional[PyObjectId] = None
    parent_department_id: Optional[PyObjectId] = None
    budget: Optional[float] = None
    cost_center: Optional[str] = None
    location: Optional[str] = None
    
    # Status
    is_active: bool = True
    
    # Metadata
    created_at: datetime = Field(default_factory=datetime.utcnow)
    updated_at: datetime = Field(default_factory=datetime.utcnow)
    created_by: Optional[PyObjectId] = None
    
    model_config = {
        "populate_by_name": True,
        "arbitrary_types_allowed": True,
        "json_encoders": {ObjectId: str}
    }


class UserSession(BaseModel):
    """User session model for tracking active sessions"""
    id: PyObjectId = Field(default_factory=PyObjectId, alias="_id")
    user_id: PyObjectId
    session_id: str = Field(..., unique=True)
    refresh_token: str
    
    # Session details
    ip_address: Optional[str] = None
    user_agent: Optional[str] = None
    device_info: Optional[Dict[str, str]] = None
    location: Optional[Dict[str, str]] = None
    
    # Timestamps
    created_at: datetime = Field(default_factory=datetime.utcnow)
    last_accessed: datetime = Field(default_factory=datetime.utcnow)
    expires_at: datetime
    
    # Status
    is_active: bool = True
    
    model_config = {
        "populate_by_name": True,
        "arbitrary_types_allowed": True,
        "json_encoders": {ObjectId: str}
    }


class APIKey(BaseModel):
    """API Key model for external integrations"""
    id: PyObjectId = Field(default_factory=PyObjectId, alias="_id")
    name: str
    hashed_key: str
    user_id: PyObjectId
    
    # Permissions
    scopes: List[str] = []  # List of allowed API scopes
    rate_limit: int = 1000  # Requests per hour
    
    # Status
    is_active: bool = True
    last_used: Optional[datetime] = None
    usage_count: int = 0
    
    # Expiration
    expires_at: Optional[datetime] = None
    
    # Metadata
    created_at: datetime = Field(default_factory=datetime.utcnow)
    updated_at: datetime = Field(default_factory=datetime.utcnow)
    
    model_config = {
        "populate_by_name": True,
        "arbitrary_types_allowed": True,
        "json_encoders": {ObjectId: str}
    }
