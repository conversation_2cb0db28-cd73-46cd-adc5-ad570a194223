"""
Simplified Database Connection for MongoDB
"""

import logging
from motor.motor_asyncio import AsyncIOMotorClient, AsyncIOMotorDatabase
from .config import settings

logger = logging.getLogger(__name__)

class MongoDB:
    client: AsyncIOMotorClient = None
    database: AsyncIOMotorDatabase = None

mongodb = MongoDB()

async def connect_to_mongo():
    """Connect to MongoDB"""
    try:
        logger.info("Connecting to MongoDB...")
        
        # Create client
        mongodb.client = AsyncIOMotorClient(settings.MONGODB_URL)
        
        # Test connection
        await mongodb.client.admin.command('ping')
        
        # Get database
        mongodb.database = mongodb.client[settings.DATABASE_NAME]
        
        logger.info(f"✅ Connected to MongoDB successfully!")
        logger.info(f"📊 Database: {settings.DATABASE_NAME}")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ MongoDB connection failed: {e}")
        mongodb.client = None
        mongodb.database = None
        return False

async def close_mongo_connection():
    """Close MongoDB connection"""
    if mongodb.client:
        mongodb.client.close()
        logger.info("Disconnected from MongoDB")

async def get_database() -> AsyncIOMotorDatabase:
    """Get database instance"""
    return mongodb.database

# Test the connection
async def test_connection():
    """Test MongoDB connection"""
    try:
        if mongodb.database is None:
            logger.warning("No database connection available")
            return False
            
        # Test with a simple operation
        result = await mongodb.database.command('ping')
        logger.info(f"Database ping successful: {result}")
        return True
        
    except Exception as e:
        logger.error(f"Database test failed: {e}")
        return False
