/**
 * AI Assistant Page Component
 * Features: Chat interface, task automation, intelligent suggestions
 */
import React, { useState, useEffect, useCallback, useRef } from 'react'
import { logger, <PERSON>rror<PERSON><PERSON>ler, PerformanceMonitor } from '../utils/logger'
import type { User } from '../types'

interface Message {
  id: string
  content: string
  role: 'user' | 'assistant'
  timestamp: string
  type?: 'text' | 'task' | 'suggestion' | 'error'
}

interface AIAssistantPageProps {
  user: User
  onNavigateBack?: () => void
}

interface AIState {
  isLoading: boolean
  messages: Message[]
  inputMessage: string
  isTyping: boolean
  conversations: { id: string; title: string; updated_at: string }[]
  currentConversationId: string | null
  suggestions: string[]
  error: string | null
}

const quickActions = [
  { label: 'Create a task', prompt: 'Help me create a new task for my project' },
  { label: 'Schedule meeting', prompt: 'Help me schedule a team meeting' },
  { label: 'Generate report', prompt: 'Generate a weekly progress report' },
  { label: 'Analyze productivity', prompt: 'Analyze my productivity this week' },
  { label: 'Project summary', prompt: 'Give me a summary of my current projects' },
  { label: 'Time management tips', prompt: 'Give me tips to improve my time management' }
]

export default function AIAssistantPage({ user, onNavigateBack }: AIAssistantPageProps) {
  const messagesEndRef = useRef<HTMLDivElement>(null)
  const inputRef = useRef<HTMLTextAreaElement>(null)
  
  const [state, setState] = useState<AIState>({
    isLoading: false,
    messages: [],
    inputMessage: '',
    isTyping: false,
    conversations: [],
    currentConversationId: null,
    suggestions: [
      'How can I improve my productivity?',
      'What tasks are due this week?',
      'Create a project timeline',
      'Analyze team performance'
    ],
    error: null
  })

  useEffect(() => {
    logger.componentMount('AIAssistantPage')
    loadConversations()
    startNewConversation()
    
    return () => {
      logger.componentUnmount('AIAssistantPage')
    }
  }, [])

  useEffect(() => {
    scrollToBottom()
  }, [state.messages])

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' })
  }

  const loadConversations = useCallback(async () => {
    try {
      const response = await fetch('http://localhost:8002/api/ai/conversations', {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('authToken')}`,
          'Content-Type': 'application/json'
        }
      })

      if (response.ok) {
        const data = await response.json()
        setState(prev => ({
          ...prev,
          conversations: data.conversations || []
        }))
      } else {
        // Mock conversations
        setState(prev => ({
          ...prev,
          conversations: [
            {
              id: '1',
              title: 'Project Planning Discussion',
              updated_at: new Date().toISOString()
            },
            {
              id: '2',
              title: 'Task Management Help',
              updated_at: new Date(Date.now() - 86400000).toISOString()
            }
          ]
        }))
      }
    } catch (error) {
      logger.error('Failed to load conversations', 'AIAssistantPage', error)
    }
  }, [])

  const startNewConversation = () => {
    const newConversationId = Date.now().toString()
    setState(prev => ({
      ...prev,
      currentConversationId: newConversationId,
      messages: [
        {
          id: '1',
          content: `Hello ${user.username}! I'm your AI assistant. I can help you with tasks, projects, scheduling, and productivity insights. How can I assist you today?`,
          role: 'assistant',
          timestamp: new Date().toISOString(),
          type: 'text'
        }
      ]
    }))
  }

  const sendMessage = async () => {
    if (!state.inputMessage.trim() || state.isTyping) return

    const userMessage: Message = {
      id: Date.now().toString(),
      content: state.inputMessage.trim(),
      role: 'user',
      timestamp: new Date().toISOString(),
      type: 'text'
    }

    setState(prev => ({
      ...prev,
      messages: [...prev.messages, userMessage],
      inputMessage: '',
      isTyping: true
    }))

    try {
      PerformanceMonitor.startTimer('ai_response')

      const response = await fetch('http://localhost:8002/api/ai/chat', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('authToken')}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          message: userMessage.content,
          conversation_id: state.currentConversationId,
          context: {
            user_role: user.role,
            department: user.department
          }
        })
      })

      const duration = PerformanceMonitor.endTimer('ai_response')

      if (response.ok) {
        const data = await response.json()
        
        const assistantMessage: Message = {
          id: (Date.now() + 1).toString(),
          content: data.response,
          role: 'assistant',
          timestamp: new Date().toISOString(),
          type: data.type || 'text'
        }

        setState(prev => ({
          ...prev,
          messages: [...prev.messages, assistantMessage],
          isTyping: false,
          suggestions: data.suggestions || prev.suggestions
        }))

        logger.info(`AI response received in ${duration.toFixed(2)}ms`, 'AIAssistantPage')
      } else {
        // Mock AI response
        const mockResponses = [
          "I understand you'd like help with that. Based on your role as a " + user.role + ", I can suggest creating a structured approach to tackle this task.",
          "That's a great question! Let me help you break this down into manageable steps.",
          "I can definitely assist with that. Here are some recommendations based on best practices in your department.",
          "Excellent! I'll help you organize this efficiently. Let's start by prioritizing the key components."
        ]

        const mockResponse = mockResponses[Math.floor(Math.random() * mockResponses.length)]
        
        const assistantMessage: Message = {
          id: (Date.now() + 1).toString(),
          content: mockResponse,
          role: 'assistant',
          timestamp: new Date().toISOString(),
          type: 'text'
        }

        setState(prev => ({
          ...prev,
          messages: [...prev.messages, assistantMessage],
          isTyping: false
        }))

        logger.info('Mock AI response generated', 'AIAssistantPage')
      }
    } catch (error) {
      const errorMessage: Message = {
        id: (Date.now() + 1).toString(),
        content: 'I apologize, but I encountered an error processing your request. Please try again.',
        role: 'assistant',
        timestamp: new Date().toISOString(),
        type: 'error'
      }

      setState(prev => ({
        ...prev,
        messages: [...prev.messages, errorMessage],
        isTyping: false
      }))

      logger.error('Failed to get AI response', 'AIAssistantPage', error)
    }
  }

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault()
      sendMessage()
    }
  }

  const handleQuickAction = (prompt: string) => {
    setState(prev => ({ ...prev, inputMessage: prompt }))
    inputRef.current?.focus()
  }

  const handleSuggestion = (suggestion: string) => {
    setState(prev => ({ ...prev, inputMessage: suggestion }))
    inputRef.current?.focus()
  }

  const getMessageTypeColor = (type?: string): string => {
    switch (type) {
      case 'task': return '#10b981'
      case 'suggestion': return '#ff0000'
      case 'error': return '#ef4444'
      default: return '#6b7280'
    }
  }

  const formatTime = (timestamp: string): string => {
    return new Date(timestamp).toLocaleTimeString('en-US', {
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  return (
    <div style={{ minHeight: '100vh', backgroundColor: '#f9fafb', display: 'flex', flexDirection: 'column' }}>
      {/* Header */}
      <header style={{
        backgroundColor: 'white',
        boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1)',
        padding: '16px 0',
        flexShrink: 0
      }}>
        <div style={{
          maxWidth: '1400px',
          margin: '0 auto',
          padding: '0 24px',
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center'
        }}>
          <div style={{ display: 'flex', alignItems: 'center', gap: '16px' }}>
            <button
              onClick={onNavigateBack}
              style={{
                padding: '8px 12px',
                backgroundColor: '#f3f4f6',
                border: 'none',
                borderRadius: '6px',
                cursor: 'pointer',
                fontSize: '14px'
              }}
            >
              ← Back
            </button>
            <div>
              <h1 style={{ 
                margin: 0, 
                color: '#1f2937', 
                fontSize: '24px',
                fontWeight: 'bold'
              }}>
                🤖 AI Assistant
              </h1>
              <p style={{ 
                margin: '4px 0 0 0', 
                color: '#6b7280', 
                fontSize: '14px' 
              }}>
                Your intelligent work companion
              </p>
            </div>
          </div>
          
          <button
            onClick={startNewConversation}
            style={{
              padding: '12px 20px',
              backgroundColor: '#667eea',
              color: 'white',
              border: 'none',
              borderRadius: '8px',
              cursor: 'pointer',
              fontSize: '14px',
              fontWeight: '500',
              display: 'flex',
              alignItems: 'center',
              gap: '8px'
            }}
          >
            ➕ New Chat
          </button>
        </div>
      </header>

      {/* Main Content */}
      <div style={{ 
        flex: 1, 
        display: 'flex', 
        maxWidth: '1400px', 
        margin: '0 auto', 
        width: '100%',
        padding: '24px',
        gap: '24px'
      }}>
        {/* Sidebar */}
        <div style={{
          width: '300px',
          flexShrink: 0,
          display: 'flex',
          flexDirection: 'column',
          gap: '16px'
        }}>
          {/* Quick Actions */}
          <div style={{
            backgroundColor: 'white',
            borderRadius: '12px',
            padding: '20px',
            boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)'
          }}>
            <h3 style={{
              fontSize: '16px',
              fontWeight: '600',
              color: '#1f2937',
              marginBottom: '16px'
            }}>
              🚀 Quick Actions
            </h3>
            <div style={{ display: 'flex', flexDirection: 'column', gap: '8px' }}>
              {quickActions.map((action, index) => (
                <button
                  key={index}
                  onClick={() => handleQuickAction(action.prompt)}
                  style={{
                    padding: '12px',
                    backgroundColor: '#f9fafb',
                    border: '1px solid #e5e7eb',
                    borderRadius: '8px',
                    cursor: 'pointer',
                    fontSize: '14px',
                    textAlign: 'left',
                    transition: 'all 0.2s'
                  }}
                  onMouseEnter={(e) => {
                    e.currentTarget.style.backgroundColor = '#f3f4f6'
                    e.currentTarget.style.borderColor = '#d1d5db'
                  }}
                  onMouseLeave={(e) => {
                    e.currentTarget.style.backgroundColor = '#f9fafb'
                    e.currentTarget.style.borderColor = '#e5e7eb'
                  }}
                >
                  {action.label}
                </button>
              ))}
            </div>
          </div>

          {/* Suggestions */}
          <div style={{
            backgroundColor: 'white',
            borderRadius: '12px',
            padding: '20px',
            boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)'
          }}>
            <h3 style={{
              fontSize: '16px',
              fontWeight: '600',
              color: '#1f2937',
              marginBottom: '16px'
            }}>
              💡 Suggestions
            </h3>
            <div style={{ display: 'flex', flexDirection: 'column', gap: '8px' }}>
              {state.suggestions.map((suggestion, index) => (
                <button
                  key={index}
                  onClick={() => handleSuggestion(suggestion)}
                  style={{
                    padding: '10px',
                    backgroundColor: '#f0f9ff',
                    border: '1px solid #bae6fd',
                    borderRadius: '6px',
                    cursor: 'pointer',
                    fontSize: '13px',
                    textAlign: 'left',
                    color: '#0369a1',
                    transition: 'all 0.2s'
                  }}
                  onMouseEnter={(e) => {
                    e.currentTarget.style.backgroundColor = '#e0f2fe'
                  }}
                  onMouseLeave={(e) => {
                    e.currentTarget.style.backgroundColor = '#f0f9ff'
                  }}
                >
                  {suggestion}
                </button>
              ))}
            </div>
          </div>
        </div>

        {/* Chat Area */}
        <div style={{
          flex: 1,
          backgroundColor: 'white',
          borderRadius: '12px',
          boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)',
          display: 'flex',
          flexDirection: 'column',
          height: 'calc(100vh - 140px)'
        }}>
          {/* Messages */}
          <div style={{
            flex: 1,
            padding: '24px',
            overflowY: 'auto',
            display: 'flex',
            flexDirection: 'column',
            gap: '16px'
          }}>
            {state.messages.map((message) => (
              <div
                key={message.id}
                style={{
                  display: 'flex',
                  justifyContent: message.role === 'user' ? 'flex-end' : 'flex-start'
                }}
              >
                <div style={{
                  maxWidth: '70%',
                  padding: '16px',
                  borderRadius: '16px',
                  backgroundColor: message.role === 'user' ? '#667eea' : '#f9fafb',
                  color: message.role === 'user' ? 'white' : '#1f2937',
                  border: message.role === 'assistant' ? '1px solid #e5e7eb' : 'none',
                  borderLeft: message.type && message.role === 'assistant' 
                    ? `4px solid ${getMessageTypeColor(message.type)}` 
                    : undefined
                }}>
                  <div style={{
                    fontSize: '14px',
                    lineHeight: '1.5',
                    whiteSpace: 'pre-wrap'
                  }}>
                    {message.content}
                  </div>
                  <div style={{
                    fontSize: '12px',
                    opacity: 0.7,
                    marginTop: '8px',
                    textAlign: 'right'
                  }}>
                    {formatTime(message.timestamp)}
                  </div>
                </div>
              </div>
            ))}

            {state.isTyping && (
              <div style={{ display: 'flex', justifyContent: 'flex-start' }}>
                <div style={{
                  padding: '16px',
                  borderRadius: '16px',
                  backgroundColor: '#f9fafb',
                  border: '1px solid #e5e7eb',
                  display: 'flex',
                  alignItems: 'center',
                  gap: '8px'
                }}>
                  <div style={{
                    display: 'flex',
                    gap: '4px'
                  }}>
                    {[0, 1, 2].map((i) => (
                      <div
                        key={i}
                        style={{
                          width: '8px',
                          height: '8px',
                          backgroundColor: '#6b7280',
                          borderRadius: '50%',
                          animation: `pulse 1.4s ease-in-out ${i * 0.2}s infinite`
                        }}
                      />
                    ))}
                  </div>
                  <span style={{ fontSize: '14px', color: '#6b7280' }}>AI is thinking...</span>
                </div>
              </div>
            )}

            <div ref={messagesEndRef} />
          </div>

          {/* Input Area */}
          <div style={{
            padding: '24px',
            borderTop: '1px solid #e5e7eb',
            backgroundColor: '#fafafa'
          }}>
            <div style={{
              display: 'flex',
              gap: '12px',
              alignItems: 'flex-end'
            }}>
              <textarea
                ref={inputRef}
                value={state.inputMessage}
                onChange={(e) => setState(prev => ({ ...prev, inputMessage: e.target.value }))}
                onKeyPress={handleKeyPress}
                placeholder="Type your message here... (Press Enter to send, Shift+Enter for new line)"
                disabled={state.isTyping}
                style={{
                  flex: 1,
                  minHeight: '44px',
                  maxHeight: '120px',
                  padding: '12px 16px',
                  border: '1px solid #d1d5db',
                  borderRadius: '12px',
                  fontSize: '14px',
                  resize: 'none',
                  outline: 'none',
                  backgroundColor: 'white'
                }}
              />
              <button
                onClick={sendMessage}
                disabled={!state.inputMessage.trim() || state.isTyping}
                style={{
                  padding: '12px 20px',
                  backgroundColor: (!state.inputMessage.trim() || state.isTyping) ? '#9ca3af' : '#667eea',
                  color: 'white',
                  border: 'none',
                  borderRadius: '12px',
                  cursor: (!state.inputMessage.trim() || state.isTyping) ? 'not-allowed' : 'pointer',
                  fontSize: '14px',
                  fontWeight: '500',
                  display: 'flex',
                  alignItems: 'center',
                  gap: '8px'
                }}
              >
                {state.isTyping ? (
                  <div style={{
                    width: '16px',
                    height: '16px',
                    border: '2px solid transparent',
                    borderTop: '2px solid white',
                    borderRadius: '50%',
                    animation: 'spin 1s linear infinite'
                  }} />
                ) : (
                  '📤'
                )}
                Send
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* CSS Animations */}
      <style>{`
        @keyframes spin {
          0% { transform: rotate(0deg); }
          100% { transform: rotate(360deg); }
        }
        
        @keyframes pulse {
          0%, 80%, 100% {
            transform: scale(0);
            opacity: 0.5;
          }
          40% {
            transform: scale(1);
            opacity: 1;
          }
        }
      `}</style>
    </div>
  )
}
