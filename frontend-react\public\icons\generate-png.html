<!DOCTYPE html>
<html>
<head>
    <title>PNG Icon Generator</title>
    <style>
        body { 
            font-family: Arial, sans-serif; 
            padding: 20px; 
            background: #1a1a1a; 
            color: white; 
            text-align: center;
        }
        canvas { 
            border: 2px solid #ff0000; 
            margin: 10px; 
            display: none;
        }
        .icon-grid { 
            display: grid; 
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr)); 
            gap: 20px; 
            margin-top: 20px;
        }
        .icon-item { 
            text-align: center; 
            background: #2a2a2a;
            padding: 15px;
            border-radius: 8px;
        }
        .download-btn {
            background: #ff0000;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            margin-top: 10px;
        }
        .download-btn:hover {
            background: #cc0000;
        }
    </style>
</head>
<body>
    <h1>🚀 CT Communication Towers PWA Icon Generator</h1>
    <p>Click "Generate Icons" to create all PWA icon sizes</p>
    <button onclick="generateAllIcons()" style="background: #ff0000; color: white; border: none; padding: 12px 24px; border-radius: 8px; font-size: 16px; cursor: pointer;">Generate All Icons</button>
    
    <div id="iconGrid" class="icon-grid"></div>

    <script>
        function createIcon(size) {
            const canvas = document.createElement('canvas');
            canvas.width = size;
            canvas.height = size;
            const ctx = canvas.getContext('2d');

            // Create gradient background
            const gradient = ctx.createRadialGradient(size/2, size/2, 0, size/2, size/2, size/2);
            gradient.addColorStop(0, '#ff0000');
            gradient.addColorStop(0.7, '#cc0000');
            gradient.addColorStop(1, '#990000');
            
            ctx.fillStyle = gradient;
            ctx.fillRect(0, 0, size, size);

            // Add border
            ctx.strokeStyle = '#ffffff';
            ctx.lineWidth = Math.max(2, size * 0.02);
            ctx.strokeRect(ctx.lineWidth/2, ctx.lineWidth/2, size - ctx.lineWidth, size - ctx.lineWidth);

            // Add clock icon
            const centerX = size / 2;
            const centerY = size / 2;
            const radius = size * 0.3;

            // Clock circle
            ctx.beginPath();
            ctx.arc(centerX, centerY, radius, 0, 2 * Math.PI);
            ctx.fillStyle = '#ffffff';
            ctx.fill();
            ctx.strokeStyle = '#000000';
            ctx.lineWidth = Math.max(1, size * 0.01);
            ctx.stroke();

            // Clock hands
            ctx.strokeStyle = '#000000';
            ctx.lineWidth = Math.max(2, size * 0.015);
            ctx.lineCap = 'round';

            // Hour hand (pointing to 2)
            ctx.beginPath();
            ctx.moveTo(centerX, centerY);
            ctx.lineTo(centerX + radius * 0.5 * Math.cos(-Math.PI/2 + Math.PI/3), 
                      centerY + radius * 0.5 * Math.sin(-Math.PI/2 + Math.PI/3));
            ctx.stroke();

            // Minute hand (pointing to 12)
            ctx.beginPath();
            ctx.moveTo(centerX, centerY);
            ctx.lineTo(centerX + radius * 0.7 * Math.cos(-Math.PI/2), 
                      centerY + radius * 0.7 * Math.sin(-Math.PI/2));
            ctx.stroke();

            // Center dot
            ctx.beginPath();
            ctx.arc(centerX, centerY, Math.max(2, size * 0.02), 0, 2 * Math.PI);
            ctx.fillStyle = '#ff0000';
            ctx.fill();

            // Add CT text for larger icons
            if (size >= 128) {
                ctx.fillStyle = '#ffffff';
                ctx.font = `bold ${size * 0.08}px Arial`;
                ctx.textAlign = 'center';
                ctx.fillText('CT', centerX, size * 0.9);
            }

            // Add communication tower symbol for larger icons
            if (size >= 192) {
                // Draw simple tower
                ctx.strokeStyle = '#ffffff';
                ctx.lineWidth = Math.max(1, size * 0.01);

                // Tower mast
                ctx.beginPath();
                ctx.moveTo(centerX, centerY - radius * 0.3);
                ctx.lineTo(centerX, centerY + radius * 0.8);
                ctx.stroke();

                // Signal waves
                ctx.strokeStyle = '#ffffff';
                ctx.lineWidth = Math.max(1, size * 0.008);
                for (let i = 0; i < 3; i++) {
                    ctx.beginPath();
                    const waveRadius = radius * 0.4 + (i * size * 0.02);
                    ctx.arc(centerX, centerY - radius * 0.3, waveRadius, Math.PI * 0.7, Math.PI * 0.3, true);
                    ctx.stroke();
                }
            }

            return canvas;
        }

        function downloadCanvas(canvas, filename) {
            const link = document.createElement('a');
            link.download = filename;
            link.href = canvas.toDataURL('image/png');
            link.click();
        }

        function generateAllIcons() {
            const sizes = [16, 32, 72, 96, 128, 144, 152, 192, 384, 512];
            const iconGrid = document.getElementById('iconGrid');
            iconGrid.innerHTML = '';

            sizes.forEach(size => {
                const canvas = createIcon(size);
                
                const iconItem = document.createElement('div');
                iconItem.className = 'icon-item';
                
                const label = document.createElement('h3');
                label.textContent = `${size}x${size}`;
                label.style.margin = '0 0 10px 0';
                
                const preview = document.createElement('canvas');
                preview.width = 64;
                preview.height = 64;
                const previewCtx = preview.getContext('2d');
                previewCtx.drawImage(canvas, 0, 0, 64, 64);
                
                const downloadBtn = document.createElement('button');
                downloadBtn.className = 'download-btn';
                downloadBtn.textContent = 'Download';
                downloadBtn.onclick = () => downloadCanvas(canvas, `icon-${size}x${size}.png`);
                
                iconItem.appendChild(label);
                iconItem.appendChild(preview);
                iconItem.appendChild(downloadBtn);
                iconGrid.appendChild(iconItem);
            });

            // Auto-download all icons
            setTimeout(() => {
                sizes.forEach(size => {
                    const canvas = createIcon(size);
                    downloadCanvas(canvas, `icon-${size}x${size}.png`);
                });
            }, 1000);
        }

        // Auto-generate on load
        window.onload = () => {
            console.log('🎨 PWA Icon Generator Ready!');
        };
    </script>
</body>
</html>
