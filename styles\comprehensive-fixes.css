/* Comprehensive CSS Fixes for CTNL AI Work-Board */
/* Addresses common Tailwind and styling issues */

/* ===== RESET AND BASE FIXES ===== */

/* Fix for box-sizing inheritance */
*,
*::before,
*::after {
  box-sizing: border-box;
}

/* Fix for default margins and padding */
* {
  margin: 0;
  padding: 0;
}

/* Fix for line-height inheritance */
html {
  line-height: 1.15;
  -webkit-text-size-adjust: 100%;
}

/* Fix for body defaults */
body {
  margin: 0;
  font-family: system-ui, -apple-system, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif;
  line-height: 1.6;
  color: hsl(var(--foreground));
  background-color: hsl(var(--background));
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* ===== TAILWIND UTILITY FIXES ===== */

/* Fix for missing utility classes */
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

/* Fix for focus-visible */
.focus-visible:focus-visible {
  outline: 2px solid hsl(var(--ring));
  outline-offset: 2px;
}

/* Fix for backdrop-blur support */
@supports (backdrop-filter: blur(0)) {
  .backdrop-blur-sm {
    backdrop-filter: blur(4px);
  }
  .backdrop-blur {
    backdrop-filter: blur(8px);
  }
  .backdrop-blur-md {
    backdrop-filter: blur(12px);
  }
  .backdrop-blur-lg {
    backdrop-filter: blur(16px);
  }
}

/* Fallback for browsers without backdrop-filter */
@supports not (backdrop-filter: blur(0)) {
  .backdrop-blur-sm,
  .backdrop-blur,
  .backdrop-blur-md,
  .backdrop-blur-lg {
    background-color: rgba(255, 255, 255, 0.8);
  }
  
  .dark .backdrop-blur-sm,
  .dark .backdrop-blur,
  .dark .backdrop-blur-md,
  .dark .backdrop-blur-lg {
    background-color: rgba(0, 0, 0, 0.8);
  }
}

/* ===== COMPONENT FIXES ===== */

/* Fix for button focus states */
.btn:focus,
button:focus {
  outline: 2px solid hsl(var(--ring));
  outline-offset: 2px;
}

/* Fix for input focus states */
input:focus,
textarea:focus,
select:focus {
  outline: 2px solid hsl(var(--ring));
  outline-offset: 2px;
  border-color: hsl(var(--ring));
}

/* Fix for card shadows */
.card {
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
}

.card-hover:hover {
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

/* Fix for glassmorphism effect */
.glassmorphism {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 12px;
}

.dark .glassmorphism {
  background: rgba(0, 0, 0, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

/* ===== LAYOUT FIXES ===== */

/* Fix for sidebar layout */
.sidebar-layout {
  display: grid;
  grid-template-columns: auto 1fr;
  min-height: 100vh;
}

@media (max-width: 768px) {
  .sidebar-layout {
    grid-template-columns: 1fr;
  }
}

/* Fix for dashboard container */
.dashboard-container {
  min-height: 100vh;
  background: hsl(var(--background));
}

/* Fix for main content area */
.main-content {
  flex: 1;
  overflow-x: hidden;
  padding: 1rem;
}

@media (min-width: 768px) {
  .main-content {
    padding: 2rem;
  }
}

/* ===== ANIMATION FIXES ===== */

/* Fix for reduced motion */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }
}

/* Fix for smooth animations */
.animate-smooth {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Fix for loading states */
.loading {
  opacity: 0.6;
  pointer-events: none;
}

.loading::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 20px;
  height: 20px;
  margin: -10px 0 0 -10px;
  border: 2px solid hsl(var(--primary));
  border-radius: 50%;
  border-top-color: transparent;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

/* ===== RESPONSIVE FIXES ===== */

/* Fix for container queries */
.container {
  width: 100%;
  margin-left: auto;
  margin-right: auto;
  padding-left: 1rem;
  padding-right: 1rem;
}

@media (min-width: 640px) {
  .container {
    max-width: 640px;
  }
}

@media (min-width: 768px) {
  .container {
    max-width: 768px;
  }
}

@media (min-width: 1024px) {
  .container {
    max-width: 1024px;
  }
}

@media (min-width: 1280px) {
  .container {
    max-width: 1280px;
  }
}

/* Fix for mobile navigation */
@media (max-width: 768px) {
  .mobile-nav {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    background: hsl(var(--background));
    border-top: 1px solid hsl(var(--border));
    padding: 0.5rem;
    z-index: 50;
  }
}

/* ===== ACCESSIBILITY FIXES ===== */

/* Fix for focus indicators */
.focus-ring:focus {
  outline: 2px solid hsl(var(--ring));
  outline-offset: 2px;
}

/* Fix for high contrast mode */
@media (prefers-contrast: high) {
  .card {
    border: 2px solid hsl(var(--border));
  }
  
  .btn {
    border: 2px solid currentColor;
  }
}

/* Fix for dark mode */
.dark {
  color-scheme: dark;
}

/* ===== UTILITY FIXES ===== */

/* Fix for text truncation */
.truncate {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* Fix for aspect ratios */
.aspect-square {
  aspect-ratio: 1 / 1;
}

.aspect-video {
  aspect-ratio: 16 / 9;
}

/* Fix for scroll behavior */
.scroll-smooth {
  scroll-behavior: smooth;
}

/* Fix for overflow handling */
.overflow-hidden {
  overflow: hidden;
}

.overflow-auto {
  overflow: auto;
}

/* ===== PRINT FIXES ===== */

@media print {
  .no-print {
    display: none !important;
  }
  
  .print-only {
    display: block !important;
  }
  
  * {
    background: transparent !important;
    color: black !important;
    box-shadow: none !important;
    text-shadow: none !important;
  }
  
  a,
  a:visited {
    text-decoration: underline;
  }
  
  .page-break {
    page-break-before: always;
  }
}

/* ===== BROWSER SPECIFIC FIXES ===== */

/* Fix for Safari */
@supports (-webkit-appearance: none) {
  .safari-fix {
    -webkit-appearance: none;
  }
}

/* Fix for Firefox */
@-moz-document url-prefix() {
  .firefox-fix {
    -moz-appearance: none;
  }
}

/* Fix for Edge */
@supports (-ms-ime-align: auto) {
  .edge-fix {
    -ms-overflow-style: none;
  }
}

/* ===== COMPONENT SPECIFIC FIXES ===== */

/* Fix for sidebar component */
.sidebar {
  position: fixed;
  top: 0;
  left: 0;
  height: 100vh;
  width: 280px;
  background: hsl(var(--background));
  border-right: 1px solid hsl(var(--border));
  transform: translateX(-100%);
  transition: transform 0.3s ease-in-out;
  z-index: 40;
}

.sidebar.open {
  transform: translateX(0);
}

@media (min-width: 768px) {
  .sidebar {
    position: relative;
    transform: translateX(0);
  }
}

/* Fix for modal component */
.modal-overlay {
  position: fixed;
  inset: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 50;
}

.modal-content {
  background: hsl(var(--background));
  border-radius: 8px;
  padding: 1.5rem;
  max-width: 90vw;
  max-height: 90vh;
  overflow-y: auto;
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
}

/* Fix for dropdown component */
.dropdown {
  position: relative;
  display: inline-block;
}

.dropdown-content {
  position: absolute;
  top: 100%;
  left: 0;
  min-width: 160px;
  background: hsl(var(--background));
  border: 1px solid hsl(var(--border));
  border-radius: 6px;
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
  z-index: 10;
  opacity: 0;
  visibility: hidden;
  transform: translateY(-10px);
  transition: all 0.2s ease-in-out;
}

.dropdown.open .dropdown-content {
  opacity: 1;
  visibility: visible;
  transform: translateY(0);
}

/* Fix for toast notifications */
.toast {
  position: fixed;
  top: 1rem;
  right: 1rem;
  background: hsl(var(--background));
  border: 1px solid hsl(var(--border));
  border-radius: 6px;
  padding: 1rem;
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
  z-index: 60;
  transform: translateX(100%);
  transition: transform 0.3s ease-in-out;
}

.toast.show {
  transform: translateX(0);
}

.toast.success {
  border-left: 4px solid #10b981;
}

.toast.error {
  border-left: 4px solid #ef4444;
}

.toast.warning {
  border-left: 4px solid #f59e0b;
}

.toast.info {
  border-left: 4px solid #3b82f6;
}

/* Fix for loading spinner */
.spinner {
  display: inline-block;
  width: 20px;
  height: 20px;
  border: 2px solid hsl(var(--muted));
  border-radius: 50%;
  border-top-color: hsl(var(--primary));
  animation: spin 1s ease-in-out infinite;
}

/* Fix for progress bar */
.progress {
  width: 100%;
  height: 8px;
  background: hsl(var(--muted));
  border-radius: 4px;
  overflow: hidden;
}

.progress-bar {
  height: 100%;
  background: hsl(var(--primary));
  transition: width 0.3s ease-in-out;
}

/* Fix for tabs component */
.tabs {
  width: 100%;
}

.tab-list {
  display: flex;
  border-bottom: 1px solid hsl(var(--border));
}

.tab-button {
  padding: 0.75rem 1rem;
  border: none;
  background: transparent;
  color: hsl(var(--muted-foreground));
  cursor: pointer;
  border-bottom: 2px solid transparent;
  transition: all 0.2s ease-in-out;
}

.tab-button:hover {
  color: hsl(var(--foreground));
}

.tab-button.active {
  color: hsl(var(--primary));
  border-bottom-color: hsl(var(--primary));
}

.tab-content {
  padding: 1rem 0;
}

/* Fix for accordion component */
.accordion-item {
  border-bottom: 1px solid hsl(var(--border));
}

.accordion-header {
  width: 100%;
  padding: 1rem;
  text-align: left;
  background: transparent;
  border: none;
  cursor: pointer;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.accordion-content {
  max-height: 0;
  overflow: hidden;
  transition: max-height 0.3s ease-in-out;
}

.accordion-item.open .accordion-content {
  max-height: 1000px;
}

.accordion-body {
  padding: 0 1rem 1rem;
}
