/**
 * Component to test type imports and basic functionality
 */
import React, { useEffect, useState } from 'react'
import { logger } from '../utils/logger'

// Import types individually to test each one
import type {
  User,
  AuthResponse,
  Task,
  Project,
  DashboardStats,
  WebSocketMessage,
  ApiResponse,
  Notification
} from '../types'

export default function TypeTest() {
  const [testResults, setTestResults] = useState<string[]>([])

  useEffect(() => {
    logger.componentMount('TypeTest')

    // Test all type imports
    const results: string[] = []

    try {
      // Test User type
      const testUser: User = {
        id: '123',
        username: 'test',
        email: '<EMAIL>',
        role: 'user',
        department: 'IT'
      }
      results.push('✅ User type working')
      logger.debug('User type test passed', 'TypeTest', testUser)

      // Test AuthResponse type
      const testAuthResponse: AuthResponse = {
        access_token: 'test-token',
        token_type: 'Bearer',
        user: testUser
      }
      results.push('✅ AuthResponse type working')
      logger.debug('AuthResponse type test passed', 'TypeTest', testAuthResponse)

      // Test Task type
      const testTask: Task = {
        id: 'task-1',
        title: 'Test Task',
        status: 'pending',
        priority: 'medium',
        created_at: new Date().toISOString()
      }
      results.push('✅ Task type working')

      // Test Project type
      const testProject: Project = {
        id: 'project-1',
        name: 'Test Project',
        status: 'active',
        created_at: new Date().toISOString()
      }
      results.push('✅ Project type working')

      // Test DashboardStats type
      const testStats: DashboardStats = {
        total_tasks: 10,
        completed_tasks: 5,
        pending_tasks: 5,
        total_projects: 3,
        active_projects: 2
      }
      results.push('✅ DashboardStats type working')

      // Test WebSocketMessage type
      const testMessage: WebSocketMessage = {
        type: 'test',
        data: { test: true },
        timestamp: new Date().toISOString()
      }
      results.push('✅ WebSocketMessage type working')

      // Test ApiResponse type
      const testApiResponse: ApiResponse<User> = {
        success: true,
        data: testUser,
        message: 'Success'
      }
      results.push('✅ ApiResponse type working')

      // Test Notification type
      const testNotification: Notification = {
        id: 'notif-1',
        type: 'info',
        title: 'Test',
        message: 'Test notification',
        timestamp: new Date().toISOString()
      }
      results.push('✅ Notification type working')

      logger.info('All type tests passed successfully', 'TypeTest')

    } catch (error) {
      logger.error('Type test failed', 'TypeTest', error)
      results.push('❌ Type test failed: ' + (error as Error).message)
    }

    setTestResults(results)

    return () => {
      logger.componentUnmount('TypeTest')
    }
  }, [])

  return (
    <div style={{
      padding: '20px',
      backgroundColor: '#f8f9fa',
      borderRadius: '8px',
      margin: '20px 0'
    }}>
      <h2 style={{ color: '#333', marginBottom: '16px' }}>
        🧪 TypeScript Type Test
      </h2>

      <div style={{ marginBottom: '16px' }}>
        {testResults.map((result, index) => (
          <div
            key={index}
            style={{
              padding: '8px 12px',
              margin: '4px 0',
              backgroundColor: result.includes('✅') ? '#d4edda' : '#f8d7da',
              color: result.includes('✅') ? '#155724' : '#721c24',
              borderRadius: '4px',
              fontSize: '14px',
              fontFamily: 'monospace'
            }}
          >
            {result}
          </div>
        ))}
      </div>

      <div style={{
        fontSize: '12px',
        color: '#6c757d',
        fontFamily: 'monospace'
      }}>
        <p>Component mounted at: {new Date().toLocaleTimeString()}</p>
        <p>Environment: {import.meta.env.DEV ? 'Development' : 'Production'}</p>
        <p>Vite Version: {import.meta.env.VITE_VERSION || 'Unknown'}</p>
      </div>
    </div>
  )
}
