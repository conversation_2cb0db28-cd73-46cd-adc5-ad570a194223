// Simple icon generator for PWA
const fs = require('fs');
const { createCanvas } = require('canvas');

const sizes = [16, 32, 72, 96, 128, 144, 152, 192, 384, 512];

function createIcon(size) {
  const canvas = createCanvas(size, size);
  const ctx = canvas.getContext('2d');

  // Create gradient background
  const gradient = ctx.createRadialGradient(size/2, size/2, 0, size/2, size/2, size/2);
  gradient.addColorStop(0, '#ff0000');
  gradient.addColorStop(0.7, '#cc0000');
  gradient.addColorStop(1, '#990000');
  
  ctx.fillStyle = gradient;
  ctx.fillRect(0, 0, size, size);

  // Add border
  ctx.strokeStyle = '#ffffff';
  ctx.lineWidth = size * 0.02;
  ctx.strokeRect(ctx.lineWidth/2, ctx.lineWidth/2, size - ctx.lineWidth, size - ctx.lineWidth);

  // Add clock icon
  const centerX = size / 2;
  const centerY = size / 2;
  const radius = size * 0.3;

  // Clock circle
  ctx.beginPath();
  ctx.arc(centerX, centerY, radius, 0, 2 * Math.PI);
  ctx.fillStyle = '#ffffff';
  ctx.fill();
  ctx.strokeStyle = '#000000';
  ctx.lineWidth = size * 0.01;
  ctx.stroke();

  // Clock hands
  ctx.strokeStyle = '#000000';
  ctx.lineWidth = size * 0.015;
  ctx.lineCap = 'round';

  // Hour hand
  ctx.beginPath();
  ctx.moveTo(centerX, centerY);
  ctx.lineTo(centerX + radius * 0.5 * Math.cos(-Math.PI/2 + Math.PI/3), 
            centerY + radius * 0.5 * Math.sin(-Math.PI/2 + Math.PI/3));
  ctx.stroke();

  // Minute hand
  ctx.beginPath();
  ctx.moveTo(centerX, centerY);
  ctx.lineTo(centerX + radius * 0.7 * Math.cos(-Math.PI/2), 
            centerY + radius * 0.7 * Math.sin(-Math.PI/2));
  ctx.stroke();

  // Center dot
  ctx.beginPath();
  ctx.arc(centerX, centerY, size * 0.02, 0, 2 * Math.PI);
  ctx.fillStyle = '#ff0000';
  ctx.fill();

  return canvas.toBuffer('image/png');
}

// Generate all icon sizes
sizes.forEach(size => {
  try {
    const buffer = createIcon(size);
    fs.writeFileSync(`./icon-${size}x${size}.png`, buffer);
    console.log(`✅ Generated icon-${size}x${size}.png`);
  } catch (error) {
    console.log(`❌ Failed to generate ${size}x${size} icon:`, error.message);
  }
});

console.log('🎉 Icon generation complete!');
