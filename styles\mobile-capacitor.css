/* Mobile Capacitor Styles for CTNL AI Work-Board */
/* Styles specific to Capacitor mobile app environment */

/* Capacitor status bar adjustments */
.capacitor-app {
  /* Account for status bar on mobile devices */
  padding-top: env(safe-area-inset-top);
  padding-bottom: env(safe-area-inset-bottom);
  padding-left: env(safe-area-inset-left);
  padding-right: env(safe-area-inset-right);
}

/* Status bar styling */
.status-bar-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  height: env(safe-area-inset-top);
  background: #000000;
  z-index: 9999;
}

/* Navigation adjustments for Capacitor */
.capacitor-nav {
  position: fixed;
  top: env(safe-area-inset-top);
  left: 0;
  right: 0;
  background: rgba(0, 0, 0, 0.95);
  backdrop-filter: blur(20px);
  z-index: 1000;
}

/* Content area adjustments */
.capacitor-content {
  margin-top: calc(env(safe-area-inset-top) + 60px);
  margin-bottom: env(safe-area-inset-bottom);
  padding-left: env(safe-area-inset-left);
  padding-right: env(safe-area-inset-right);
}

/* Keyboard adjustments */
.capacitor-keyboard-adjust {
  /* Adjust for virtual keyboard */
  transition: transform 0.3s ease;
}

.capacitor-keyboard-open .capacitor-keyboard-adjust {
  transform: translateY(-100px);
}

/* Touch feedback */
.capacitor-touch-feedback {
  -webkit-tap-highlight-color: rgba(255, 0, 0, 0.1);
  -webkit-touch-callout: none;
  -webkit-user-select: none;
  user-select: none;
}

/* Scroll behavior */
.capacitor-scroll {
  -webkit-overflow-scrolling: touch;
  overflow-scrolling: touch;
}

/* Hardware back button handling */
.capacitor-back-button-handler {
  /* Styles for components that handle back button */
  position: relative;
}

/* Network status indicator */
.capacitor-network-status {
  position: fixed;
  top: env(safe-area-inset-top);
  left: 50%;
  transform: translateX(-50%);
  background: rgba(255, 0, 0, 0.9);
  color: white;
  padding: 0.25rem 0.5rem;
  border-radius: 0 0 8px 8px;
  font-size: 0.75rem;
  z-index: 10000;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.capacitor-network-status.offline {
  opacity: 1;
  background: rgba(220, 53, 69, 0.9);
}

.capacitor-network-status.online {
  background: rgba(40, 167, 69, 0.9);
}

/* Loading states for Capacitor */
.capacitor-loading {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: #000000;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10001;
}

.capacitor-loading-spinner {
  width: 40px;
  height: 40px;
  border: 3px solid rgba(255, 0, 0, 0.3);
  border-top: 3px solid #ff0000;
  border-radius: 50%;
  animation: capacitor-spin 1s linear infinite;
}

@keyframes capacitor-spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Camera and media adjustments */
.capacitor-camera-preview {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: #000000;
  z-index: 10000;
}

.capacitor-media-controls {
  position: absolute;
  bottom: env(safe-area-inset-bottom, 20px);
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  gap: 1rem;
}

/* File picker adjustments */
.capacitor-file-picker {
  /* Styles for file picker integration */
  width: 100%;
  height: 44px;
  background: transparent;
  border: 2px dashed rgba(255, 0, 0, 0.3);
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: rgba(255, 255, 255, 0.7);
  cursor: pointer;
  transition: all 0.3s ease;
}

.capacitor-file-picker:hover {
  border-color: rgba(255, 0, 0, 0.5);
  background: rgba(255, 0, 0, 0.05);
}

/* Geolocation indicator */
.capacitor-location-indicator {
  position: fixed;
  top: calc(env(safe-area-inset-top) + 10px);
  right: calc(env(safe-area-inset-right) + 10px);
  width: 12px;
  height: 12px;
  background: #ff0000;
  border-radius: 50%;
  opacity: 0;
  transition: opacity 0.3s ease;
  z-index: 1001;
}

.capacitor-location-indicator.active {
  opacity: 1;
  animation: capacitor-pulse 2s infinite;
}

@keyframes capacitor-pulse {
  0%, 100% { opacity: 1; transform: scale(1); }
  50% { opacity: 0.5; transform: scale(1.2); }
}

/* Push notification styles */
.capacitor-notification-badge {
  position: absolute;
  top: -5px;
  right: -5px;
  background: #ff0000;
  color: white;
  border-radius: 50%;
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.7rem;
  font-weight: bold;
}

/* Haptic feedback classes */
.capacitor-haptic-light {
  /* Light haptic feedback trigger */
}

.capacitor-haptic-medium {
  /* Medium haptic feedback trigger */
}

.capacitor-haptic-heavy {
  /* Heavy haptic feedback trigger */
}

/* Device orientation adjustments */
@media (orientation: landscape) {
  .capacitor-content {
    margin-top: calc(env(safe-area-inset-top) + 50px);
  }
  
  .capacitor-nav {
    height: 50px;
  }
}

@media (orientation: portrait) {
  .capacitor-content {
    margin-top: calc(env(safe-area-inset-top) + 60px);
  }
  
  .capacitor-nav {
    height: 60px;
  }
}

/* Platform-specific adjustments */
.capacitor-ios {
  /* iOS-specific styles */
}

.capacitor-android {
  /* Android-specific styles */
}

/* Splash screen styles */
.capacitor-splash {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: #000000;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  z-index: 10002;
}

.capacitor-splash-logo {
  width: 120px;
  height: 120px;
  margin-bottom: 2rem;
  animation: capacitor-fade-in 1s ease-in-out;
}

.capacitor-splash-text {
  color: #ff0000;
  font-size: 1.5rem;
  font-weight: bold;
  animation: capacitor-fade-in 1s ease-in-out 0.5s both;
}

@keyframes capacitor-fade-in {
  from { opacity: 0; transform: translateY(20px); }
  to { opacity: 1; transform: translateY(0); }
}

/* Error boundary for Capacitor */
.capacitor-error-boundary {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: #000000;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 2rem;
  z-index: 10003;
}

.capacitor-error-title {
  color: #ff0000;
  font-size: 1.5rem;
  font-weight: bold;
  margin-bottom: 1rem;
  text-align: center;
}

.capacitor-error-message {
  color: rgba(255, 255, 255, 0.8);
  text-align: center;
  margin-bottom: 2rem;
  line-height: 1.5;
}

.capacitor-error-button {
  background: linear-gradient(135deg, #ff0000 0%, #cc0000 100%);
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 8px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
}

.capacitor-error-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(255, 0, 0, 0.3);
}
