import React, { useState, useEffect } from 'react';

interface Repository {
  id: string;
  name: string;
  full_name: string;
  private: boolean;
  description: string;
  language: string;
  stars: number;
  forks: number;
  issues?: number;
  pull_requests?: number;
  last_commit?: {
    message: string;
    author: string;
    date: string;
  };
}

interface GitHubUser {
  login: string;
  name: string;
  email: string;
  avatar_url: string;
}

interface GitHubIntegrationProps {
  isOpen: boolean;
  onClose: () => void;
}

const GitHubIntegration: React.FC<GitHubIntegrationProps> = ({ isOpen, onClose }) => {
  const [isConnected, setIsConnected] = useState(false);
  const [repositories, setRepositories] = useState<Repository[]>([]);
  const [user, setUser] = useState<GitHubUser | null>(null);
  const [accessToken, setAccessToken] = useState('');
  const [isLoading, setIsLoading] = useState(false);

  const connectGitHub = async () => {
    if (!accessToken.trim()) {
      alert('Please enter your GitHub access token');
      return;
    }

    setIsLoading(true);
    try {
      const response = await fetch('http://localhost:8002/api/github/connect', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          access_token: accessToken,
          user_id: 'current_user'
        })
      });

      const data = await response.json();

      if (data.success) {
        setIsConnected(true);
        setRepositories(data.repositories || []);
        setUser(data.user_info);
        setAccessToken(''); // Clear token for security
      } else {
        alert(data.message || 'Failed to connect GitHub');
      }
    } catch (error) {
      console.error('GitHub connection error:', error);
      alert('Failed to connect to GitHub. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  const createIssueFromTask = async (repository: string, title: string, description: string) => {
    try {
      const response = await fetch('http://localhost:8002/api/github/create-issue', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          repository,
          title,
          description,
          labels: ['enhancement', 'ctnl-work-board']
        })
      });

      const data = await response.json();

      if (data.success) {
        alert(`GitHub issue #${data.issue.number} created successfully!`);
      } else {
        alert(data.message || 'Failed to create GitHub issue');
      }
    } catch (error) {
      console.error('Issue creation error:', error);
      alert('Failed to create GitHub issue. Please try again.');
    }
  };

  if (!isOpen) return null;

  return (
    <div style={{
      position: 'fixed',
      top: 0,
      left: 0,
      right: 0,
      bottom: 0,
      backgroundColor: 'rgba(0, 0, 0, 0.8)',
      zIndex: 1000,
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center'
    }}>
      <div style={{
        backgroundColor: '#1a1a1a',
        borderRadius: '16px',
        width: '90%',
        maxWidth: '800px',
        height: '80%',
        maxHeight: '700px',
        border: '2px solid #8B0000',
        boxShadow: '0 20px 40px rgba(139, 0, 0, 0.3)',
        display: 'flex',
        flexDirection: 'column',
        overflow: 'hidden'
      }}>
        {/* Header */}
        <div style={{
          padding: '20px',
          borderBottom: '1px solid #8B0000',
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
          background: 'linear-gradient(135deg, #2a2a2a, #1a1a1a)'
        }}>
          <h2 style={{
            margin: 0,
            color: '#8B0000',
            fontSize: '20px',
            fontWeight: 'bold'
          }}>
            🐙 GitHub Integration
          </h2>
          <button
            onClick={onClose}
            style={{
              background: 'none',
              border: 'none',
              color: '#8B0000',
              fontSize: '24px',
              cursor: 'pointer',
              padding: '4px'
            }}
          >
            ✕
          </button>
        </div>

        {/* Content */}
        <div style={{
          flex: 1,
          padding: '20px',
          overflowY: 'auto'
        }}>
          {!isConnected ? (
            <div style={{ textAlign: 'center' }}>
              <h3 style={{ color: '#8B0000', marginBottom: '20px' }}>
                Connect Your GitHub Account
              </h3>
              <p style={{ color: '#9ca3af', marginBottom: '20px' }}>
                Connect your GitHub account to sync repositories, create issues from tasks, and track development progress.
              </p>
              
              <div style={{ marginBottom: '20px' }}>
                <input
                  type="password"
                  placeholder="Enter GitHub Personal Access Token"
                  value={accessToken}
                  onChange={(e) => setAccessToken(e.target.value)}
                  style={{
                    width: '100%',
                    maxWidth: '400px',
                    padding: '12px',
                    backgroundColor: '#2a2a2a',
                    border: '1px solid #8B0000',
                    borderRadius: '8px',
                    color: '#ffffff',
                    fontSize: '14px',
                    marginBottom: '10px'
                  }}
                />
                <p style={{ color: '#9ca3af', fontSize: '12px', margin: 0 }}>
                  Generate a token at: Settings → Developer settings → Personal access tokens
                </p>
              </div>

              <button
                onClick={connectGitHub}
                disabled={isLoading}
                style={{
                  backgroundColor: '#8B0000',
                  color: '#ffffff',
                  border: 'none',
                  borderRadius: '8px',
                  padding: '12px 24px',
                  fontSize: '16px',
                  fontWeight: 'bold',
                  cursor: 'pointer',
                  opacity: isLoading ? 0.5 : 1
                }}
              >
                {isLoading ? 'Connecting...' : 'Connect GitHub'}
              </button>
            </div>
          ) : (
            <div>
              {/* User Info */}
              {user && (
                <div style={{
                  backgroundColor: '#2a2a2a',
                  padding: '16px',
                  borderRadius: '8px',
                  marginBottom: '20px',
                  border: '1px solid #8B0000'
                }}>
                  <div style={{ display: 'flex', alignItems: 'center', gap: '12px' }}>
                    <img
                      src={user.avatar_url}
                      alt={user.name}
                      style={{
                        width: '40px',
                        height: '40px',
                        borderRadius: '50%',
                        border: '2px solid #8B0000'
                      }}
                    />
                    <div>
                      <h4 style={{ margin: 0, color: '#8B0000' }}>{user.name}</h4>
                      <p style={{ margin: 0, color: '#9ca3af', fontSize: '14px' }}>@{user.login}</p>
                    </div>
                  </div>
                </div>
              )}

              {/* Repositories */}
              <h3 style={{ color: '#8B0000', marginBottom: '16px' }}>
                📚 Your Repositories
              </h3>
              
              <div style={{ display: 'grid', gap: '12px' }}>
                {repositories.map((repo) => (
                  <div
                    key={repo.id}
                    style={{
                      backgroundColor: '#2a2a2a',
                      padding: '16px',
                      borderRadius: '8px',
                      border: '1px solid #8B0000'
                    }}
                  >
                    <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start' }}>
                      <div style={{ flex: 1 }}>
                        <h4 style={{
                          margin: '0 0 8px 0',
                          color: '#8B0000',
                          fontSize: '16px'
                        }}>
                          {repo.private ? '🔒' : '📖'} {repo.name}
                        </h4>
                        <p style={{
                          margin: '0 0 8px 0',
                          color: '#9ca3af',
                          fontSize: '14px'
                        }}>
                          {repo.description}
                        </p>
                        <div style={{ display: 'flex', gap: '16px', fontSize: '12px', color: '#9ca3af' }}>
                          <span>🔤 {repo.language}</span>
                          <span>⭐ {repo.stars}</span>
                          <span>🍴 {repo.forks}</span>
                          {repo.issues && <span>🐛 {repo.issues} issues</span>}
                        </div>
                      </div>
                      <button
                        onClick={() => createIssueFromTask(
                          repo.full_name,
                          'New Task from CTNL WORK-BOARD',
                          'Task created via CTNL WORK-BOARD AI system'
                        )}
                        style={{
                          backgroundColor: '#8B0000',
                          color: '#ffffff',
                          border: 'none',
                          borderRadius: '6px',
                          padding: '6px 12px',
                          fontSize: '12px',
                          cursor: 'pointer'
                        }}
                      >
                        Create Issue
                      </button>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default GitHubIntegration;
