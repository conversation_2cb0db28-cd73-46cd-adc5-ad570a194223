/* Basic Tailwind-like utility classes */
.min-h-screen {
  min-height: 100vh;
}

.flex {
  display: flex;
}

.items-center {
  align-items: center;
}

.justify-center {
  justify-content: center;
}

.justify-between {
  justify-content: space-between;
}

.bg-gray-50 {
  background-color: #f9fafb;
}

.bg-white {
  background-color: #2a2a2a;
}

.bg-indigo-600 {
  background-color: #ff0000;
}

.bg-indigo-700 {
  background-color: #cc0000;
}

.bg-red-500 {
  background-color: #ef4444;
}

.bg-red-600 {
  background-color: #dc2626;
}

.bg-blue-500 {
  background-color: #ff0000;
}

.bg-blue-600 {
  background-color: #cc0000;
}

.bg-green-500 {
  background-color: #10b981;
}

.bg-yellow-500 {
  background-color: #f59e0b;
}

.bg-purple-500 {
  background-color: #ff0000;
}

.text-white {
  color: #8B0000;
}

.text-gray-900 {
  color: #111827;
}

.text-gray-600 {
  color: #4b5563;
}

.text-gray-500 {
  color: #6b7280;
}

.text-indigo-600 {
  color: #ff0000;
}

.text-red-700 {
  color: #b91c1c;
}

.text-green-700 {
  color: #15803d;
}

.rounded {
  border-radius: 0.25rem;
}

.rounded-lg {
  border-radius: 0.5rem;
}

.rounded-md {
  border-radius: 0.375rem;
}

.shadow {
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
}

.shadow-md {
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

.p-4 {
  padding: 1rem;
}

.p-6 {
  padding: 1.5rem;
}

.px-4 {
  padding-left: 1rem;
  padding-right: 1rem;
}

.py-2 {
  padding-top: 0.5rem;
  padding-bottom: 0.5rem;
}

.py-6 {
  padding-top: 1.5rem;
  padding-bottom: 1.5rem;
}

.mb-4 {
  margin-bottom: 1rem;
}

.mb-6 {
  margin-bottom: 1.5rem;
}

.mb-8 {
  margin-bottom: 2rem;
}

.space-x-4 > * + * {
  margin-left: 1rem;
}

.space-y-3 > * + * {
  margin-top: 0.75rem;
}

.w-full {
  width: 100%;
}

.max-w-md {
  max-width: 28rem;
}

.max-w-4xl {
  max-width: 56rem;
}

.max-w-7xl {
  max-width: 80rem;
}

.mx-auto {
  margin-left: auto;
  margin-right: auto;
}

.text-3xl {
  font-size: 1.875rem;
  line-height: 2.25rem;
}

.text-xl {
  font-size: 1.25rem;
  line-height: 1.75rem;
}

.text-lg {
  font-size: 1.125rem;
  line-height: 1.75rem;
}

.text-sm {
  font-size: 0.875rem;
  line-height: 1.25rem;
}

.font-bold {
  font-weight: 700;
}

.font-semibold {
  font-weight: 600;
}

.font-medium {
  font-weight: 500;
}

.border {
  border-width: 1px;
}

.border-gray-300 {
  border-color: #d1d5db;
}

.focus\:outline-none:focus {
  outline: 2px solid transparent;
  outline-offset: 2px;
}

.focus\:ring-2:focus {
  box-shadow: 0 0 0 3px rgba(255, 0, 0, 0.5);
}

.hover\:bg-indigo-700:hover {
  background-color: #cc0000;
}

.hover\:bg-red-600:hover {
  background-color: #dc2626;
}

.hover\:bg-blue-600:hover {
  background-color: #cc0000;
}

.disabled\:opacity-50:disabled {
  opacity: 0.5;
}

.disabled\:cursor-not-allowed:disabled {
  cursor: not-allowed;
}

.disabled\:bg-gray-300:disabled {
  background-color: #d1d5db;
}

.grid {
  display: grid;
}

.grid-cols-1 {
  grid-template-columns: repeat(1, minmax(0, 1fr));
}

.grid-cols-2 {
  grid-template-columns: repeat(2, minmax(0, 1fr));
}

.grid-cols-4 {
  grid-template-columns: repeat(4, minmax(0, 1fr));
}

.gap-4 {
  gap: 1rem;
}

.gap-6 {
  gap: 1.5rem;
}

@media (min-width: 768px) {
  .md\:grid-cols-2 {
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }

  .md\:grid-cols-4 {
    grid-template-columns: repeat(4, minmax(0, 1fr));
  }
}

@media (min-width: 1024px) {
  .lg\:grid-cols-2 {
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }

  .lg\:grid-cols-4 {
    grid-template-columns: repeat(4, minmax(0, 1fr));
  }
}

/* Dark Mode Form Elements */
select,
input[type="text"],
input[type="email"],
input[type="password"],
input[type="number"],
input[type="date"],
input[type="time"],
textarea {
  background-color: #2a2a2a !important;
  color: #8B0000 !important;
  border: 1px solid #8B0000 !important;
  border-radius: 8px !important;
}

select:focus,
input:focus,
textarea:focus {
  outline: none !important;
  border-color: #DC143C !important;
  box-shadow: 0 0 0 2px rgba(220, 20, 60, 0.2) !important;
}

select option {
  background-color: #2a2a2a !important;
  color: #8B0000 !important;
}

/* Dropdown Dark Mode */
.dropdown-menu,
.select-dropdown {
  background-color: #2a2a2a !important;
  border: 1px solid #8B0000 !important;
  border-radius: 8px !important;
  box-shadow: 0 4px 12px rgba(139, 0, 0, 0.3) !important;
}

.dropdown-item,
.select-option {
  color: #8B0000 !important;
  background-color: transparent !important;
}

.dropdown-item:hover,
.select-option:hover {
  background-color: #3a3a3a !important;
  color: #DC143C !important;
}
