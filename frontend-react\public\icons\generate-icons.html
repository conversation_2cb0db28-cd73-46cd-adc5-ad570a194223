<!DOCTYPE html>
<html>
<head>
    <title>PWA Icon Generator</title>
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; background: #1a1a1a; color: white; }
        canvas { border: 2px solid #ff0000; margin: 10px; }
        .icon-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(150px, 1fr)); gap: 20px; }
        .icon-item { text-align: center; }
    </style>
</head>
<body>
    <h1>🚀 CTNL PWA Icon Generator</h1>
    <button onclick="generateIcons()">Generate All Icons</button>
    <div id="iconGrid" class="icon-grid"></div>

    <script>
        function generateIcons() {
            const sizes = [72, 96, 128, 144, 152, 192, 384, 512];
            const iconGrid = document.getElementById('iconGrid');
            iconGrid.innerHTML = '';

            sizes.forEach(size => {
                const canvas = document.createElement('canvas');
                canvas.width = size;
                canvas.height = size;
                const ctx = canvas.getContext('2d');

                // Create gradient background
                const gradient = ctx.createRadialGradient(size/2, size/2, 0, size/2, size/2, size/2);
                gradient.addColorStop(0, '#ff0000');
                gradient.addColorStop(0.7, '#cc0000');
                gradient.addColorStop(1, '#990000');
                
                ctx.fillStyle = gradient;
                ctx.fillRect(0, 0, size, size);

                // Add border
                ctx.strokeStyle = '#ffffff';
                ctx.lineWidth = size * 0.02;
                ctx.strokeRect(ctx.lineWidth/2, ctx.lineWidth/2, size - ctx.lineWidth, size - ctx.lineWidth);

                // Add clock icon
                const centerX = size / 2;
                const centerY = size / 2;
                const radius = size * 0.3;

                // Clock circle
                ctx.beginPath();
                ctx.arc(centerX, centerY, radius, 0, 2 * Math.PI);
                ctx.fillStyle = '#ffffff';
                ctx.fill();
                ctx.strokeStyle = '#000000';
                ctx.lineWidth = size * 0.01;
                ctx.stroke();

                // Clock hands
                ctx.strokeStyle = '#000000';
                ctx.lineWidth = size * 0.015;
                ctx.lineCap = 'round';

                // Hour hand
                ctx.beginPath();
                ctx.moveTo(centerX, centerY);
                ctx.lineTo(centerX + radius * 0.5 * Math.cos(-Math.PI/2 + Math.PI/3), 
                          centerY + radius * 0.5 * Math.sin(-Math.PI/2 + Math.PI/3));
                ctx.stroke();

                // Minute hand
                ctx.beginPath();
                ctx.moveTo(centerX, centerY);
                ctx.lineTo(centerX + radius * 0.7 * Math.cos(-Math.PI/2), 
                          centerY + radius * 0.7 * Math.sin(-Math.PI/2));
                ctx.stroke();

                // Center dot
                ctx.beginPath();
                ctx.arc(centerX, centerY, size * 0.02, 0, 2 * Math.PI);
                ctx.fillStyle = '#ff0000';
                ctx.fill();

                // Create download link
                const iconItem = document.createElement('div');
                iconItem.className = 'icon-item';
                
                const label = document.createElement('p');
                label.textContent = `${size}x${size}`;
                
                const downloadLink = document.createElement('a');
                downloadLink.href = canvas.toDataURL('image/png');
                downloadLink.download = `icon-${size}x${size}.png`;
                downloadLink.appendChild(canvas);
                
                iconItem.appendChild(label);
                iconItem.appendChild(downloadLink);
                iconGrid.appendChild(iconItem);
            });
        }

        // Auto-generate on load
        window.onload = generateIcons;
    </script>
</body>
</html>
