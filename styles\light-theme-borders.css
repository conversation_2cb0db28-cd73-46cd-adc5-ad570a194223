/**
 * Light Theme Border Enhancement
 * Adds subtle border lines to buttons and cards for better visibility in light mode
 */

/* ============= LIGHT THEME BUTTON BORDERS ============= */

/* Primary Buttons */
:root:not(.dark) .btn-primary,
:root:not(.dark) button[class*="bg-primary"],
:root:not(.dark) button[class*="from-primary"],
:root:not(.dark) .bg-primary {
  border: 1px solid rgba(255, 28, 4, 0.3) !important;
  box-shadow:
    0 1px 3px rgba(0, 0, 0, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.2) !important;
}

/* Secondary Buttons */
:root:not(.dark) .btn-secondary,
:root:not(.dark) button[class*="bg-secondary"],
:root:not(.dark) .bg-secondary {
  border: 1px solid rgba(15, 160, 206, 0.3) !important;
  box-shadow: 
    0 1px 3px rgba(0, 0, 0, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.2) !important;
}

/* Outline Buttons */
:root:not(.dark) button[class*="variant-outline"],
:root:not(.dark) .btn-outline {
  border: 1px solid rgba(0, 0, 0, 0.15) !important;
  background: rgba(255, 255, 255, 0.8) !important;
  box-shadow: 
    0 1px 3px rgba(0, 0, 0, 0.08),
    inset 0 1px 0 rgba(255, 255, 255, 0.5) !important;
}

:root:not(.dark) button[class*="variant-outline"]:hover {
  border-color: rgba(0, 0, 0, 0.25) !important;
  background: rgba(255, 255, 255, 0.95) !important;
}

/* Default/Ghost Buttons */
:root:not(.dark) button[class*="variant-ghost"],
:root:not(.dark) .btn-ghost {
  border: 1px solid rgba(0, 0, 0, 0.08) !important;
  background: rgba(255, 255, 255, 0.6) !important;
}

:root:not(.dark) button[class*="variant-ghost"]:hover {
  border-color: rgba(0, 0, 0, 0.15) !important;
  background: rgba(255, 255, 255, 0.8) !important;
}

/* Destructive Buttons */
:root:not(.dark) button[class*="variant-destructive"],
:root:not(.dark) .btn-destructive {
  border: 1px solid rgba(220, 38, 38, 0.3) !important;
  box-shadow: 
    0 1px 3px rgba(0, 0, 0, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.2) !important;
}

/* Icon Buttons */
:root:not(.dark) button[class*="size-icon"] {
  border: 1px solid rgba(0, 0, 0, 0.1) !important;
  background: rgba(255, 255, 255, 0.8) !important;
  box-shadow: 
    0 1px 2px rgba(0, 0, 0, 0.05),
    inset 0 1px 0 rgba(255, 255, 255, 0.3) !important;
}

:root:not(.dark) button[class*="size-icon"]:hover {
  border-color: rgba(0, 0, 0, 0.2) !important;
  background: rgba(255, 255, 255, 0.95) !important;
}

/* ============= LIGHT THEME CARD BORDERS ============= */

/* Standard Cards */
:root:not(.dark) .card,
:root:not(.dark) [class*="bg-card"],
:root:not(.dark) div[class*="rounded-"],
:root:not(.dark) .glassmorphism {
  border: 1px solid rgba(0, 0, 0, 0.08) !important;
  box-shadow:
    0 2px 8px rgba(0, 0, 0, 0.06),
    0 1px 3px rgba(0, 0, 0, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.8) !important;
}

/* Elevated Cards */
:root:not(.dark) .card:hover,
:root:not(.dark) .card-hover:hover {
  border-color: rgba(0, 0, 0, 0.12) !important;
  box-shadow: 
    0 4px 16px rgba(0, 0, 0, 0.08),
    0 2px 6px rgba(0, 0, 0, 0.12),
    inset 0 1px 0 rgba(255, 255, 255, 0.9) !important;
}

/* Neumorphism Cards */
:root:not(.dark) .neumorphism,
:root:not(.dark) .neumorphic-card {
  border: 1px solid rgba(0, 0, 0, 0.06) !important;
  background: linear-gradient(145deg, #ffffff, #f5f5f5) !important;
  box-shadow: 
    8px 8px 16px rgba(0, 0, 0, 0.1),
    -8px -8px 16px rgba(255, 255, 255, 0.8),
    inset 0 1px 0 rgba(255, 255, 255, 0.9) !important;
}

/* Dashboard Cards */
:root:not(.dark) .dashboard-card {
  border: 1px solid rgba(0, 0, 0, 0.1) !important;
  background: rgba(255, 255, 255, 0.95) !important;
  box-shadow: 
    0 2px 12px rgba(0, 0, 0, 0.08),
    inset 0 1px 0 rgba(255, 255, 255, 0.9) !important;
}

/* Stats Cards */
:root:not(.dark) .stats-card,
:root:not(.dark) .metric-card {
  border: 1px solid rgba(0, 0, 0, 0.08) !important;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.95), rgba(248, 250, 252, 0.9)) !important;
  box-shadow: 
    0 1px 6px rgba(0, 0, 0, 0.06),
    inset 0 1px 0 rgba(255, 255, 255, 0.8) !important;
}

/* ============= LIGHT THEME FORM ELEMENTS ============= */

/* Input Fields */
:root:not(.dark) input[type="text"],
:root:not(.dark) input[type="email"],
:root:not(.dark) input[type="password"],
:root:not(.dark) input[type="number"],
:root:not(.dark) textarea,
:root:not(.dark) select {
  border: 1px solid rgba(0, 0, 0, 0.15) !important;
  background: rgba(255, 255, 255, 0.9) !important;
  box-shadow: 
    inset 0 1px 3px rgba(0, 0, 0, 0.06),
    0 1px 0 rgba(255, 255, 255, 0.8) !important;
}

:root:not(.dark) input:focus,
:root:not(.dark) textarea:focus,
:root:not(.dark) select:focus {
  border-color: rgba(255, 28, 4, 0.4) !important;
  box-shadow: 
    inset 0 1px 3px rgba(0, 0, 0, 0.06),
    0 0 0 3px rgba(255, 28, 4, 0.1) !important;
}

/* ============= LIGHT THEME NAVIGATION ============= */

/* Navigation Cards */
:root:not(.dark) .nav-card,
:root:not(.dark) .sidebar-card {
  border: 1px solid rgba(0, 0, 0, 0.08) !important;
  background: rgba(255, 255, 255, 0.95) !important;
  box-shadow: 
    0 2px 8px rgba(0, 0, 0, 0.06),
    inset 0 1px 0 rgba(255, 255, 255, 0.9) !important;
}

/* Tab Components */
:root:not(.dark) .tabs-list {
  border: 1px solid rgba(0, 0, 0, 0.1) !important;
  background: rgba(255, 255, 255, 0.8) !important;
  box-shadow: 
    0 1px 3px rgba(0, 0, 0, 0.06),
    inset 0 1px 0 rgba(255, 255, 255, 0.9) !important;
}

:root:not(.dark) .tabs-trigger {
  border: 1px solid transparent !important;
}

:root:not(.dark) .tabs-trigger[data-state="active"] {
  border-color: rgba(0, 0, 0, 0.1) !important;
  background: rgba(255, 255, 255, 0.95) !important;
  box-shadow: 
    0 1px 3px rgba(0, 0, 0, 0.08),
    inset 0 1px 0 rgba(255, 255, 255, 0.9) !important;
}

/* ============= LIGHT THEME MODALS AND DIALOGS ============= */

/* Modal/Dialog Cards */
:root:not(.dark) .dialog-content,
:root:not(.dark) .modal-content {
  border: 1px solid rgba(0, 0, 0, 0.12) !important;
  background: rgba(255, 255, 255, 0.98) !important;
  box-shadow: 
    0 8px 32px rgba(0, 0, 0, 0.12),
    0 4px 16px rgba(0, 0, 0, 0.08),
    inset 0 1px 0 rgba(255, 255, 255, 0.9) !important;
}

/* Popover Content */
:root:not(.dark) .popover-content {
  border: 1px solid rgba(0, 0, 0, 0.1) !important;
  background: rgba(255, 255, 255, 0.95) !important;
  box-shadow: 
    0 4px 16px rgba(0, 0, 0, 0.1),
    0 2px 8px rgba(0, 0, 0, 0.06),
    inset 0 1px 0 rgba(255, 255, 255, 0.9) !important;
}

/* ============= LIGHT THEME SPECIFIC COMPONENTS ============= */

/* Clock-in Cards */
:root:not(.dark) .clockin-card {
  border: 1px solid rgba(0, 0, 0, 0.1) !important;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.95), rgba(248, 250, 252, 0.9)) !important;
  box-shadow: 
    0 4px 16px rgba(0, 0, 0, 0.08),
    inset 0 1px 0 rgba(255, 255, 255, 0.9) !important;
}

/* Voice Navigation Button */
:root:not(.dark) .voice-nav-button {
  border: 1px solid rgba(255, 28, 4, 0.2) !important;
  box-shadow: 
    0 2px 8px rgba(255, 28, 4, 0.15),
    0 1px 3px rgba(0, 0, 0, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.3) !important;
}

/* AI System Cards */
:root:not(.dark) .ai-card {
  border: 1px solid rgba(0, 0, 0, 0.08) !important;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.95), rgba(240, 245, 255, 0.9)) !important;
  box-shadow: 
    0 3px 12px rgba(0, 0, 0, 0.06),
    inset 0 1px 0 rgba(255, 255, 255, 0.9) !important;
}

/* ============= LIGHT THEME HOVER EFFECTS ============= */

/* Enhanced Hover States */
:root:not(.dark) .card:hover,
:root:not(.dark) button:hover {
  transform: translateY(-1px);
  transition: all 0.2s ease-in-out;
}

:root:not(.dark) .card:hover {
  border-color: rgba(0, 0, 0, 0.15) !important;
}

:root:not(.dark) button:hover {
  border-color: rgba(0, 0, 0, 0.2) !important;
}

/* ============= LIGHT THEME ACCESSIBILITY ============= */

/* Focus States */
:root:not(.dark) *:focus-visible {
  outline: 2px solid rgba(255, 28, 4, 0.6) !important;
  outline-offset: 2px !important;
  border-color: rgba(255, 28, 4, 0.4) !important;
}

/* High Contrast Mode Support */
@media (prefers-contrast: high) {
  :root:not(.dark) .card,
  :root:not(.dark) button {
    border-width: 2px !important;
    border-color: rgba(0, 0, 0, 0.3) !important;
  }
}

/* ============= TAILWIND CSS SPECIFIC TARGETING ============= */

/* Target Tailwind button classes specifically */
:root:not(.dark) button {
  border: 1px solid rgba(0, 0, 0, 0.1) !important;
  box-shadow:
    0 1px 3px rgba(0, 0, 0, 0.08),
    inset 0 1px 0 rgba(255, 255, 255, 0.3) !important;
}

/* Target Tailwind card classes specifically */
:root:not(.dark) div[class*="rounded-"] {
  border: 1px solid rgba(0, 0, 0, 0.08) !important;
  box-shadow:
    0 2px 8px rgba(0, 0, 0, 0.06),
    inset 0 1px 0 rgba(255, 255, 255, 0.8) !important;
}

/* Override for specific component patterns */
:root:not(.dark) div[class*="bg-gradient"] {
  border: 1px solid rgba(255, 28, 4, 0.2) !important;
}

:root:not(.dark) div[class*="glassmorphism"] {
  border: 1px solid rgba(0, 0, 0, 0.06) !important;
  background: rgba(255, 255, 255, 0.85) !important;
  backdrop-filter: blur(10px) !important;
}

/* ============= RESPONSIVE BORDER ADJUSTMENTS ============= */

/* Mobile Devices */
@media (max-width: 768px) {
  :root:not(.dark) .card {
    border-width: 1px !important;
    box-shadow: 
      0 1px 6px rgba(0, 0, 0, 0.08),
      inset 0 1px 0 rgba(255, 255, 255, 0.8) !important;
  }
  
  :root:not(.dark) button {
    border-width: 1px !important;
    box-shadow: 
      0 1px 3px rgba(0, 0, 0, 0.08),
      inset 0 1px 0 rgba(255, 255, 255, 0.3) !important;
  }
}

/* Large Screens */
@media (min-width: 1200px) {
  :root:not(.dark) .card {
    box-shadow: 
      0 4px 16px rgba(0, 0, 0, 0.08),
      0 2px 8px rgba(0, 0, 0, 0.06),
      inset 0 1px 0 rgba(255, 255, 255, 0.9) !important;
  }
}
