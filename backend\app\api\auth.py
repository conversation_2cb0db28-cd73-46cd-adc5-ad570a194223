"""
Authentication API routes
"""
from fastapi import API<PERSON><PERSON><PERSON>, Depends, HTTPException, status, Request
from fastapi.security import HTT<PERSON><PERSON>earer
from motor.motor_asyncio import AsyncIOMotorDatabase
from typing import Dict, Any
from bson import ObjectId
from datetime import datetime
import logging

from ..core.database import get_database
from ..core.security import get_current_user_token, verify_token
from ..services.user_service import UserService
from ..schemas.user import (
    UserLogin, UserLoginResponse, TokenRefresh, 
    PasswordChange, PasswordReset, PasswordResetConfirm
)

router = APIRouter()
security = HTTPBearer()
logger = logging.getLogger(__name__)


@router.post("/login", response_model=UserLoginResponse)
async def login(
    login_data: UserLogin,
    request: Request
):
    """User login endpoint"""
    from ..core.database import mongodb

    # Check if database is available
    if mongodb.database is None:
        raise HTTPException(
            status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
            detail="Database service unavailable"
        )

    user_service = UserService(mongodb.database)

    # Get client IP and user agent
    client_ip = request.client.host
    user_agent = request.headers.get("user-agent")

    try:
        result = await user_service.authenticate_user(
            login_data=login_data,
            ip_address=client_ip,
            user_agent=user_agent
        )
        return result
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Login error: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Login failed"
        )


@router.post("/refresh")
async def refresh_token(
    token_data: TokenRefresh,
    db: AsyncIOMotorDatabase = Depends(get_database)
):
    """Refresh access token"""
    try:
        # Verify refresh token
        payload = verify_token(token_data.refresh_token)
        
        if payload.get("type") != "refresh":
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Invalid token type"
            )
        
        user_id = payload.get("sub")
        if not user_id:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Invalid token"
            )
        
        # Get user service
        user_service = UserService(db)
        
        # Get user details
        user = await user_service.get_user_by_id(user_id)
        if not user or not user.get("is_active"):
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="User not found or inactive"
            )
        
        # Create new access token
        from ..core.security import create_access_token
        token_data = {
            "sub": user_id,
            "username": user["username"],
            "email": user["email"],
            "role": user["role"],
            "department": user["department"]
        }
        
        access_token = create_access_token(token_data)
        
        return {
            "access_token": access_token,
            "token_type": "bearer",
            "expires_in": 30 * 60  # 30 minutes
        }
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Token refresh failed"
        )


@router.post("/logout")
async def logout(
    current_user: Dict[str, Any] = Depends(get_current_user_token),
    db: AsyncIOMotorDatabase = Depends(get_database)
):
    """User logout endpoint"""
    try:
        user_id = current_user.get("sub")
        
        # Update user status
        await db.users.update_one(
            {"_id": ObjectId(user_id)},
            {
                "$set": {
                    "is_online": False,
                    "activity.current_session_id": None,
                    "updated_at": datetime.utcnow()
                }
            }
        )
        
        # Deactivate all user sessions
        await db.user_sessions.update_many(
            {"user_id": ObjectId(user_id)},
            {"$set": {"is_active": False}}
        )
        
        return {"message": "Successfully logged out"}
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Logout failed"
        )


@router.get("/me")
async def get_current_user(
    current_user: Dict[str, Any] = Depends(get_current_user_token),
    db: AsyncIOMotorDatabase = Depends(get_database)
):
    """Get current user information"""
    user_service = UserService(db)
    user_id = current_user.get("sub")
    
    user = await user_service.get_user_by_id(user_id)
    if not user:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="User not found"
        )
    
    return user


@router.post("/change-password")
async def change_password(
    password_data: PasswordChange,
    current_user: Dict[str, Any] = Depends(get_current_user_token),
    db: AsyncIOMotorDatabase = Depends(get_database)
):
    """Change user password"""
    from bson import ObjectId
    from datetime import datetime
    from ..core.security import verify_password, get_password_hash
    
    user_id = current_user.get("sub")
    
    # Get current user
    user = await db.users.find_one({"_id": ObjectId(user_id)})
    if not user:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="User not found"
        )
    
    # Verify current password
    if not verify_password(password_data.current_password, user["hashed_password"]):
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Current password is incorrect"
        )
    
    # Update password
    new_password_hash = get_password_hash(password_data.new_password)
    
    await db.users.update_one(
        {"_id": ObjectId(user_id)},
        {
            "$set": {
                "hashed_password": new_password_hash,
                "security.last_password_change": datetime.utcnow(),
                "updated_at": datetime.utcnow()
            }
        }
    )
    
    # Invalidate all sessions except current one
    await db.user_sessions.update_many(
        {
            "user_id": ObjectId(user_id),
            "session_id": {"$ne": current_user.get("session_id")}
        },
        {"$set": {"is_active": False}}
    )
    
    return {"message": "Password changed successfully"}


@router.post("/forgot-password")
async def forgot_password(
    reset_data: PasswordReset,
    db: AsyncIOMotorDatabase = Depends(get_database)
):
    """Request password reset"""
    import secrets
    from datetime import datetime, timedelta
    from bson import ObjectId
    
    # Find user by email
    user = await db.users.find_one({"email": reset_data.email})
    if not user:
        # Don't reveal if email exists or not
        return {"message": "If the email exists, a reset link has been sent"}
    
    # Generate reset token
    reset_token = secrets.token_urlsafe(32)
    reset_expires = datetime.utcnow() + timedelta(hours=1)
    
    # Update user with reset token
    await db.users.update_one(
        {"_id": user["_id"]},
        {
            "$set": {
                "security.password_reset_token": reset_token,
                "security.password_reset_expires": reset_expires,
                "updated_at": datetime.utcnow()
            }
        }
    )
    
    # TODO: Send email with reset link
    # For now, just return success message
    
    return {"message": "If the email exists, a reset link has been sent"}


@router.post("/reset-password")
async def reset_password(
    reset_data: PasswordResetConfirm,
    db: AsyncIOMotorDatabase = Depends(get_database)
):
    """Reset password with token"""
    from bson import ObjectId
    from datetime import datetime
    from ..core.security import get_password_hash
    
    # Find user with valid reset token
    user = await db.users.find_one({
        "security.password_reset_token": reset_data.token,
        "security.password_reset_expires": {"$gt": datetime.utcnow()}
    })
    
    if not user:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Invalid or expired reset token"
        )
    
    # Update password and clear reset token
    new_password_hash = get_password_hash(reset_data.new_password)
    
    await db.users.update_one(
        {"_id": user["_id"]},
        {
            "$set": {
                "hashed_password": new_password_hash,
                "security.last_password_change": datetime.utcnow(),
                "security.password_reset_token": None,
                "security.password_reset_expires": None,
                "security.failed_login_attempts": 0,
                "security.account_locked_until": None,
                "updated_at": datetime.utcnow()
            }
        }
    )
    
    # Invalidate all user sessions
    await db.user_sessions.update_many(
        {"user_id": user["_id"]},
        {"$set": {"is_active": False}}
    )
    
    return {"message": "Password reset successfully"}


@router.get("/sessions")
async def get_user_sessions(
    current_user: Dict[str, Any] = Depends(get_current_user_token),
    db: AsyncIOMotorDatabase = Depends(get_database)
):
    """Get user's active sessions"""
    from bson import ObjectId
    
    user_id = current_user.get("sub")
    
    sessions = await db.user_sessions.find({
        "user_id": ObjectId(user_id),
        "is_active": True
    }).sort("last_accessed", -1).to_list(length=None)
    
    # Convert ObjectIds to strings and format response
    session_list = []
    for session in sessions:
        session_list.append({
            "id": str(session["_id"]),
            "session_id": session["session_id"],
            "ip_address": session.get("ip_address"),
            "user_agent": session.get("user_agent"),
            "created_at": session["created_at"],
            "last_accessed": session["last_accessed"],
            "expires_at": session["expires_at"]
        })
    
    return {"sessions": session_list}


@router.delete("/sessions/{session_id}")
async def revoke_session(
    session_id: str,
    current_user: Dict[str, Any] = Depends(get_current_user_token),
    db: AsyncIOMotorDatabase = Depends(get_database)
):
    """Revoke a specific session"""
    from bson import ObjectId
    
    user_id = current_user.get("sub")
    
    # Deactivate the session
    result = await db.user_sessions.update_one(
        {
            "user_id": ObjectId(user_id),
            "session_id": session_id
        },
        {"$set": {"is_active": False}}
    )
    
    if result.matched_count == 0:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Session not found"
        )
    
    return {"message": "Session revoked successfully"}
