"""
Reports and Analytics API routes
"""
from fastapi import APIRouter, Depends, HTTPException, status, Query
from motor.motor_asyncio import AsyncIOMotorDatabase
from typing import Dict, Any, List, Optional
from datetime import datetime, timedelta
from bson import ObjectId
from pydantic import BaseModel

from ..core.database import get_database, create_audit_log
from ..core.security import get_current_user_token, require_manager_or_admin
from ..core.config import Collections

router = APIRouter()


class ReportRequest(BaseModel):
    report_type: str  # productivity, project_summary, time_tracking, approval_summary
    start_date: datetime
    end_date: datetime
    filters: Dict[str, Any] = {}


@router.post("/generate")
async def generate_report(
    report_request: ReportRequest,
    current_user: Dict[str, Any] = Depends(require_manager_or_admin),
    db: AsyncIOMotorDatabase = Depends(get_database)
):
    """Generate a custom report"""
    
    user_id = current_user.get("sub")
    user_role = current_user.get("role")
    user_department = current_user.get("department")
    
    try:
        report_data = {}
        
        if report_request.report_type == "productivity":
            report_data = await _generate_productivity_report(
                report_request.start_date,
                report_request.end_date,
                report_request.filters,
                user_role,
                user_department,
                db
            )
        elif report_request.report_type == "project_summary":
            report_data = await _generate_project_summary_report(
                report_request.start_date,
                report_request.end_date,
                report_request.filters,
                user_role,
                user_department,
                db
            )
        elif report_request.report_type == "time_tracking":
            report_data = await _generate_time_tracking_report(
                report_request.start_date,
                report_request.end_date,
                report_request.filters,
                user_role,
                user_department,
                db
            )
        elif report_request.report_type == "approval_summary":
            report_data = await _generate_approval_summary_report(
                report_request.start_date,
                report_request.end_date,
                report_request.filters,
                user_role,
                user_department,
                db
            )
        else:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Invalid report type"
            )
        
        # Save report to database
        report_doc = {
            "report_type": report_request.report_type,
            "start_date": report_request.start_date,
            "end_date": report_request.end_date,
            "filters": report_request.filters,
            "data": report_data,
            "generated_by": ObjectId(user_id),
            "generated_at": datetime.utcnow(),
            "department": user_department if user_role != "admin" else None
        }
        
        result = await db["reports"].insert_one(report_doc)
        
        # Create audit log
        await create_audit_log(
            user_id=user_id,
            action="generate_report",
            resource_type="report",
            resource_id=str(result.inserted_id),
            details={
                "report_type": report_request.report_type,
                "date_range": f"{report_request.start_date.date()} to {report_request.end_date.date()}"
            }
        )
        
        return {
            "report_id": str(result.inserted_id),
            "report_type": report_request.report_type,
            "data": report_data,
            "generated_at": datetime.utcnow().isoformat()
        }
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to generate report"
        )


@router.get("/productivity")
async def get_productivity_metrics(
    start_date: Optional[str] = Query(None),
    end_date: Optional[str] = Query(None),
    department: Optional[str] = Query(None),
    user_id_filter: Optional[str] = Query(None),
    current_user: Dict[str, Any] = Depends(get_current_user_token),
    db: AsyncIOMotorDatabase = Depends(get_database)
):
    """Get productivity metrics"""
    
    user_id = current_user.get("sub")
    user_role = current_user.get("role")
    user_department = current_user.get("department")
    
    try:
        # Set default date range (last 30 days)
        if not start_date:
            start_dt = datetime.utcnow() - timedelta(days=30)
        else:
            start_dt = datetime.fromisoformat(start_date.replace('Z', '+00:00'))
        
        if not end_date:
            end_dt = datetime.utcnow()
        else:
            end_dt = datetime.fromisoformat(end_date.replace('Z', '+00:00'))
        
        # Build filter based on permissions
        filter_query = {
            "created_at": {"$gte": start_dt, "$lte": end_dt}
        }
        
        if user_role not in ["admin", "hr"]:
            if user_id_filter:
                # Can only view own data unless admin/hr
                if user_id_filter != user_id:
                    raise HTTPException(
                        status_code=status.HTTP_403_FORBIDDEN,
                        detail="Access denied"
                    )
                filter_query["assignee_id"] = ObjectId(user_id_filter)
            else:
                # Show department data for managers, own data for staff
                if user_role == "manager":
                    # Get department users
                    dept_users = await db[Collections.USERS].find({
                        "department": user_department
                    }).to_list(length=None)
                    dept_user_ids = [u["_id"] for u in dept_users]
                    filter_query["assignee_id"] = {"$in": dept_user_ids}
                else:
                    filter_query["assignee_id"] = ObjectId(user_id)
        elif department:
            # Admin can filter by department
            dept_users = await db[Collections.USERS].find({
                "department": department
            }).to_list(length=None)
            dept_user_ids = [u["_id"] for u in dept_users]
            filter_query["assignee_id"] = {"$in": dept_user_ids}
        elif user_id_filter:
            filter_query["assignee_id"] = ObjectId(user_id_filter)
        
        # Get task statistics
        task_pipeline = [
            {"$match": filter_query},
            {"$group": {
                "_id": "$status",
                "count": {"$sum": 1},
                "total_hours": {"$sum": "$actual_hours"}
            }}
        ]
        
        task_stats = await db[Collections.TASKS].aggregate(task_pipeline).to_list(length=None)
        
        # Get time tracking statistics
        time_filter = {
            "clock_in": {"$gte": start_dt, "$lte": end_dt}
        }
        
        if filter_query.get("assignee_id"):
            if isinstance(filter_query["assignee_id"], dict):
                time_filter["user_id"] = filter_query["assignee_id"]
            else:
                time_filter["user_id"] = filter_query["assignee_id"]
        
        time_logs = await db[Collections.TIME_LOGS].find(time_filter).to_list(length=None)
        
        # Calculate metrics
        total_tasks = sum([stat["count"] for stat in task_stats])
        completed_tasks = next((stat["count"] for stat in task_stats if stat["_id"] == "completed"), 0)
        completion_rate = (completed_tasks / total_tasks * 100) if total_tasks > 0 else 0
        
        total_logged_hours = sum([log.get("duration", 0) / 3600 for log in time_logs if log.get("duration")])
        total_task_hours = sum([stat["total_hours"] for stat in task_stats])
        
        # Daily productivity trend
        daily_stats = {}
        for log in time_logs:
            date_key = log["clock_in"].date().isoformat()
            if date_key not in daily_stats:
                daily_stats[date_key] = {"hours": 0, "sessions": 0}
            
            if log.get("duration"):
                daily_stats[date_key]["hours"] += log["duration"] / 3600
            daily_stats[date_key]["sessions"] += 1
        
        return {
            "period": {
                "start_date": start_dt.isoformat(),
                "end_date": end_dt.isoformat(),
                "days": (end_dt - start_dt).days + 1
            },
            "task_metrics": {
                "total_tasks": total_tasks,
                "completed_tasks": completed_tasks,
                "completion_rate": round(completion_rate, 2),
                "task_breakdown": {stat["_id"]: stat["count"] for stat in task_stats},
                "total_task_hours": round(total_task_hours, 2)
            },
            "time_metrics": {
                "total_logged_hours": round(total_logged_hours, 2),
                "total_sessions": len(time_logs),
                "avg_session_hours": round(total_logged_hours / len(time_logs), 2) if time_logs else 0,
                "avg_daily_hours": round(total_logged_hours / ((end_dt - start_dt).days + 1), 2)
            },
            "daily_trend": [
                {
                    "date": date,
                    "hours": round(stats["hours"], 2),
                    "sessions": stats["sessions"]
                }
                for date, stats in sorted(daily_stats.items())
            ]
        }
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to get productivity metrics"
        )


@router.get("/dashboard-summary")
async def get_dashboard_summary(
    current_user: Dict[str, Any] = Depends(get_current_user_token),
    db: AsyncIOMotorDatabase = Depends(get_database)
):
    """Get summary statistics for dashboard"""
    
    user_id = current_user.get("sub")
    user_role = current_user.get("role")
    user_department = current_user.get("department")
    
    try:
        summary = {}
        
        # Date ranges
        today = datetime.utcnow().replace(hour=0, minute=0, second=0, microsecond=0)
        week_ago = today - timedelta(days=7)
        month_ago = today - timedelta(days=30)
        
        if user_role == "admin":
            # System-wide statistics for admin
            summary = {
                "users": {
                    "total": await db[Collections.USERS].count_documents({}),
                    "active": await db[Collections.USERS].count_documents({"is_active": True}),
                    "online": await db[Collections.USERS].count_documents({"is_online": True})
                },
                "projects": {
                    "total": await db[Collections.PROJECTS].count_documents({}),
                    "active": await db[Collections.PROJECTS].count_documents({"status": "active"}),
                    "completed": await db[Collections.PROJECTS].count_documents({"status": "completed"})
                },
                "tasks": {
                    "total": await db[Collections.TASKS].count_documents({}),
                    "pending": await db[Collections.TASKS].count_documents({"status": "todo"}),
                    "in_progress": await db[Collections.TASKS].count_documents({"status": "in_progress"}),
                    "completed": await db[Collections.TASKS].count_documents({"status": "completed"})
                },
                "approvals": {
                    "pending": await db[Collections.APPROVALS].count_documents({"status": "pending"}),
                    "approved_today": await db[Collections.APPROVALS].count_documents({
                        "status": "approved",
                        "final_approved_at": {"$gte": today}
                    })
                }
            }
        
        elif user_role == "manager":
            # Department statistics for manager
            dept_users = await db[Collections.USERS].find({"department": user_department}).to_list(length=None)
            dept_user_ids = [u["_id"] for u in dept_users]
            
            summary = {
                "team": {
                    "total_members": len(dept_users),
                    "online_members": len([u for u in dept_users if u.get("is_online", False)])
                },
                "projects": {
                    "total": await db[Collections.PROJECTS].count_documents({"department": user_department}),
                    "active": await db[Collections.PROJECTS].count_documents({
                        "department": user_department,
                        "status": "active"
                    })
                },
                "tasks": {
                    "total": await db[Collections.TASKS].count_documents({
                        "assignee_id": {"$in": dept_user_ids}
                    }),
                    "pending": await db[Collections.TASKS].count_documents({
                        "assignee_id": {"$in": dept_user_ids},
                        "status": "todo"
                    }),
                    "in_progress": await db[Collections.TASKS].count_documents({
                        "assignee_id": {"$in": dept_user_ids},
                        "status": "in_progress"
                    })
                },
                "approvals": {
                    "pending_my_approval": await db[Collections.APPROVALS].count_documents({
                        "steps.approver_id": ObjectId(user_id),
                        "steps.status": "pending"
                    })
                }
            }
        
        else:
            # Personal statistics for staff
            user_obj_id = ObjectId(user_id)
            
            summary = {
                "my_tasks": {
                    "total": await db[Collections.TASKS].count_documents({"assignee_id": user_obj_id}),
                    "pending": await db[Collections.TASKS].count_documents({
                        "assignee_id": user_obj_id,
                        "status": "todo"
                    }),
                    "in_progress": await db[Collections.TASKS].count_documents({
                        "assignee_id": user_obj_id,
                        "status": "in_progress"
                    }),
                    "completed_this_week": await db[Collections.TASKS].count_documents({
                        "assignee_id": user_obj_id,
                        "status": "completed",
                        "completion_date": {"$gte": week_ago}
                    })
                },
                "time_tracking": {
                    "hours_today": 0,  # Will be calculated from time logs
                    "hours_this_week": 0  # Will be calculated from time logs
                },
                "my_approvals": {
                    "pending": await db[Collections.APPROVALS].count_documents({
                        "requester_id": user_obj_id,
                        "status": "pending"
                    })
                }
            }
            
            # Calculate time tracking hours
            today_logs = await db[Collections.TIME_LOGS].find({
                "user_id": user_obj_id,
                "clock_in": {"$gte": today}
            }).to_list(length=None)
            
            week_logs = await db[Collections.TIME_LOGS].find({
                "user_id": user_obj_id,
                "clock_in": {"$gte": week_ago}
            }).to_list(length=None)
            
            summary["time_tracking"]["hours_today"] = round(
                sum([log.get("duration", 0) / 3600 for log in today_logs if log.get("duration")]), 2
            )
            summary["time_tracking"]["hours_this_week"] = round(
                sum([log.get("duration", 0) / 3600 for log in week_logs if log.get("duration")]), 2
            )
        
        return {
            "summary": summary,
            "generated_at": datetime.utcnow().isoformat(),
            "user_role": user_role
        }
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to get dashboard summary"
        )


async def _generate_productivity_report(
    start_date: datetime,
    end_date: datetime,
    filters: Dict[str, Any],
    user_role: str,
    user_department: str,
    db: AsyncIOMotorDatabase
) -> Dict[str, Any]:
    """Generate productivity report"""
    
    # Implementation for productivity report
    return {
        "report_type": "productivity",
        "period": {
            "start": start_date.isoformat(),
            "end": end_date.isoformat()
        },
        "metrics": {
            "task_completion_rate": 85.5,
            "average_task_duration": 4.2,
            "productivity_score": 92.3
        }
    }


async def _generate_project_summary_report(
    start_date: datetime,
    end_date: datetime,
    filters: Dict[str, Any],
    user_role: str,
    user_department: str,
    db: AsyncIOMotorDatabase
) -> Dict[str, Any]:
    """Generate project summary report"""
    
    return {
        "report_type": "project_summary",
        "period": {
            "start": start_date.isoformat(),
            "end": end_date.isoformat()
        },
        "projects": []
    }


async def _generate_time_tracking_report(
    start_date: datetime,
    end_date: datetime,
    filters: Dict[str, Any],
    user_role: str,
    user_department: str,
    db: AsyncIOMotorDatabase
) -> Dict[str, Any]:
    """Generate time tracking report"""
    
    return {
        "report_type": "time_tracking",
        "period": {
            "start": start_date.isoformat(),
            "end": end_date.isoformat()
        },
        "time_metrics": {}
    }


async def _generate_approval_summary_report(
    start_date: datetime,
    end_date: datetime,
    filters: Dict[str, Any],
    user_role: str,
    user_department: str,
    db: AsyncIOMotorDatabase
) -> Dict[str, Any]:
    """Generate approval summary report"""
    
    return {
        "report_type": "approval_summary",
        "period": {
            "start": start_date.isoformat(),
            "end": end_date.isoformat()
        },
        "approval_metrics": {}
    }
