#!/usr/bin/env python3
"""
Simple MongoDB Connection Test
"""

import asyncio
from motor.motor_asyncio import AsyncIOMotorClient

async def test_mongodb():
    """Test MongoDB connection"""
    try:
        print("🧪 Testing MongoDB Connection...")
        
        # Simple connection
        client = AsyncIOMotorClient("mongodb://localhost:27017")
        
        # Test ping
        result = await client.admin.command('ping')
        print(f"✅ MongoDB Ping Result: {result}")
        
        # Test database access
        db = client.ctnl_ai_workboard
        print(f"✅ Database Access: {db.name}")
        
        # Test collection access
        collection = db.test_collection
        
        # Insert a test document
        test_doc = {"test": "data", "timestamp": "2025-07-29"}
        result = await collection.insert_one(test_doc)
        print(f"✅ Insert Test: {result.inserted_id}")
        
        # Find the document
        found_doc = await collection.find_one({"test": "data"})
        print(f"✅ Find Test: {found_doc}")
        
        # Clean up
        await collection.delete_one({"test": "data"})
        print("✅ Cleanup Complete")
        
        client.close()
        print("🎉 MongoDB is working perfectly!")
        return True
        
    except Exception as e:
        print(f"❌ MongoDB Test Failed: {e}")
        return False

if __name__ == "__main__":
    asyncio.run(test_mongodb())
