/**
 * API Service for backend communication
 */
import axios from 'axios'
import { User, AuthResponse, TasksResponse, ProjectsResponse, DashboardStats } from '../types'

// API Configuration
const API_BASE_URL = import.meta.env.VITE_API_URL || 'http://localhost:8002'

console.log('🔧 API Base URL:', API_BASE_URL, 'Timestamp:', Date.now())
console.log('🚨 USING PORT 8002 - NOT 8000!')

// Create axios instance
const api = axios.create({
  baseURL: API_BASE_URL,
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
  },
})

// Request interceptor to add auth token
api.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('authToken')
    if (token) {
      config.headers.Authorization = `Bearer ${token}`
    }
    return config
  },
  (error) => {
    return Promise.reject(error)
  }
)

// Response interceptor for error handling
api.interceptors.response.use(
  (response) => response,
  (error) => {
    if (error.response?.status === 401) {
      // Unauthorized - clear token and redirect to login
      localStorage.removeItem('authToken')
      localStorage.removeItem('user')
      window.location.href = '/login'
    }
    return Promise.reject(error)
  }
)

// Auth API
export const authAPI = {
  login: async (credentials: { username: string; password: string }): Promise<AuthResponse> => {
    const response = await api.post('/api/auth/login', credentials)
    return response.data
  },

  register: async (userData: {
    username: string
    email: string
    password: string
    role?: string
    department?: string
  }): Promise<AuthResponse> => {
    const response = await api.post('/api/auth/register', userData)
    return response.data
  },

  logout: async (): Promise<void> => {
    const response = await api.post('/api/auth/logout')
    return response.data
  },

  getCurrentUser: async (): Promise<User> => {
    const response = await api.get('/api/auth/me')
    return response.data
  },
}

// Tasks API
export const tasksAPI = {
  getTasks: async (params?: {
    project_id?: string
    status?: string
    assigned_to?: string
    page?: number
    limit?: number
  }): Promise<TasksResponse> => {
    const response = await api.get('/api/tasks', { params })
    return response.data
  },

  getTask: async (taskId: string) => {
    const response = await api.get(`/api/tasks/${taskId}`)
    return response.data
  },

  createTask: async (taskData: {
    title: string
    description?: string
    project_id?: string
    assigned_to?: string
    priority?: string
    due_date?: string
    status?: string
  }) => {
    const response = await api.post('/api/tasks', taskData)
    return response.data
  },

  updateTask: async (taskId: string, updates: any) => {
    const response = await api.put(`/api/tasks/${taskId}`, updates)
    return response.data
  },

  deleteTask: async (taskId: string) => {
    const response = await api.delete(`/api/tasks/${taskId}`)
    return response.data
  },
}

// Projects API
export const projectsAPI = {
  getProjects: async (params?: {
    status?: string
    page?: number
    limit?: number
  }) => {
    const response = await api.get('/projects', { params })
    return response.data
  },
  
  getProject: async (projectId: string) => {
    const response = await api.get(`/projects/${projectId}`)
    return response.data
  },
  
  createProject: async (projectData: {
    name: string
    description?: string
    status?: string
    start_date?: string
    end_date?: string
  }) => {
    const response = await api.post('/projects', projectData)
    return response.data
  },
  
  updateProject: async (projectId: string, updates: any) => {
    const response = await api.put(`/projects/${projectId}`, updates)
    return response.data
  },
  
  deleteProject: async (projectId: string) => {
    const response = await api.delete(`/projects/${projectId}`)
    return response.data
  },
}

// Dashboard API
export const dashboardAPI = {
  getStats: async (): Promise<DashboardStats> => {
    const response = await api.get('/api/dashboard/stats')
    return response.data
  },

  getRecentActivity: async () => {
    const response = await api.get('/api/dashboard/activity')
    return response.data
  },
}

// Users API
export const usersAPI = {
  getUsers: async (params?: {
    role?: string
    department?: string
    page?: number
    limit?: number
  }) => {
    const response = await api.get('/users', { params })
    return response.data
  },
  
  getUser: async (userId: string) => {
    const response = await api.get(`/users/${userId}`)
    return response.data
  },
  
  updateUser: async (userId: string, updates: any) => {
    const response = await api.put(`/users/${userId}`, updates)
    return response.data
  },
}

export default api
