# FastAPI and ASGI server
fastapi==0.104.1
uvicorn[standard]==0.24.0
python-multipart==0.0.6

# Database
motor==3.3.2  # Async MongoDB driver
pymongo==4.6.0
redis==5.0.1

# Authentication & Security
python-jose[cryptography]==3.3.0
passlib[bcrypt]==1.7.4
bcrypt==4.1.2

# AI & ML
openai>=1.6.1
langchain==0.0.350
langchain-openai==0.0.2
tiktoken==0.5.2

# File handling & Storage
boto3==1.34.0  # AWS S3
Pillow==10.1.0
python-magic==0.4.27

# HTTP & Networking
httpx==0.25.2
aiofiles==23.2.1
websockets==12.0

# Data validation & serialization
pydantic==2.5.0
pydantic-settings==2.1.0

# Background tasks
celery==5.3.4
flower==2.0.1

# Monitoring & Logging
sentry-sdk[fastapi]==1.38.0
structlog==23.2.0

# Development
pytest==7.4.3
pytest-asyncio==0.21.1
black==23.11.0
isort==5.12.0
flake8==6.1.0

# Email
emails==0.6.0

# Environment
python-dotenv==1.0.0

# Socket.IO for real-time features
python-socketio==5.10.0
python-socketio[asyncio]==5.10.0

# PDF processing
PyPDF2==3.0.1

# Excel processing
openpyxl==3.1.2
pandas==2.1.4

# Image processing
opencv-python==********

# Timezone handling
pytz==2023.3

# CORS
fastapi-cors==0.0.6
