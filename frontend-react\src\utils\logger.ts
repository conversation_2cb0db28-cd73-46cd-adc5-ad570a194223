/**
 * Comprehensive logging utility with error handling
 */

export enum LogLevel {
  DEBUG = 0,
  INFO = 1,
  WARN = 2,
  ERROR = 3,
}

export interface LogEntry {
  level: LogLevel
  message: string
  timestamp: Date
  context?: string
  data?: any
  error?: Error
}

class Logger {
  private logLevel: LogLevel = LogLevel.INFO
  private logs: LogEntry[] = []
  private maxLogs = 1000

  constructor() {
    // Set log level based on environment
    if (import.meta.env.DEV) {
      this.logLevel = LogLevel.DEBUG
    }
  }

  private log(level: LogLevel, message: string, context?: string, data?: any, error?: Error) {
    if (level < this.logLevel) return

    const entry: LogEntry = {
      level,
      message,
      timestamp: new Date(),
      context,
      data,
      error,
    }

    // Add to internal log storage
    this.logs.push(entry)
    if (this.logs.length > this.maxLogs) {
      this.logs.shift()
    }

    // Console output with styling
    const timestamp = entry.timestamp.toISOString()
    const contextStr = context ? `[${context}]` : ''
    const logMessage = `${timestamp} ${contextStr} ${message}`

    switch (level) {
      case LogLevel.DEBUG:
        console.debug('🔍', logMessage, data || '')
        break
      case LogLevel.INFO:
        console.info('ℹ️', logMessage, data || '')
        break
      case LogLevel.WARN:
        console.warn('⚠️', logMessage, data || '')
        break
      case LogLevel.ERROR:
        console.error('❌', logMessage, error || data || '')
        if (error) {
          console.error('Stack trace:', error.stack)
        }
        break
    }
  }

  debug(message: string, context?: string, data?: any) {
    this.log(LogLevel.DEBUG, message, context, data)
  }

  info(message: string, context?: string, data?: any) {
    this.log(LogLevel.INFO, message, context, data)
  }

  warn(message: string, context?: string, data?: any) {
    this.log(LogLevel.WARN, message, context, data)
  }

  error(message: string, context?: string, error?: Error | any) {
    if (error instanceof Error) {
      this.log(LogLevel.ERROR, message, context, undefined, error)
    } else {
      this.log(LogLevel.ERROR, message, context, error)
    }
  }

  // API specific logging
  apiRequest(method: string, url: string, data?: any) {
    this.debug(`API Request: ${method} ${url}`, 'API', data)
  }

  apiResponse(method: string, url: string, status: number, data?: any) {
    if (status >= 400) {
      this.error(`API Error: ${method} ${url} - Status ${status}`, 'API', data)
    } else {
      this.debug(`API Response: ${method} ${url} - Status ${status}`, 'API', data)
    }
  }

  // WebSocket specific logging
  wsConnect(url: string) {
    this.info(`WebSocket connecting to: ${url}`, 'WebSocket')
  }

  wsConnected(url: string) {
    this.info(`WebSocket connected to: ${url}`, 'WebSocket')
  }

  wsDisconnected(url: string, reason?: string) {
    this.warn(`WebSocket disconnected from: ${url}`, 'WebSocket', { reason })
  }

  wsMessage(type: string, data?: any) {
    this.debug(`WebSocket message: ${type}`, 'WebSocket', data)
  }

  wsError(error: Error | string) {
    this.error('WebSocket error', 'WebSocket', error)
  }

  // Authentication logging
  authLogin(username: string) {
    this.info(`User login attempt: ${username}`, 'Auth')
  }

  authLoginSuccess(username: string) {
    this.info(`User login successful: ${username}`, 'Auth')
  }

  authLoginFailed(username: string, reason: string) {
    this.warn(`User login failed: ${username}`, 'Auth', { reason })
  }

  authLogout(username: string) {
    this.info(`User logout: ${username}`, 'Auth')
  }

  // Component lifecycle logging
  componentMount(componentName: string) {
    this.debug(`Component mounted: ${componentName}`, 'Component')
  }

  componentUnmount(componentName: string) {
    this.debug(`Component unmounted: ${componentName}`, 'Component')
  }

  componentError(componentName: string, error: Error) {
    this.error(`Component error in ${componentName}`, 'Component', error)
  }

  // Get logs for debugging
  getLogs(level?: LogLevel): LogEntry[] {
    if (level !== undefined) {
      return this.logs.filter(log => log.level >= level)
    }
    return [...this.logs]
  }

  // Clear logs
  clearLogs() {
    this.logs = []
    this.info('Logs cleared', 'Logger')
  }

  // Export logs as JSON
  exportLogs(): string {
    return JSON.stringify(this.logs, null, 2)
  }
}

// Create singleton instance
export const logger = new Logger()

// Error boundary helper
export class ErrorHandler {
  static handleError(error: Error, context?: string, additionalData?: any) {
    logger.error(`Unhandled error: ${error.message}`, context, error)
    
    // In production, you might want to send errors to a monitoring service
    if (!import.meta.env.DEV) {
      // Example: Send to error monitoring service
      // errorMonitoringService.captureException(error, { context, additionalData })
    }
  }

  static handleApiError(error: any, context?: string) {
    if (error.response) {
      // Server responded with error status
      const status = error.response.status
      const message = error.response.data?.message || error.message
      logger.error(`API Error ${status}: ${message}`, context, error.response.data)
      return {
        message,
        status,
        data: error.response.data
      }
    } else if (error.request) {
      // Network error
      logger.error('Network error: No response received', context, error)
      return {
        message: 'Network error. Please check your connection.',
        status: 0
      }
    } else {
      // Other error
      logger.error(`Request error: ${error.message}`, context, error)
      return {
        message: error.message || 'An unexpected error occurred',
        status: -1
      }
    }
  }

  static handleWebSocketError(error: Event | Error, context?: string) {
    if (error instanceof Error) {
      logger.wsError(error)
    } else {
      logger.wsError('WebSocket connection error')
    }
  }
}

// Performance monitoring
export class PerformanceMonitor {
  private static timers = new Map<string, number>()

  static startTimer(name: string) {
    this.timers.set(name, performance.now())
    logger.debug(`Timer started: ${name}`, 'Performance')
  }

  static endTimer(name: string) {
    const startTime = this.timers.get(name)
    if (startTime) {
      const duration = performance.now() - startTime
      this.timers.delete(name)
      logger.debug(`Timer ended: ${name} - ${duration.toFixed(2)}ms`, 'Performance')
      return duration
    }
    return 0
  }

  static measureAsync<T>(name: string, fn: () => Promise<T>): Promise<T> {
    this.startTimer(name)
    return fn().finally(() => this.endTimer(name))
  }
}

export default logger
