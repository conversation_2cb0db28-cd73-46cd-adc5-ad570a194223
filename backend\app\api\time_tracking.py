"""
Time Tracking API routes
"""
from fastapi import APIRouter, Depends, HTTPException, status
from motor.motor_asyncio import AsyncIOMotorDatabase
from typing import Dict, Any, Optional
from datetime import datetime, timedelta
from bson import ObjectId
from pydantic import BaseModel

from ..core.database import get_database, create_audit_log
from ..core.security import get_current_user_token
from ..core.config import Collections

router = APIRouter()


class LocationData(BaseModel):
    latitude: float
    longitude: float
    accuracy: Optional[float] = None


class ClockInData(BaseModel):
    location: Optional[LocationData] = None
    notes: Optional[str] = None
    project_id: Optional[str] = None
    task_id: Optional[str] = None


class ClockOutData(BaseModel):
    location: Optional[LocationData] = None
    notes: Optional[str] = None


@router.post("/clock-in")
async def clock_in(
    data: ClockInData,
    current_user: Dict[str, Any] = Depends(get_current_user_token),
    db: AsyncIOMotorDatabase = Depends(get_database)
):
    """Clock in user"""
    user_id = current_user.get("sub")
    user_obj_id = ObjectId(user_id)
    
    try:
        # Check if user is already clocked in
        existing_log = await db[Collections.TIME_LOGS].find_one({
            "user_id": user_obj_id,
            "clock_out": {"$exists": False}
        })
        
        if existing_log:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="User is already clocked in"
            )
        
        # Create new time log entry
        time_log = {
            "user_id": user_obj_id,
            "clock_in": datetime.utcnow(),
            "clock_out": None,
            "duration": None,
            "location": data.location.dict() if data.location else None,
            "notes": data.notes,
            "project_id": ObjectId(data.project_id) if data.project_id else None,
            "task_id": ObjectId(data.task_id) if data.task_id else None,
            "is_approved": False,
            "approved_by": None,
            "approved_at": None,
            "created_at": datetime.utcnow()
        }
        
        result = await db[Collections.TIME_LOGS].insert_one(time_log)
        
        # Update user status
        await db[Collections.USERS].update_one(
            {"_id": user_obj_id},
            {
                "$set": {
                    "is_online": True,
                    "activity.last_active": datetime.utcnow(),
                    "updated_at": datetime.utcnow()
                }
            }
        )
        
        # Create audit log
        await create_audit_log(
            user_id=user_id,
            action="clock_in",
            resource_type="time_log",
            resource_id=str(result.inserted_id),
            details={
                "clock_in_time": datetime.utcnow().isoformat(),
                "location": data.location.dict() if data.location else None
            }
        )
        
        return {
            "message": "Clocked in successfully",
            "time_log_id": str(result.inserted_id),
            "clock_in_time": datetime.utcnow().isoformat()
        }
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to clock in"
        )


@router.post("/clock-out")
async def clock_out(
    data: ClockOutData,
    current_user: Dict[str, Any] = Depends(get_current_user_token),
    db: AsyncIOMotorDatabase = Depends(get_database)
):
    """Clock out user"""
    user_id = current_user.get("sub")
    user_obj_id = ObjectId(user_id)
    
    try:
        # Find active time log
        active_log = await db[Collections.TIME_LOGS].find_one({
            "user_id": user_obj_id,
            "clock_out": {"$exists": False}
        })
        
        if not active_log:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="User is not currently clocked in"
            )
        
        clock_out_time = datetime.utcnow()
        clock_in_time = active_log["clock_in"]
        duration = (clock_out_time - clock_in_time).total_seconds()
        
        # Update time log with clock out information
        await db[Collections.TIME_LOGS].update_one(
            {"_id": active_log["_id"]},
            {
                "$set": {
                    "clock_out": clock_out_time,
                    "duration": duration,
                    "notes": f"{active_log.get('notes', '')} {data.notes}".strip() if data.notes else active_log.get('notes'),
                    "updated_at": clock_out_time
                }
            }
        )
        
        # Update user status
        await db[Collections.USERS].update_one(
            {"_id": user_obj_id},
            {
                "$set": {
                    "activity.last_active": clock_out_time,
                    "updated_at": clock_out_time
                }
            }
        )
        
        # Create audit log
        await create_audit_log(
            user_id=user_id,
            action="clock_out",
            resource_type="time_log",
            resource_id=str(active_log["_id"]),
            details={
                "clock_out_time": clock_out_time.isoformat(),
                "duration_hours": round(duration / 3600, 2),
                "location": data.location.dict() if data.location else None
            }
        )
        
        return {
            "message": "Clocked out successfully",
            "time_log_id": str(active_log["_id"]),
            "clock_out_time": clock_out_time.isoformat(),
            "duration_hours": round(duration / 3600, 2)
        }
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to clock out"
        )


@router.get("/status")
async def get_time_tracking_status(
    current_user: Dict[str, Any] = Depends(get_current_user_token),
    db: AsyncIOMotorDatabase = Depends(get_database)
):
    """Get current time tracking status for user"""
    user_id = current_user.get("sub")
    user_obj_id = ObjectId(user_id)
    
    try:
        # Check if user is currently clocked in
        active_log = await db[Collections.TIME_LOGS].find_one({
            "user_id": user_obj_id,
            "clock_out": {"$exists": False}
        })
        
        is_clocked_in = active_log is not None
        last_clock_in = active_log["clock_in"] if active_log else None
        
        # Get today's total hours
        today_start = datetime.utcnow().replace(hour=0, minute=0, second=0, microsecond=0)
        today_logs = await db[Collections.TIME_LOGS].find({
            "user_id": user_obj_id,
            "clock_in": {"$gte": today_start}
        }).to_list(length=None)
        
        today_hours = 0
        for log in today_logs:
            if log.get("duration"):
                today_hours += log["duration"] / 3600
            elif log.get("clock_in") and not log.get("clock_out"):
                # Currently active session
                current_duration = (datetime.utcnow() - log["clock_in"]).total_seconds()
                today_hours += current_duration / 3600
        
        # Get week's total hours
        week_start = today_start - timedelta(days=today_start.weekday())
        week_logs = await db[Collections.TIME_LOGS].find({
            "user_id": user_obj_id,
            "clock_in": {"$gte": week_start}
        }).to_list(length=None)
        
        week_hours = 0
        for log in week_logs:
            if log.get("duration"):
                week_hours += log["duration"] / 3600
            elif log.get("clock_in") and not log.get("clock_out"):
                # Currently active session
                current_duration = (datetime.utcnow() - log["clock_in"]).total_seconds()
                week_hours += current_duration / 3600
        
        return {
            "is_clocked_in": is_clocked_in,
            "last_clock_in": last_clock_in.isoformat() if last_clock_in else None,
            "today_hours": round(today_hours, 2),
            "week_hours": round(week_hours, 2),
            "current_session_duration": round((datetime.utcnow() - last_clock_in).total_seconds() / 3600, 2) if last_clock_in else 0
        }
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to get time tracking status"
        )


@router.get("/logs")
async def get_time_logs(
    start_date: Optional[str] = None,
    end_date: Optional[str] = None,
    page: int = 1,
    size: int = 20,
    current_user: Dict[str, Any] = Depends(get_current_user_token),
    db: AsyncIOMotorDatabase = Depends(get_database)
):
    """Get time logs for user"""
    user_id = current_user.get("sub")
    user_obj_id = ObjectId(user_id)
    
    try:
        # Build filter query
        filter_query = {"user_id": user_obj_id}
        
        if start_date:
            start_dt = datetime.fromisoformat(start_date.replace('Z', '+00:00'))
            filter_query["clock_in"] = {"$gte": start_dt}
        
        if end_date:
            end_dt = datetime.fromisoformat(end_date.replace('Z', '+00:00'))
            if "clock_in" in filter_query:
                filter_query["clock_in"]["$lte"] = end_dt
            else:
                filter_query["clock_in"] = {"$lte": end_dt}
        
        # Get total count
        total = await db[Collections.TIME_LOGS].count_documents(filter_query)
        
        # Calculate pagination
        skip = (page - 1) * size
        pages = (total + size - 1) // size
        
        # Get logs
        logs = await db[Collections.TIME_LOGS].find(filter_query).sort("clock_in", -1).skip(skip).limit(size).to_list(length=None)
        
        # Format response
        formatted_logs = []
        for log in logs:
            # Get project and task names if available
            project_name = None
            task_name = None
            
            if log.get("project_id"):
                project = await db[Collections.PROJECTS].find_one({"_id": log["project_id"]})
                project_name = project.get("name") if project else None
            
            if log.get("task_id"):
                task = await db[Collections.TASKS].find_one({"_id": log["task_id"]})
                task_name = task.get("title") if task else None
            
            duration_hours = None
            if log.get("duration"):
                duration_hours = round(log["duration"] / 3600, 2)
            elif log.get("clock_in") and not log.get("clock_out"):
                # Currently active session
                current_duration = (datetime.utcnow() - log["clock_in"]).total_seconds()
                duration_hours = round(current_duration / 3600, 2)
            
            formatted_logs.append({
                "id": str(log["_id"]),
                "clock_in": log["clock_in"].isoformat(),
                "clock_out": log["clock_out"].isoformat() if log.get("clock_out") else None,
                "duration_hours": duration_hours,
                "location": log.get("location"),
                "notes": log.get("notes"),
                "project_id": str(log["project_id"]) if log.get("project_id") else None,
                "project_name": project_name,
                "task_id": str(log["task_id"]) if log.get("task_id") else None,
                "task_name": task_name,
                "is_approved": log.get("is_approved", False),
                "approved_by": str(log["approved_by"]) if log.get("approved_by") else None,
                "approved_at": log["approved_at"].isoformat() if log.get("approved_at") else None,
                "created_at": log["created_at"].isoformat()
            })
        
        return {
            "logs": formatted_logs,
            "total": total,
            "page": page,
            "size": size,
            "pages": pages
        }
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve time logs"
        )


@router.get("/stats")
async def get_time_tracking_stats(
    current_user: Dict[str, Any] = Depends(get_current_user_token),
    db: AsyncIOMotorDatabase = Depends(get_database)
):
    """Get time tracking statistics for user"""
    user_id = current_user.get("sub")
    user_obj_id = ObjectId(user_id)
    
    try:
        # Get current status
        status_data = await get_time_tracking_status(current_user, db)
        
        # Get monthly hours
        month_start = datetime.utcnow().replace(day=1, hour=0, minute=0, second=0, microsecond=0)
        month_logs = await db[Collections.TIME_LOGS].find({
            "user_id": user_obj_id,
            "clock_in": {"$gte": month_start}
        }).to_list(length=None)
        
        month_hours = sum([
            log["duration"] / 3600 for log in month_logs if log.get("duration")
        ])
        
        # Get average daily hours (last 30 days)
        thirty_days_ago = datetime.utcnow() - timedelta(days=30)
        recent_logs = await db[Collections.TIME_LOGS].find({
            "user_id": user_obj_id,
            "clock_in": {"$gte": thirty_days_ago},
            "duration": {"$exists": True}
        }).to_list(length=None)
        
        if recent_logs:
            total_recent_hours = sum([log["duration"] / 3600 for log in recent_logs])
            avg_daily_hours = total_recent_hours / 30
        else:
            avg_daily_hours = 0
        
        return {
            **status_data,
            "month_hours": round(month_hours, 2),
            "avg_daily_hours": round(avg_daily_hours, 2),
            "total_sessions_this_month": len(month_logs)
        }
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to get time tracking statistics"
        )
