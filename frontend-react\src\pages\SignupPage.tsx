/**
 * Professional Sign-up Page Component
 * Features: Multi-step form, validation, password strength, department selection
 */
import React, { useState, useEffect } from 'react'
import { logger, <PERSON>rror<PERSON><PERSON><PERSON>, PerformanceMonitor } from '../utils/logger'
import type { RegisterData, User } from '../types'

interface SignupPageProps {
  onSignupSuccess?: (user: User) => void
  onNavigateToLogin?: () => void
}

interface SignupState {
  isLoading: boolean
  message: string
  messageType: 'info' | 'success' | 'error'
  showPassword: boolean
  showConfirmPassword: boolean
  currentStep: number
  passwordStrength: number
}

interface FormData extends RegisterData {
  confirmPassword: string
  firstName: string
  lastName: string
  agreeToTerms: boolean
}

const departments = [
  'General', 'Engineering', 'Marketing', 'Sales', 'HR', 'Finance', 
  'Operations', 'Customer Support', 'Design', 'Product', 'Legal'
]

const roles = [
  { value: 'staff', label: '1. Staff' },
  { value: 'accountant', label: '2. Accountant' },
  { value: 'staff-admin', label: '3. Staff-Admin' },
  { value: 'admin', label: '4. Admin' }
]

export default function SignupPage({ onSignupSuccess, onNavigateToLogin }: SignupPageProps) {
  // Add CSS animations for message popups
  React.useEffect(() => {
    const style = document.createElement('style')
    style.textContent = `
      @keyframes fadeInSlide {
        0% {
          opacity: 0;
          transform: translateY(-20px) scale(0.95);
        }
        100% {
          opacity: 1;
          transform: translateY(0) scale(1);
        }
      }

      @keyframes shake {
        0%, 100% { transform: translateX(0); }
        25% { transform: translateX(-5px); }
        75% { transform: translateX(5px); }
      }
    `
    document.head.appendChild(style)
    return () => document.head.removeChild(style)
  }, [])
  const [formData, setFormData] = useState<FormData>({
    username: '',
    email: '',
    password: '',
    confirmPassword: '',
    firstName: '',
    lastName: '',
    role: 'user',
    department: 'General',
    agreeToTerms: false
  })
  
  const [state, setState] = useState<SignupState>({
    isLoading: false,
    message: '',
    messageType: 'info',
    showPassword: false,
    showConfirmPassword: false,
    currentStep: 1,
    passwordStrength: 0
  })

  useEffect(() => {
    logger.componentMount('SignupPage')
    return () => {
      logger.componentUnmount('SignupPage')
    }
  }, [])

  useEffect(() => {
    // Calculate password strength
    const calculatePasswordStrength = (password: string): number => {
      let strength = 0
      if (password.length >= 8) strength += 25
      if (/[a-z]/.test(password)) strength += 25
      if (/[A-Z]/.test(password)) strength += 25
      if (/[0-9]/.test(password)) strength += 25
      if (/[^A-Za-z0-9]/.test(password)) strength += 25
      return Math.min(strength, 100)
    }
    
    setState(prev => ({ 
      ...prev, 
      passwordStrength: calculatePasswordStrength(formData.password) 
    }))
  }, [formData.password])

  const updateMessage = (message: string, type: 'info' | 'success' | 'error' = 'info') => {
    setState(prev => ({ ...prev, message, messageType: type }))
  }

  const validateStep1 = (): boolean => {
    if (!formData.firstName.trim()) {
      updateMessage('Please enter your first name', 'error')
      return false
    }
    if (!formData.lastName.trim()) {
      updateMessage('Please enter your last name', 'error')
      return false
    }
    if (!formData.email.trim()) {
      updateMessage('Please enter your email address', 'error')
      return false
    }
    if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) {
      updateMessage('Please enter a valid email address', 'error')
      return false
    }
    return true
  }

  const validateStep2 = (): boolean => {
    if (!formData.username.trim()) {
      updateMessage('Please enter a username', 'error')
      return false
    }
    if (formData.username.length < 3) {
      updateMessage('Username must be at least 3 characters long', 'error')
      return false
    }
    if (!formData.password) {
      updateMessage('Please enter a password', 'error')
      return false
    }
    if (formData.password.length < 8) {
      updateMessage('Password must be at least 8 characters long', 'error')
      return false
    }
    if (formData.password !== formData.confirmPassword) {
      updateMessage('Passwords do not match', 'error')
      return false
    }
    if (state.passwordStrength < 50) {
      updateMessage('Please choose a stronger password', 'error')
      return false
    }
    return true
  }

  const validateStep3 = (): boolean => {
    if (!formData.agreeToTerms) {
      updateMessage('Please agree to the terms and conditions', 'error')
      return false
    }
    return true
  }

  const handleNext = () => {
    let isValid = false
    
    if (state.currentStep === 1) {
      isValid = validateStep1()
    } else if (state.currentStep === 2) {
      isValid = validateStep2()
    }
    
    if (isValid) {
      setState(prev => ({ 
        ...prev, 
        currentStep: prev.currentStep + 1,
        message: ''
      }))
    }
  }

  const handlePrevious = () => {
    setState(prev => ({ 
      ...prev, 
      currentStep: prev.currentStep - 1,
      message: ''
    }))
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!validateStep3()) return

    setState(prev => ({ ...prev, isLoading: true }))
    updateMessage('Creating your account...', 'info')

    logger.info('User registration attempt', 'SignupPage', { 
      username: formData.username,
      email: formData.email 
    })
    PerformanceMonitor.startTimer('signup_request')

    try {
      const registerData: RegisterData = {
        username: formData.username,
        email: formData.email,
        password: formData.password,
        role: formData.role,
        department: formData.department
      }

      const response = await fetch('http://localhost:8002/api/auth/register', {
        method: 'POST',
        mode: 'cors',
        credentials: 'include',
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json',
        },
        body: JSON.stringify(registerData),
      })

      const duration = PerformanceMonitor.endTimer('signup_request')

      if (response.ok) {
        const data = await response.json()
        
        logger.info(`Registration successful in ${duration.toFixed(2)}ms`, 'SignupPage', {
          username: formData.username
        })

        // Enhanced success message with countdown
        updateMessage(`🎉 Account created successfully! Welcome, ${formData.firstName}!`, 'success')

        // Auto-redirect with countdown
        let countdown = 3
        const countdownInterval = setInterval(() => {
          countdown--
          if (countdown > 0) {
            updateMessage(`🎉 Welcome, ${formData.firstName}! Redirecting to login in ${countdown}...`, 'success')
          } else {
            clearInterval(countdownInterval)
            if (onSignupSuccess && data.user) {
              onSignupSuccess(data.user)
            } else if (onNavigateToLogin) {
              onNavigateToLogin()
            }
          }
        }, 1000)
        
      } else {
        const errorData = await response.json().catch(() => ({ detail: 'Registration failed' }))
        let userFriendlyMessage = ''

        // Handle specific error cases
        if (response.status === 400) {
          if (errorData.detail && errorData.detail.includes('already exists')) {
            userFriendlyMessage = '👤 This username or email is already taken. Please try a different one.'
          } else {
            userFriendlyMessage = '📝 Please check your information and try again.'
          }
        } else if (response.status === 500) {
          userFriendlyMessage = '🔧 Server error. Please try again in a moment.'
        } else {
          userFriendlyMessage = errorData.detail || errorData.message || 'Registration failed. Please try again.'
        }

        logger.error('Registration failed', 'SignupPage', {
          error: errorData.detail || errorData.message,
          status: response.status
        })
        updateMessage(userFriendlyMessage, 'error')
      }
      
    } catch (error) {
      const handledError = ErrorHandler.handleApiError(error, 'SignupPage')
      logger.error('Registration error', 'SignupPage', error)

      // User-friendly connection error message
      const connectionMessage = '🌐 Unable to connect to the server. Please check your internet connection and try again.'
      updateMessage(connectionMessage, 'error')
    } finally {
      setState(prev => ({ ...prev, isLoading: false }))
    }
  }

  const handleInputChange = (field: keyof FormData, value: string | boolean) => {
    setFormData(prev => ({ ...prev, [field]: value }))
    // Clear error message when user starts typing
    if (state.message && state.messageType === 'error') {
      setState(prev => ({ ...prev, message: '' }))
    }
  }

  const getPasswordStrengthColor = (strength: number): string => {
    if (strength < 25) return '#ef4444'
    if (strength < 50) return '#f59e0b'
    if (strength < 75) return '#eab308'
    return '#10b981'
  }

  const getPasswordStrengthText = (strength: number): string => {
    if (strength < 25) return 'Weak'
    if (strength < 50) return 'Fair'
    if (strength < 75) return 'Good'
    return 'Strong'
  }

  const renderStep1 = () => (
    <div>
      <h2 style={{ fontSize: '24px', fontWeight: 'bold', color: '#1f2937', marginBottom: '24px', textAlign: 'center' }}>
        Personal Information
      </h2>
      
      <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '16px', marginBottom: '20px' }}>
        <div>
          <label style={{ display: 'block', fontSize: '14px', fontWeight: '500', color: '#374151', marginBottom: '6px' }}>
            First Name
          </label>
          <input
            type="text"
            value={formData.firstName}
            onChange={(e) => handleInputChange('firstName', e.target.value)}
            placeholder="John"
            style={{
              width: '100%',
              padding: '12px 16px',
              border: '2px solid #e5e7eb',
              borderRadius: '8px',
              fontSize: '16px',
              boxSizing: 'border-box'
            }}
          />
        </div>
        
        <div>
          <label style={{ display: 'block', fontSize: '14px', fontWeight: '500', color: '#374151', marginBottom: '6px' }}>
            Last Name
          </label>
          <input
            type="text"
            value={formData.lastName}
            onChange={(e) => handleInputChange('lastName', e.target.value)}
            placeholder="Doe"
            style={{
              width: '100%',
              padding: '12px 16px',
              border: '2px solid #e5e7eb',
              borderRadius: '8px',
              fontSize: '16px',
              boxSizing: 'border-box'
            }}
          />
        </div>
      </div>

      <div style={{ marginBottom: '20px' }}>
        <label style={{ display: 'block', fontSize: '14px', fontWeight: '500', color: '#374151', marginBottom: '6px' }}>
          Email Address
        </label>
        <input
          type="email"
          value={formData.email}
          onChange={(e) => handleInputChange('email', e.target.value)}
          placeholder="<EMAIL>"
          style={{
            width: '100%',
            padding: '12px 16px',
            border: '2px solid #e5e7eb',
            borderRadius: '8px',
            fontSize: '16px',
            boxSizing: 'border-box'
          }}
        />
      </div>
    </div>
  )

  const renderStep2 = () => (
    <div>
      <h2 style={{ fontSize: '24px', fontWeight: 'bold', color: '#1f2937', marginBottom: '24px', textAlign: 'center' }}>
        Account Details
      </h2>
      
      <div style={{ marginBottom: '20px' }}>
        <label style={{ display: 'block', fontSize: '14px', fontWeight: '500', color: '#374151', marginBottom: '6px' }}>
          Username
        </label>
        <input
          type="text"
          value={formData.username}
          onChange={(e) => handleInputChange('username', e.target.value)}
          placeholder="johndoe"
          style={{
            width: '100%',
            padding: '12px 16px',
            border: '2px solid #e5e7eb',
            borderRadius: '8px',
            fontSize: '16px',
            boxSizing: 'border-box'
          }}
        />
      </div>

      <div style={{ marginBottom: '20px' }}>
        <label style={{ display: 'block', fontSize: '14px', fontWeight: '500', color: '#374151', marginBottom: '6px' }}>
          Password
        </label>
        <div style={{ position: 'relative' }}>
          <input
            type={state.showPassword ? 'text' : 'password'}
            value={formData.password}
            onChange={(e) => handleInputChange('password', e.target.value)}
            placeholder="Enter a strong password"
            style={{
              width: '100%',
              padding: '12px 48px 12px 16px',
              border: '2px solid #e5e7eb',
              borderRadius: '8px',
              fontSize: '16px',
              boxSizing: 'border-box'
            }}
          />
          <button
            type="button"
            onClick={() => setState(prev => ({ ...prev, showPassword: !prev.showPassword }))}
            style={{
              position: 'absolute',
              right: '12px',
              top: '50%',
              transform: 'translateY(-50%)',
              background: 'none',
              border: 'none',
              fontSize: '18px',
              cursor: 'pointer',
              color: '#6b7280'
            }}
          >
            {state.showPassword ? '🙈' : '👁️'}
          </button>
        </div>
        
        {formData.password && (
          <div style={{ marginTop: '8px' }}>
            <div style={{
              width: '100%',
              height: '4px',
              backgroundColor: '#e5e7eb',
              borderRadius: '2px',
              overflow: 'hidden'
            }}>
              <div style={{
                width: `${state.passwordStrength}%`,
                height: '100%',
                backgroundColor: getPasswordStrengthColor(state.passwordStrength),
                transition: 'all 0.3s ease'
              }} />
            </div>
            <p style={{
              fontSize: '12px',
              color: getPasswordStrengthColor(state.passwordStrength),
              margin: '4px 0 0 0'
            }}>
              Password strength: {getPasswordStrengthText(state.passwordStrength)}
            </p>
          </div>
        )}
      </div>

      <div style={{ marginBottom: '20px' }}>
        <label style={{ display: 'block', fontSize: '14px', fontWeight: '500', color: '#374151', marginBottom: '6px' }}>
          Confirm Password
        </label>
        <div style={{ position: 'relative' }}>
          <input
            type={state.showConfirmPassword ? 'text' : 'password'}
            value={formData.confirmPassword}
            onChange={(e) => handleInputChange('confirmPassword', e.target.value)}
            placeholder="Confirm your password"
            style={{
              width: '100%',
              padding: '12px 48px 12px 16px',
              border: '2px solid #e5e7eb',
              borderRadius: '8px',
              fontSize: '16px',
              boxSizing: 'border-box'
            }}
          />
          <button
            type="button"
            onClick={() => setState(prev => ({ ...prev, showConfirmPassword: !prev.showConfirmPassword }))}
            style={{
              position: 'absolute',
              right: '12px',
              top: '50%',
              transform: 'translateY(-50%)',
              background: 'none',
              border: 'none',
              fontSize: '18px',
              cursor: 'pointer',
              color: '#6b7280'
            }}
          >
            {state.showConfirmPassword ? '🙈' : '👁️'}
          </button>
        </div>
        
        {formData.confirmPassword && formData.password !== formData.confirmPassword && (
          <p style={{ fontSize: '12px', color: '#ef4444', margin: '4px 0 0 0' }}>
            Passwords do not match
          </p>
        )}
      </div>
    </div>
  )

  const renderStep3 = () => (
    <div>
      <h2 style={{ fontSize: '24px', fontWeight: 'bold', color: '#1f2937', marginBottom: '24px', textAlign: 'center' }}>
        Work Information
      </h2>
      
      <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '16px', marginBottom: '20px' }}>
        <div>
          <label style={{ display: 'block', fontSize: '14px', fontWeight: '500', color: '#374151', marginBottom: '6px' }}>
            Department
          </label>
          <select
            value={formData.department}
            onChange={(e) => handleInputChange('department', e.target.value)}
            style={{
              width: '100%',
              padding: '12px 16px',
              border: '2px solid #ff0000',
              borderRadius: '8px',
              fontSize: '16px',
              backgroundColor: '#1a1a1a',
              color: '#ffffff',
              boxSizing: 'border-box',
              outline: 'none',
              transition: 'all 0.3s ease'
            }}
            onFocus={(e) => {
              e.target.style.borderColor = '#cc0000'
              e.target.style.boxShadow = '0 0 0 3px rgba(255, 0, 0, 0.1)'
            }}
            onBlur={(e) => {
              e.target.style.borderColor = '#ff0000'
              e.target.style.boxShadow = 'none'
            }}
          >
            {departments.map(dept => (
              <option key={dept} value={dept}>{dept}</option>
            ))}
          </select>
        </div>
        
        <div>
          <label style={{ display: 'block', fontSize: '14px', fontWeight: '500', color: '#374151', marginBottom: '6px' }}>
            Role
          </label>
          <select
            value={formData.role}
            onChange={(e) => handleInputChange('role', e.target.value)}
            style={{
              width: '100%',
              padding: '12px 16px',
              border: '2px solid #ff0000',
              borderRadius: '8px',
              fontSize: '16px',
              backgroundColor: '#1a1a1a',
              color: '#ffffff',
              boxSizing: 'border-box',
              outline: 'none',
              transition: 'all 0.3s ease'
            }}
            onFocus={(e) => {
              e.target.style.borderColor = '#cc0000'
              e.target.style.boxShadow = '0 0 0 3px rgba(255, 0, 0, 0.1)'
            }}
            onBlur={(e) => {
              e.target.style.borderColor = '#ff0000'
              e.target.style.boxShadow = 'none'
            }}
          >
            {roles.map(role => (
              <option key={role.value} value={role.value}>
                {role.label}
              </option>
            ))}
          </select>
        </div>
      </div>

      <div style={{ marginBottom: '24px' }}>
        <label style={{
          display: 'flex',
          alignItems: 'flex-start',
          cursor: 'pointer',
          fontSize: '14px',
          color: '#374151'
        }}>
          <input
            type="checkbox"
            checked={formData.agreeToTerms}
            onChange={(e) => handleInputChange('agreeToTerms', e.target.checked)}
            style={{ marginRight: '12px', marginTop: '2px' }}
          />
          <span>
            I agree to the{' '}
            <button
              type="button"
              style={{
                background: 'none',
                border: 'none',
                color: '#667eea',
                textDecoration: 'underline',
                cursor: 'pointer',
                fontSize: '14px'
              }}
            >
              Terms and Conditions
            </button>
            {' '}and{' '}
            <button
              type="button"
              style={{
                background: 'none',
                border: 'none',
                color: '#667eea',
                textDecoration: 'underline',
                cursor: 'pointer',
                fontSize: '14px'
              }}
            >
              Privacy Policy
            </button>
          </span>
        </label>
      </div>
    </div>
  )

  return (
    <div style={{
      minHeight: '100vh',
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
      background: 'linear-gradient(135deg, #0f0f0f 0%, #1a0a0a 50%, #0f0f0f 100%)',
      padding: '20px',
      position: 'relative',
      overflow: 'hidden'
    }}>
      {/* Animated background elements */}
      <div style={{
        position: 'absolute',
        top: '10%',
        left: '10%',
        width: '200px',
        height: '200px',
        background: 'radial-gradient(circle, rgba(239, 68, 68, 0.08) 0%, transparent 70%)',
        borderRadius: '50%',
        animation: 'float 8s ease-in-out infinite'
      }} />
      <div style={{
        position: 'absolute',
        bottom: '10%',
        right: '10%',
        width: '150px',
        height: '150px',
        background: 'radial-gradient(circle, rgba(239, 68, 68, 0.05) 0%, transparent 70%)',
        borderRadius: '50%',
        animation: 'float 6s ease-in-out infinite reverse'
      }} />

      <div style={{
        width: '100%',
        maxWidth: '550px',
        backgroundColor: '#1a1a1a',
        borderRadius: '24px',
        boxShadow: `
          20px 20px 60px #0a0a0a,
          -20px -20px 60px #2a2a2a,
          inset 2px 2px 10px rgba(239, 68, 68, 0.1),
          inset -2px -2px 10px rgba(0, 0, 0, 0.5)
        `,
        padding: '48px',
        position: 'relative',
        border: '1px solid rgba(239, 68, 68, 0.1)'
      }}>
        {/* Header */}
        <div style={{ textAlign: 'center', marginBottom: '40px' }}>
          <div style={{
            width: '100px',
            height: '100px',
            background: `
              linear-gradient(145deg, #2a2a2a, #0f0f0f),
              radial-gradient(circle at 30% 30%, rgba(239, 68, 68, 0.3), transparent)
            `,
            borderRadius: '50%',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            margin: '0 auto 24px',
            fontSize: '40px',
            boxShadow: `
              10px 10px 20px #0a0a0a,
              -10px -10px 20px #2a2a2a,
              inset 2px 2px 5px rgba(239, 68, 68, 0.1)
            `,
            border: '1px solid rgba(239, 68, 68, 0.2)'
          }}>
            📝
          </div>
          <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'center', gap: '12px', marginBottom: '12px' }}>
            <img
              src="/logo.svg"
              alt="CTNL WORK-BOARD"
              style={{ height: '40px', width: 'auto' }}
            />
          </div>
          <h1 style={{
            fontSize: '32px',
            fontWeight: 'bold',
            background: 'linear-gradient(135deg, #8B0000, #DC143C)',
            WebkitBackgroundClip: 'text',
            WebkitTextFillColor: 'transparent',
            margin: '0 0 12px 0',
            textShadow: '0 0 20px rgba(139, 0, 0, 0.5)',
            textAlign: 'center'
          }}>
            Join CTNL WORK-BOARD
          </h1>
          <p style={{
            color: '#9ca3af',
            fontSize: '16px',
            margin: 0,
            opacity: 0.8
          }}>
            Step {state.currentStep} of 3
          </p>
        </div>

        {/* Progress Bar */}
        <div style={{
          width: '100%',
          height: '8px',
          backgroundColor: '#0f0f0f',
          borderRadius: '4px',
          marginBottom: '40px',
          overflow: 'hidden',
          boxShadow: `
            inset 4px 4px 8px #0a0a0a,
            inset -4px -4px 8px #1a1a1a
          `,
          border: '1px solid rgba(239, 68, 68, 0.1)'
        }}>
          <div style={{
            width: `${(state.currentStep / 3) * 100}%`,
            height: '100%',
            background: 'linear-gradient(90deg, #ef4444, #dc2626)',
            transition: 'width 0.5s ease',
            boxShadow: '0 0 10px rgba(239, 68, 68, 0.5)',
            borderRadius: '4px'
          }} />
        </div>

        {/* Form Steps */}
        <form onSubmit={handleSubmit}>
          {state.currentStep === 1 && renderStep1()}
          {state.currentStep === 2 && renderStep2()}
          {state.currentStep === 3 && renderStep3()}

          {/* Enhanced Message Display */}
          {state.message && (
            <div style={{
              padding: '16px 20px',
              borderRadius: '12px',
              marginBottom: '24px',
              fontSize: '16px',
              fontWeight: '600',
              textAlign: 'center',
              position: 'relative',
              backgroundColor:
                state.messageType === 'success' ? 'rgba(34, 197, 94, 0.1)' :
                state.messageType === 'error' ? 'rgba(239, 68, 68, 0.1)' : 'rgba(59, 130, 246, 0.1)',
              color:
                state.messageType === 'success' ? '#22c55e' :
                state.messageType === 'error' ? '#ef4444' : '#3b82f6',
              border: `2px solid ${
                state.messageType === 'success' ? '#22c55e' :
                state.messageType === 'error' ? '#ef4444' : '#3b82f6'
              }`,
              boxShadow: `0 4px 12px ${
                state.messageType === 'success' ? 'rgba(34, 197, 94, 0.2)' :
                state.messageType === 'error' ? 'rgba(239, 68, 68, 0.2)' : 'rgba(59, 130, 246, 0.2)'
              }`,
              animation: state.messageType === 'error'
                ? 'fadeInSlide 0.3s ease-out, shake 0.5s ease-in-out 0.3s'
                : 'fadeInSlide 0.3s ease-out'
            }}>
              <div style={{
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                gap: '8px'
              }}>
                <span style={{ fontSize: '20px' }}>
                  {state.messageType === 'success' ? '✅' :
                   state.messageType === 'error' ? '❌' : 'ℹ️'}
                </span>
                {state.message}
              </div>

              {/* Close button for error messages */}
              {state.messageType === 'error' && (
                <button
                  type="button"
                  onClick={() => setState(prev => ({ ...prev, message: '' }))}
                  style={{
                    position: 'absolute',
                    top: '8px',
                    right: '8px',
                    background: 'none',
                    border: 'none',
                    color: '#ef4444',
                    fontSize: '18px',
                    cursor: 'pointer',
                    padding: '4px',
                    borderRadius: '4px',
                    lineHeight: 1
                  }}
                  onMouseEnter={(e) => {
                    e.currentTarget.style.backgroundColor = 'rgba(239, 68, 68, 0.1)'
                  }}
                  onMouseLeave={(e) => {
                    e.currentTarget.style.backgroundColor = 'transparent'
                  }}
                >
                  ×
                </button>
              )}
            </div>
          )}

          {/* Navigation Buttons */}
          <div style={{
            display: 'flex',
            justifyContent: 'space-between',
            gap: '16px',
            marginBottom: '24px'
          }}>
            {state.currentStep > 1 && (
              <button
                type="button"
                onClick={handlePrevious}
                disabled={state.isLoading}
                style={{
                  flex: 1,
                  padding: '12px',
                  backgroundColor: 'transparent',
                  color: '#667eea',
                  border: '2px solid #667eea',
                  borderRadius: '8px',
                  fontSize: '16px',
                  fontWeight: '600',
                  cursor: state.isLoading ? 'not-allowed' : 'pointer'
                }}
              >
                Previous
              </button>
            )}
            
            {state.currentStep < 3 ? (
              <button
                type="button"
                onClick={handleNext}
                disabled={state.isLoading}
                style={{
                  flex: 1,
                  padding: '12px',
                  backgroundColor: '#ff0000',
                  color: 'white',
                  border: 'none',
                  borderRadius: '8px',
                  fontSize: '16px',
                  fontWeight: '600',
                  cursor: state.isLoading ? 'not-allowed' : 'pointer',
                  boxShadow: '0 4px 8px rgba(255, 0, 0, 0.3)',
                  transition: 'all 0.3s ease'
                }}
                onMouseEnter={(e) => {
                  e.currentTarget.style.backgroundColor = '#cc0000'
                  e.currentTarget.style.boxShadow = '0 6px 12px rgba(255, 0, 0, 0.4)'
                }}
                onMouseLeave={(e) => {
                  e.currentTarget.style.backgroundColor = '#ff0000'
                  e.currentTarget.style.boxShadow = '0 4px 8px rgba(255, 0, 0, 0.3)'
                }}
              >
                Next
              </button>
            ) : (
              <button
                type="submit"
                disabled={state.isLoading}
                style={{
                  flex: 1,
                  padding: '14px',
                  backgroundColor: state.isLoading ? '#9ca3af' : '#ff0000',
                  color: 'white',
                  border: 'none',
                  borderRadius: '8px',
                  fontSize: '16px',
                  fontWeight: '600',
                  cursor: state.isLoading ? 'not-allowed' : 'pointer',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  gap: '8px',
                  boxShadow: '0 4px 8px rgba(255, 0, 0, 0.3)',
                  transition: 'all 0.3s ease'
                }}
                onMouseEnter={(e) => {
                  if (!state.isLoading) {
                    e.currentTarget.style.backgroundColor = '#cc0000'
                    e.currentTarget.style.boxShadow = '0 6px 12px rgba(255, 0, 0, 0.4)'
                  }
                }}
                onMouseLeave={(e) => {
                  if (!state.isLoading) {
                    e.currentTarget.style.backgroundColor = '#ff0000'
                    e.currentTarget.style.boxShadow = '0 4px 8px rgba(255, 0, 0, 0.3)'
                  }
                }}
              >
                {state.isLoading && (
                  <div style={{
                    width: '20px',
                    height: '20px',
                    border: '2px solid transparent',
                    borderTop: '2px solid white',
                    borderRadius: '50%',
                    animation: 'spin 1s linear infinite'
                  }} />
                )}
                {state.isLoading ? 'Creating Account...' : 'Create Account'}
              </button>
            )}
          </div>
        </form>

        {/* Navigation Link */}
        <div style={{ textAlign: 'center' }}>
          <p style={{ color: '#6b7280', fontSize: '14px', margin: 0 }}>
            Already have an account?{' '}
            <button
              type="button"
              onClick={onNavigateToLogin}
              style={{
                background: 'none',
                border: 'none',
                color: '#667eea',
                fontSize: '14px',
                cursor: 'pointer',
                textDecoration: 'underline',
                fontWeight: '500'
              }}
            >
              Sign in here
            </button>
          </p>
        </div>
      </div>

      {/* CSS Animations */}
      <style>{`
        @keyframes spin {
          0% { transform: rotate(0deg); }
          100% { transform: rotate(360deg); }
        }

        @keyframes float {
          0%, 100% {
            transform: translateY(0px) rotate(0deg);
            opacity: 0.7;
          }
          50% {
            transform: translateY(-20px) rotate(180deg);
            opacity: 1;
          }
        }
      `}</style>
    </div>
  )
}
