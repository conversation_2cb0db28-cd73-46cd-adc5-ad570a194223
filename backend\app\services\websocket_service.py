"""
WebSocket Service for Real-time Communication
"""
import asyncio
import logging
from typing import Dict, Any, List, Optional
from datetime import datetime
import socketio
from motor.motor_asyncio import AsyncIOMotorDatabase
from bson import ObjectId
import json

from ..core.database import get_database
from ..core.security import verify_token
from ..core.config import Collections

logger = logging.getLogger(__name__)

class WebSocketService:
    def __init__(self):
        self.sio = socketio.AsyncServer(
            cors_allowed_origins=["http://localhost:3000", "http://localhost:3001"],
            logger=True,
            engineio_logger=True
        )
        self.connected_users: Dict[str, Dict[str, Any]] = {}
        self.user_rooms: Dict[str, List[str]] = {}
        
    async def authenticate_user(self, auth_data: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """Authenticate user from WebSocket connection"""
        try:
            user_id = auth_data.get('userId')
            role = auth_data.get('role')
            department = auth_data.get('department')
            
            if not user_id:
                return None
                
            # Get database connection
            db = await get_database()
            
            # Verify user exists in database
            user = await db[Collections.USERS].find_one({"_id": ObjectId(user_id)})
            if not user:
                return None
                
            return {
                'user_id': user_id,
                'role': role,
                'department': department,
                'username': user.get('username'),
                'email': user.get('email')
            }
        except Exception as e:
            logger.error(f"WebSocket authentication error: {e}")
            return None
    
    async def handle_connect(self, sid: str, environ: Dict[str, Any], auth: Dict[str, Any]):
        """Handle client connection"""
        try:
            # Authenticate user
            user_data = await self.authenticate_user(auth)
            if not user_data:
                logger.warning(f"WebSocket authentication failed for session {sid}")
                return False
            
            # Store user connection info
            self.connected_users[sid] = user_data
            user_id = user_data['user_id']
            
            # Initialize user rooms list
            if user_id not in self.user_rooms:
                self.user_rooms[user_id] = []
            
            logger.info(f"User {user_data['username']} connected with session {sid}")
            
            # Update user online status
            db = await get_database()
            await db[Collections.USERS].update_one(
                {"_id": ObjectId(user_id)},
                {
                    "$set": {
                        "is_online": True,
                        "activity.last_active": datetime.utcnow(),
                        "activity.socket_session_id": sid
                    }
                }
            )
            
            # Join user to their personal room
            await self.sio.enter_room(sid, f"user_{user_id}")
            
            # Join user to their department room
            if user_data.get('department'):
                await self.sio.enter_room(sid, f"department_{user_data['department']}")
            
            # Emit connection success
            await self.sio.emit('connection_established', {
                'message': 'Connected successfully',
                'user_id': user_id,
                'timestamp': datetime.utcnow().isoformat()
            }, room=sid)
            
            return True
            
        except Exception as e:
            logger.error(f"WebSocket connection error: {e}")
            return False
    
    async def handle_disconnect(self, sid: str):
        """Handle client disconnection"""
        try:
            if sid in self.connected_users:
                user_data = self.connected_users[sid]
                user_id = user_data['user_id']
                
                logger.info(f"User {user_data['username']} disconnected from session {sid}")
                
                # Update user offline status
                db = await get_database()
                await db[Collections.USERS].update_one(
                    {"_id": ObjectId(user_id)},
                    {
                        "$set": {
                            "is_online": False,
                            "activity.last_active": datetime.utcnow()
                        },
                        "$unset": {
                            "activity.socket_session_id": ""
                        }
                    }
                )
                
                # Clean up user data
                del self.connected_users[sid]
                if user_id in self.user_rooms:
                    del self.user_rooms[user_id]
                    
        except Exception as e:
            logger.error(f"WebSocket disconnection error: {e}")
    
    async def handle_join_room(self, sid: str, data: Dict[str, Any]):
        """Handle room join requests"""
        try:
            if sid not in self.connected_users:
                return
                
            room = data.get('room')
            if not room:
                return
                
            user_data = self.connected_users[sid]
            user_id = user_data['user_id']
            
            # Add room to user's room list
            if user_id not in self.user_rooms:
                self.user_rooms[user_id] = []
            
            if room not in self.user_rooms[user_id]:
                self.user_rooms[user_id].append(room)
            
            # Join the room
            await self.sio.enter_room(sid, room)
            
            # Confirm room join
            await self.sio.emit('room_joined', {
                'room': room,
                'message': f'Joined room {room}',
                'timestamp': datetime.utcnow().isoformat()
            }, room=sid)
            
            logger.info(f"User {user_data['username']} joined room {room}")
            
        except Exception as e:
            logger.error(f"Room join error: {e}")
    
    async def handle_leave_room(self, sid: str, data: Dict[str, Any]):
        """Handle room leave requests"""
        try:
            if sid not in self.connected_users:
                return
                
            room = data.get('room')
            if not room:
                return
                
            user_data = self.connected_users[sid]
            user_id = user_data['user_id']
            
            # Remove room from user's room list
            if user_id in self.user_rooms and room in self.user_rooms[user_id]:
                self.user_rooms[user_id].remove(room)
            
            # Leave the room
            await self.sio.leave_room(sid, room)
            
            # Confirm room leave
            await self.sio.emit('room_left', {
                'room': room,
                'message': f'Left room {room}',
                'timestamp': datetime.utcnow().isoformat()
            }, room=sid)
            
            logger.info(f"User {user_data['username']} left room {room}")
            
        except Exception as e:
            logger.error(f"Room leave error: {e}")
    
    async def broadcast_task_update(self, task_data: Dict[str, Any]):
        """Broadcast task updates to relevant users"""
        try:
            # Broadcast to project room if task has project_id
            if task_data.get('project_id'):
                room = f"project_{task_data['project_id']}"
                await self.sio.emit('task_update', task_data, room=room)
            
            # Broadcast to assignee
            if task_data.get('assigned_to'):
                user_room = f"user_{task_data['assigned_to']}"
                await self.sio.emit('task_update', task_data, room=user_room)
            
            # Broadcast to creator
            if task_data.get('created_by'):
                user_room = f"user_{task_data['created_by']}"
                await self.sio.emit('task_update', task_data, room=user_room)
                
        except Exception as e:
            logger.error(f"Task broadcast error: {e}")
    
    async def broadcast_project_update(self, project_data: Dict[str, Any]):
        """Broadcast project updates to relevant users"""
        try:
            project_id = project_data.get('_id') or project_data.get('id')
            if project_id:
                room = f"project_{project_id}"
                await self.sio.emit('project_update', project_data, room=room)
                
        except Exception as e:
            logger.error(f"Project broadcast error: {e}")
    
    async def broadcast_approval_update(self, approval_data: Dict[str, Any]):
        """Broadcast approval status updates"""
        try:
            # Broadcast to requester
            if approval_data.get('requested_by'):
                user_room = f"user_{approval_data['requested_by']}"
                await self.sio.emit('approval_status_update', approval_data, room=user_room)
            
            # Broadcast to approver
            if approval_data.get('approver_id'):
                user_room = f"user_{approval_data['approver_id']}"
                await self.sio.emit('approval_status_update', approval_data, room=user_room)
                
        except Exception as e:
            logger.error(f"Approval broadcast error: {e}")
    
    async def broadcast_notification(self, notification_data: Dict[str, Any], target_users: List[str] = None):
        """Broadcast notifications to specific users or all connected users"""
        try:
            if target_users:
                # Send to specific users
                for user_id in target_users:
                    user_room = f"user_{user_id}"
                    await self.sio.emit('notification', notification_data, room=user_room)
            else:
                # Broadcast to all connected users
                await self.sio.emit('notification', notification_data)
                
        except Exception as e:
            logger.error(f"Notification broadcast error: {e}")

# Global WebSocket service instance
websocket_service = WebSocketService()

# Helper functions for broadcasting from API endpoints
async def broadcast_task_update(task_data: Dict[str, Any]):
    """Helper function to broadcast task updates"""
    await websocket_service.broadcast_task_update(task_data)

async def broadcast_project_update(project_data: Dict[str, Any]):
    """Helper function to broadcast project updates"""
    await websocket_service.broadcast_project_update(project_data)

async def broadcast_approval_update(approval_data: Dict[str, Any]):
    """Helper function to broadcast approval updates"""
    await websocket_service.broadcast_approval_update(approval_data)

async def broadcast_notification(notification_data: Dict[str, Any], target_users: List[str] = None):
    """Helper function to broadcast notifications"""
    await websocket_service.broadcast_notification(notification_data, target_users)
