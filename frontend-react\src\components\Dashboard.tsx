/**
 * Dashboard Component with comprehensive error handling and logging
 */
import React, { useState, useEffect, useCallback } from 'react'
import { logger, <PERSON><PERSON>r<PERSON><PERSON><PERSON>, PerformanceMonitor } from '../utils/logger'
import type { DashboardStats, Task, User, LoadingState } from '../types'

interface DashboardProps {
  user?: User
  onLogout?: () => void
}

export default function Dashboard({ user, onLogout }: DashboardProps) {
  const [stats, setStats] = useState<DashboardStats | null>(null)
  const [recentTasks, setRecentTasks] = useState<Task[]>([])
  const [loadingState, setLoadingState] = useState<LoadingState>({
    isLoading: true,
    error: null
  })

  useEffect(() => {
    logger.componentMount('Dashboard')
    loadDashboardData()

    return () => {
      logger.componentUnmount('Dashboard')
    }
  }, [])

  const loadDashboardData = useCallback(async () => {
    try {
      setLoadingState({ isLoading: true, error: null })
      logger.info('Loading dashboard data', 'Dashboard')

      PerformanceMonitor.startTimer('dashboard_load')

      // Load dashboard stats
      const statsResponse = await fetch('http://localhost:8002/api/dashboard/stats', {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('authToken')}`,
          'Content-Type': 'application/json'
        }
      })

      if (statsResponse.ok) {
        const statsData: DashboardStats = await statsResponse.json()
        setStats(statsData)
        logger.debug('Dashboard stats loaded', 'Dashboard', statsData)
      } else {
        throw new Error(`Failed to load stats: ${statsResponse.status}`)
      }

      // Load recent tasks
      const tasksResponse = await fetch('http://localhost:8002/api/tasks?limit=5', {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('authToken')}`,
          'Content-Type': 'application/json'
        }
      })

      if (tasksResponse.ok) {
        const tasksData = await tasksResponse.json()
        setRecentTasks(tasksData.tasks || [])
        logger.debug('Recent tasks loaded', 'Dashboard', { count: tasksData.tasks?.length || 0 })
      } else {
        logger.warn('Failed to load recent tasks', 'Dashboard', { status: tasksResponse.status })
      }

      const duration = PerformanceMonitor.endTimer('dashboard_load')
      logger.info(`Dashboard data loaded in ${duration.toFixed(2)}ms`, 'Dashboard')

      setLoadingState({ isLoading: false, error: null })

    } catch (error) {
      const handledError = ErrorHandler.handleApiError(error, 'Dashboard')
      setLoadingState({
        isLoading: false,
        error: handledError.message
      })
      logger.error('Failed to load dashboard data', 'Dashboard', error)
    }
  }, [])

  const handleRefresh = () => {
    logger.info('Dashboard refresh requested', 'Dashboard')
    loadDashboardData()
  }

  const handleLogout = () => {
    logger.info('Logout requested from dashboard', 'Dashboard')
    localStorage.removeItem('authToken')
    localStorage.removeItem('user')
    if (onLogout) {
      onLogout()
    }
  }

  if (loadingState.isLoading) {
    return (
      <div style={{
        minHeight: '100vh',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        backgroundColor: '#f8f9fa'
      }}>
        <div style={{ textAlign: 'center' }}>
          <div style={{
            fontSize: '24px',
            marginBottom: '16px'
          }}>
            ⏳
          </div>
          <p>Loading dashboard...</p>
        </div>
      </div>
    )
  }

  if (loadingState.error) {
    return (
      <div style={{
        minHeight: '100vh',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        backgroundColor: '#f8f9fa',
        padding: '20px'
      }}>
        <div style={{
          maxWidth: '500px',
          width: '100%',
          backgroundColor: 'white',
          padding: '40px',
          borderRadius: '8px',
          boxShadow: '0 2px 10px rgba(0,0,0,0.1)',
          textAlign: 'center'
        }}>
          <div style={{ fontSize: '48px', marginBottom: '20px' }}>⚠️</div>
          <h2 style={{ color: '#dc3545', marginBottom: '16px' }}>
            Failed to Load Dashboard
          </h2>
          <p style={{ color: '#6c757d', marginBottom: '24px' }}>
            {loadingState.error}
          </p>
          <button
            onClick={handleRefresh}
            style={{
              padding: '12px 24px',
              backgroundColor: '#007bff',
              color: 'white',
              border: 'none',
              borderRadius: '4px',
              cursor: 'pointer',
              marginRight: '12px'
            }}
          >
            Try Again
          </button>
          <button
            onClick={handleLogout}
            style={{
              padding: '12px 24px',
              backgroundColor: '#6c757d',
              color: 'white',
              border: 'none',
              borderRadius: '4px',
              cursor: 'pointer'
            }}
          >
            Logout
          </button>
        </div>
      </div>
    )
  }

  return (
    <div style={{ minHeight: '100vh', backgroundColor: '#f8f9fa' }}>
      {/* Header */}
      <header style={{
        backgroundColor: 'white',
        boxShadow: '0 1px 3px rgba(0,0,0,0.1)',
        padding: '16px 0'
      }}>
        <div style={{
          maxWidth: '1200px',
          margin: '0 auto',
          padding: '0 20px',
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center'
        }}>
          <div>
            <h1 style={{ margin: 0, color: '#333', fontSize: '24px' }}>
              📊 Dashboard
            </h1>
            {user && (
              <p style={{ margin: '4px 0 0 0', color: '#6c757d', fontSize: '14px' }}>
                Welcome back, {user.username}! ({user.role})
              </p>
            )}
          </div>
          <div style={{ display: 'flex', gap: '12px' }}>
            <button
              onClick={handleRefresh}
              style={{
                padding: '8px 16px',
                backgroundColor: '#28a745',
                color: 'white',
                border: 'none',
                borderRadius: '4px',
                cursor: 'pointer',
                fontSize: '14px'
              }}
            >
              🔄 Refresh
            </button>
            <button
              onClick={handleLogout}
              style={{
                padding: '8px 16px',
                backgroundColor: '#dc3545',
                color: 'white',
                border: 'none',
                borderRadius: '4px',
                cursor: 'pointer',
                fontSize: '14px'
              }}
            >
              🚪 Logout
            </button>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main style={{ maxWidth: '1200px', margin: '0 auto', padding: '24px 20px' }}>
        {/* Stats Cards */}
        {stats && (
          <div style={{
            display: 'grid',
            gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))',
            gap: '20px',
            marginBottom: '32px'
          }}>
            <div style={{
              backgroundColor: 'white',
              padding: '24px',
              borderRadius: '8px',
              boxShadow: '0 2px 4px rgba(0,0,0,0.1)'
            }}>
              <div style={{ display: 'flex', alignItems: 'center', marginBottom: '12px' }}>
                <div style={{
                  width: '40px',
                  height: '40px',
                  backgroundColor: '#007bff',
                  borderRadius: '8px',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  marginRight: '12px'
                }}>
                  <span style={{ color: 'white', fontSize: '18px' }}>📋</span>
                </div>
                <div>
                  <h3 style={{ margin: 0, fontSize: '14px', color: '#6c757d' }}>
                    Total Tasks
                  </h3>
                  <p style={{ margin: 0, fontSize: '24px', fontWeight: 'bold', color: '#333' }}>
                    {stats.total_tasks}
                  </p>
                </div>
              </div>
            </div>

            <div style={{
              backgroundColor: 'white',
              padding: '24px',
              borderRadius: '8px',
              boxShadow: '0 2px 4px rgba(0,0,0,0.1)'
            }}>
              <div style={{ display: 'flex', alignItems: 'center', marginBottom: '12px' }}>
                <div style={{
                  width: '40px',
                  height: '40px',
                  backgroundColor: '#28a745',
                  borderRadius: '8px',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  marginRight: '12px'
                }}>
                  <span style={{ color: 'white', fontSize: '18px' }}>✅</span>
                </div>
                <div>
                  <h3 style={{ margin: 0, fontSize: '14px', color: '#6c757d' }}>
                    Completed
                  </h3>
                  <p style={{ margin: 0, fontSize: '24px', fontWeight: 'bold', color: '#333' }}>
                    {stats.completed_tasks}
                  </p>
                </div>
              </div>
            </div>

            <div style={{
              backgroundColor: 'white',
              padding: '24px',
              borderRadius: '8px',
              boxShadow: '0 2px 4px rgba(0,0,0,0.1)'
            }}>
              <div style={{ display: 'flex', alignItems: 'center', marginBottom: '12px' }}>
                <div style={{
                  width: '40px',
                  height: '40px',
                  backgroundColor: '#ffc107',
                  borderRadius: '8px',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  marginRight: '12px'
                }}>
                  <span style={{ color: 'white', fontSize: '18px' }}>⏳</span>
                </div>
                <div>
                  <h3 style={{ margin: 0, fontSize: '14px', color: '#6c757d' }}>
                    Pending
                  </h3>
                  <p style={{ margin: 0, fontSize: '24px', fontWeight: 'bold', color: '#333' }}>
                    {stats.pending_tasks}
                  </p>
                </div>
              </div>
            </div>

            <div style={{
              backgroundColor: 'white',
              padding: '24px',
              borderRadius: '8px',
              boxShadow: '0 2px 4px rgba(0,0,0,0.1)'
            }}>
              <div style={{ display: 'flex', alignItems: 'center', marginBottom: '12px' }}>
                <div style={{
                  width: '40px',
                  height: '40px',
                  backgroundColor: '#6f42c1',
                  borderRadius: '8px',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  marginRight: '12px'
                }}>
                  <span style={{ color: 'white', fontSize: '18px' }}>📁</span>
                </div>
                <div>
                  <h3 style={{ margin: 0, fontSize: '14px', color: '#6c757d' }}>
                    Projects
                  </h3>
                  <p style={{ margin: 0, fontSize: '24px', fontWeight: 'bold', color: '#333' }}>
                    {stats.total_projects}
                  </p>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Recent Tasks */}
        <div style={{
          backgroundColor: 'white',
          borderRadius: '8px',
          boxShadow: '0 2px 4px rgba(0,0,0,0.1)',
          padding: '24px'
        }}>
          <h2 style={{ margin: '0 0 20px 0', fontSize: '18px', color: '#333' }}>
            📋 Recent Tasks
          </h2>
          {recentTasks.length > 0 ? (
            <div style={{ display: 'flex', flexDirection: 'column', gap: '12px' }}>
              {recentTasks.map((task) => (
                <div
                  key={task.id}
                  style={{
                    padding: '16px',
                    backgroundColor: '#f8f9fa',
                    borderRadius: '6px',
                    display: 'flex',
                    justifyContent: 'space-between',
                    alignItems: 'center'
                  }}
                >
                  <div>
                    <h4 style={{ margin: '0 0 4px 0', fontSize: '14px', color: '#333' }}>
                      {task.title}
                    </h4>
                    <p style={{ margin: 0, fontSize: '12px', color: '#6c757d' }}>
                      Status: {task.status} • Priority: {task.priority}
                    </p>
                  </div>
                  <span style={{
                    padding: '4px 8px',
                    borderRadius: '12px',
                    fontSize: '12px',
                    fontWeight: '500',
                    backgroundColor:
                      task.priority === 'high' ? '#dc3545' :
                      task.priority === 'medium' ? '#ffc107' : '#28a745',
                    color: 'white'
                  }}>
                    {task.priority}
                  </span>
                </div>
              ))}
            </div>
          ) : (
            <p style={{ color: '#6c757d', fontStyle: 'italic' }}>
              No recent tasks found
            </p>
          )}
        </div>
      </main>
    </div>
  )
}
