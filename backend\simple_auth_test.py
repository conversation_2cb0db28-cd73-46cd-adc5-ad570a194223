#!/usr/bin/env python3
"""
Simple authentication test endpoint
"""

import asyncio
import sys
import os
from fastapi import FastAP<PERSON>, HTTPException
from pydantic import BaseModel
from passlib.context import CryptContext
import uvicorn

# Add the app directory to the path
sys.path.append(os.path.join(os.path.dirname(__file__), 'app'))

from app.core.database_simple import connect_to_mongo, mongodb, close_mongo_connection

# Password hashing
pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")

app = FastAPI(title="Simple Auth Test")

class LoginRequest(BaseModel):
    username: str
    password: str

class LoginResponse(BaseModel):
    success: bool
    message: str
    user_id: str = None
    username: str = None
    role: str = None

@app.on_event("startup")
async def startup():
    """Connect to database on startup"""
    await connect_to_mongo()
    print("✅ Simple auth server started")

@app.on_event("shutdown") 
async def shutdown():
    """Close database connection on shutdown"""
    await close_mongo_connection()
    print("✅ Simple auth server stopped")

@app.post("/test-login", response_model=LoginResponse)
async def test_login(login_data: LoginRequest):
    """Simple login test endpoint"""
    try:
        if mongodb.database is None:
            return LoginResponse(
                success=False,
                message="Database not connected"
            )
        
        # Find user
        users_collection = mongodb.database.users
        user = await users_collection.find_one({
            "$or": [
                {"username": login_data.username},
                {"email": login_data.username}
            ]
        })
        
        if not user:
            return LoginResponse(
                success=False,
                message="User not found"
            )
        
        # Verify password
        if not pwd_context.verify(login_data.password, user["hashed_password"]):
            return LoginResponse(
                success=False,
                message="Invalid password"
            )
        
        return LoginResponse(
            success=True,
            message="Login successful",
            user_id=str(user["_id"]),
            username=user["username"],
            role=user["role"]
        )
        
    except Exception as e:
        print(f"Login error: {e}")
        return LoginResponse(
            success=False,
            message=f"Login failed: {str(e)}"
        )

@app.get("/health")
async def health():
    """Health check"""
    return {
        "status": "healthy",
        "database": "connected" if mongodb.database else "disconnected"
    }

if __name__ == "__main__":
    print("🚀 Starting Simple Auth Test Server...")
    print("📍 Server will run on: http://localhost:8001")
    print("🧪 Test endpoint: POST http://localhost:8001/test-login")
    uvicorn.run(app, host="0.0.0.0", port=8001)
