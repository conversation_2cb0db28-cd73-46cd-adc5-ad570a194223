import React, { useState, useEffect } from 'react'

interface User {
  id: string
  email: string
  name: string
  role: string
}

interface ClockInPageProps {
  user: User | null
  onNavigateToLogin: () => void
  onNavigateToDashboard: () => void
}

interface ClockInState {
  currentTime: Date
  isLoading: boolean
  message: string
  messageType: 'info' | 'success' | 'error'
  workStatus: 'clocked-out' | 'clocked-in' | 'on-break'
  totalWorkedTime: number
}

export default function ClockInPage({ 
  user, 
  onNavigateToLogin, 
  onNavigateToDashboard 
}: ClockInPageProps) {
  const [state, setState] = useState<ClockInState>({
    currentTime: new Date(),
    isLoading: false,
    message: '',
    messageType: 'info',
    workStatus: 'clocked-out',
    totalWorkedTime: 0
  })

  useEffect(() => {
    console.log('ClockInPage mounted')

    // Update time every second
    const timeInterval = setInterval(() => {
      setState(prev => ({ ...prev, currentTime: new Date() }))
    }, 1000)

    return () => {
      clearInterval(timeInterval)
      console.log('ClockInPage unmounted')
    }
  }, [])

  const handleClockAction = () => {
    if (state.isLoading) return

    setState(prev => ({ ...prev, isLoading: true }))
    
    // Simulate API call
    setTimeout(() => {
      if (state.workStatus === 'clocked-out') {
        setState(prev => ({ 
          ...prev, 
          workStatus: 'clocked-in',
          isLoading: false,
          message: 'Successfully clocked in!',
          messageType: 'success'
        }))
      } else {
        setState(prev => ({ 
          ...prev, 
          workStatus: 'clocked-out',
          isLoading: false,
          message: 'Successfully clocked out!',
          messageType: 'success'
        }))
      }
      
      // Clear message after 3 seconds and navigate to login
      setTimeout(() => {
        setState(prev => ({ ...prev, message: '' }))
        // Navigate to login page after successful clock action
        onNavigateToLogin()
      }, 3000)
    }, 1500)
  }

  const formatTime = (date: Date): string => {
    return date.toLocaleTimeString('en-US', {
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit',
      hour12: true
    })
  }

  const formatDate = (date: Date): string => {
    return date.toLocaleDateString('en-US', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    })
  }

  if (!user) {
    return (
      <div style={{
        minHeight: '100vh',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        background: 'linear-gradient(135deg, #000000 0%, #1a0000 50%, #000000 100%)'
      }}>
        <div style={{
          background: 'rgba(255, 255, 255, 0.1)',
          padding: '40px',
          borderRadius: '20px',
          textAlign: 'center',
          color: 'white'
        }}>
          <h2>Authentication Required</h2>
          <p>Please log in to access the time tracking system.</p>
          <button
            onClick={onNavigateToLogin}
            style={{
              width: '100%',
              padding: '12px',
              backgroundColor: '#ff0000',
              color: 'white',
              border: 'none',
              borderRadius: '8px',
              fontSize: '16px',
              fontWeight: '600',
              cursor: 'pointer'
            }}
          >
            Go to Login
          </button>
        </div>
      </div>
    )
  }

  return (
    <>
      {/* CSS ANIMATIONS */}
      <style>{`
        @keyframes spin {
          0% { transform: rotate(0deg); }
          100% { transform: rotate(360deg); }
        }
        
        @keyframes float {
          0%, 100% { 
            transform: translateY(0px) rotate(0deg);
            opacity: 0.7;
          }
          50% { 
            transform: translateY(-30px) rotate(180deg);
            opacity: 1;
          }
        }
        
        @keyframes pulse {
          0% { 
            opacity: 0.3;
            transform: translate(-50%, -50%) scale(0.8);
          }
          100% { 
            opacity: 0.1;
            transform: translate(-50%, -50%) scale(1.2);
          }
        }
        
        /* 3D Button Hover Effects */
        .clock-button:hover:not(:disabled) {
          transform: scale(1.05) translateY(-3px) !important;
          box-shadow: 
            0 0 0 8px rgba(200, 0, 0, 0.15),
            0 0 0 16px rgba(200, 0, 0, 0.08),
            0 0 0 24px rgba(200, 0, 0, 0.04),
            30px 30px 60px rgba(0, 0, 0, 0.95),
            -30px -30px 60px rgba(100, 100, 100, 0.4),
            inset 10px 10px 25px rgba(255, 255, 255, 0.3),
            inset -10px -10px 25px rgba(0, 0, 0, 0.6),
            0 0 120px rgba(200, 0, 0, 0.6),
            0 0 180px rgba(200, 0, 0, 0.3),
            0 0 240px rgba(200, 0, 0, 0.2) !important;
          filter: brightness(1.2) saturate(1.2) contrast(1.2) !important;
        }
        
        .clock-button:active:not(:disabled) {
          transform: scale(0.92) translateY(15px) !important;
          box-shadow: 
            inset 25px 25px 50px rgba(0, 0, 0, 0.95),
            inset -25px -25px 50px rgba(80, 80, 80, 0.4),
            0 0 0 6px rgba(150, 0, 0, 0.08),
            0 0 50px rgba(150, 0, 0, 0.3) !important;
          filter: brightness(0.8) saturate(1.0) contrast(1.0) !important;
        }
      `}</style>

      <div style={{
        minHeight: '100vh',
        width: '100vw',
        background: 'linear-gradient(135deg, #000000 0%, #1a0000 30%, #000000 70%, #0a0000 100%)',
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'center',
        justifyContent: 'center',
        padding: '20px',
        position: 'relative',
        overflow: 'hidden'
      }}>
        {/* FLOATING BACKGROUND ELEMENTS */}
        <div style={{
          position: 'absolute',
          top: '10%',
          left: '10%',
          width: '200px',
          height: '200px',
          borderRadius: '50%',
          background: 'radial-gradient(circle, rgba(255, 0, 0, 0.3) 0%, rgba(255, 50, 50, 0.1) 50%, transparent 100%)',
          animation: 'float 6s ease-in-out infinite',
          zIndex: 1,
          filter: 'blur(2px)'
        }} />
        
        <div style={{
          position: 'absolute',
          top: '20%',
          right: '15%',
          width: '150px',
          height: '150px',
          borderRadius: '50%',
          background: 'radial-gradient(circle, rgba(255, 20, 20, 0.25) 0%, rgba(255, 60, 60, 0.08) 50%, transparent 100%)',
          animation: 'float 8s ease-in-out infinite reverse',
          zIndex: 1,
          filter: 'blur(1px)'
        }} />

        {/* MAIN CONTENT CONTAINER */}
        <div style={{
          position: 'relative',
          zIndex: 10,
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center',
          gap: '40px'
        }}>
          {/* CT COMMUNICATION TOWERS LOGO */}
          <div style={{
            textAlign: 'center',
            marginBottom: '30px'
          }}>
            <img
              src="/logo.svg"
              alt="CTNL WORK-BOARD"
              style={{
                height: '60px',
                width: 'auto',
                filter: 'brightness(0) invert(1)',
                opacity: 0.9
              }}
            />
            <h1 style={{
              color: '#8B0000',
              fontSize: '24px',
              fontWeight: 'bold',
              margin: '10px 0 0 0',
              textShadow: '0 0 20px rgba(139, 0, 0, 0.8)',
              letterSpacing: '1px'
            }}>
              CTNL WORK-BOARD
            </h1>
            <p style={{
              color: '#DC143C',
              fontSize: '14px',
              margin: '5px 0 0 0',
              opacity: 0.9,
              textShadow: '0 0 10px rgba(220, 20, 60, 0.5)'
            }}>
              AI-Powered Work Management System
            </p>
          </div>

          {/* TIME AND DATE DISPLAY */}
          <div style={{
            textAlign: 'center',
            color: '#ffffff',
            marginBottom: '20px'
          }}>
            <div style={{
              fontSize: '48px',
              fontWeight: '900',
              marginBottom: '10px',
              textShadow: '0 0 20px rgba(255, 0, 0, 0.5)',
              letterSpacing: '2px'
            }}>
              {formatTime(state.currentTime)}
            </div>
            <div style={{
              fontSize: '18px',
              fontWeight: '400',
              opacity: 0.8,
              textShadow: '0 0 10px rgba(255, 0, 0, 0.3)'
            }}>
              {formatDate(state.currentTime)}
            </div>
          </div>

          {/* SPECTACULAR 3D CLOCK-IN BUTTON */}
          <div style={{
            position: 'relative',
            zIndex: 20,
            perspective: '1000px'
          }}>
            <button
              className="clock-button"
              onClick={handleClockAction}
              disabled={state.isLoading}
              style={{
                width: '300px',
                height: '300px',
                borderRadius: '50%',
                background: state.isLoading
                  ? 'radial-gradient(circle at 30% 30%, #333333, #1a1a1a)'
                  : `
                    radial-gradient(circle at 30% 30%, #cc0000, #990000, #660000, #440000, #220000),
                    linear-gradient(145deg, #dd3333, #bb0000, #880000, #550000),
                    conic-gradient(from 0deg, #bb0000, #cc2222, #aa0000, #770000, #bb0000),
                    radial-gradient(circle at center, rgba(0, 0, 0, 0.4) 0%, rgba(0, 0, 0, 0.2) 50%, transparent 100%)
                  `,
                color: '#ffffff',
                border: state.isLoading
                  ? '6px solid #444444'
                  : '6px solid rgba(255, 255, 255, 0.4)',
                cursor: state.isLoading ? 'not-allowed' : 'pointer',
                fontSize: '24px',
                fontWeight: '900',
                transition: 'all 0.15s cubic-bezier(0.4, 0, 0.2, 1)',
                display: 'flex',
                flexDirection: 'column',
                alignItems: 'center',
                justifyContent: 'center',
                gap: '15px',
                boxShadow: state.isLoading
                  ? `
                    inset 20px 20px 40px rgba(0, 0, 0, 0.9),
                    inset -20px -20px 40px rgba(60, 60, 60, 0.3),
                    0 0 0 8px rgba(255, 0, 0, 0.05)
                  `
                  : `
                    0 0 0 8px rgba(255, 0, 0, 0.15),
                    0 0 0 16px rgba(255, 0, 0, 0.08),
                    0 0 0 24px rgba(255, 0, 0, 0.04),
                    25px 25px 50px rgba(0, 0, 0, 0.9),
                    -25px -25px 50px rgba(80, 80, 80, 0.3),
                    inset 8px 8px 20px rgba(255, 255, 255, 0.25),
                    inset -8px -8px 20px rgba(0, 0, 0, 0.5),
                    0 0 100px rgba(255, 0, 0, 0.7),
                    0 0 150px rgba(255, 0, 0, 0.4),
                    0 0 200px rgba(255, 0, 0, 0.2)
                  `,
                textShadow: state.isLoading
                  ? '0 0 8px rgba(255, 255, 255, 0.2)'
                  : '0 0 30px rgba(255, 255, 255, 0.9), 0 0 60px rgba(255, 255, 255, 0.5), 0 0 90px rgba(255, 255, 255, 0.3)',
                position: 'relative',
                overflow: 'visible',
                transform: state.isLoading ? 'scale(0.92) translateY(12px)' : 'scale(1) translateY(0px)',
                filter: state.isLoading
                  ? 'brightness(0.5) saturate(0.2) contrast(0.8)'
                  : 'brightness(1.3) saturate(1.4) contrast(1.2)',
                outline: 'none',
                userSelect: 'none'
              }}
            >
              {/* SPINNING RINGS */}
              <div style={{
                position: 'absolute',
                top: '10px',
                left: '10px',
                right: '10px',
                bottom: '10px',
                borderRadius: '50%',
                border: '4px solid transparent',
                borderTop: '4px solid rgba(255, 255, 255, 0.9)',
                borderRight: '4px solid rgba(255, 255, 255, 0.5)',
                animation: 'spin 2s linear infinite',
                filter: 'drop-shadow(0 0 10px rgba(255, 255, 255, 0.7))'
              }} />
              
              <div style={{
                position: 'absolute',
                top: '25px',
                left: '25px',
                right: '25px',
                bottom: '25px',
                borderRadius: '50%',
                border: '3px solid transparent',
                borderBottom: '3px solid rgba(255, 255, 255, 0.8)',
                borderLeft: '3px solid rgba(255, 255, 255, 0.4)',
                animation: 'spin 1.5s linear infinite reverse',
                filter: 'drop-shadow(0 0 8px rgba(255, 255, 255, 0.6))'
              }} />

              {/* BUTTON TEXT */}
              <div style={{
                fontSize: '20px',
                fontWeight: '900',
                letterSpacing: '4px',
                zIndex: 10,
                position: 'relative',
                textTransform: 'uppercase',
                textAlign: 'center',
                lineHeight: '1.2',
                filter: 'drop-shadow(0 0 25px rgba(255, 255, 255, 0.9))',
                textShadow: `
                  0 0 20px rgba(255, 255, 255, 0.8),
                  0 0 40px rgba(255, 255, 255, 0.6),
                  0 0 60px rgba(255, 255, 255, 0.4),
                  2px 2px 4px rgba(0, 0, 0, 0.8),
                  -2px -2px 4px rgba(255, 255, 255, 0.3)
                `
              }}>
                {state.isLoading ? (
                  'PROCESSING...'
                ) : (
                  state.workStatus === 'clocked-in' ? 'CLOCK OUT' : 'CLOCK IN'
                )}
              </div>
            </button>
          </div>

          {/* STATUS MESSAGE */}
          {state.message && (
            <div style={{
              padding: '15px 30px',
              borderRadius: '10px',
              background: state.messageType === 'success' 
                ? 'rgba(0, 255, 0, 0.1)' 
                : state.messageType === 'error' 
                ? 'rgba(255, 0, 0, 0.1)' 
                : 'rgba(255, 255, 255, 0.1)',
              color: state.messageType === 'success' 
                ? '#00ff00' 
                : state.messageType === 'error' 
                ? '#ff0000' 
                : '#ffffff',
              textAlign: 'center',
              fontSize: '16px',
              fontWeight: '600',
              textShadow: '0 0 10px currentColor'
            }}>
              {state.message}
            </div>
          )}

          {/* WORK STATUS DISPLAY */}
          <div style={{
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            padding: '20px 30px',
            background: 'rgba(0, 0, 0, 0.3)',
            borderRadius: '15px',
            border: '1px solid rgba(255, 0, 0, 0.2)',
            boxShadow: 'inset 5px 5px 15px rgba(0, 0, 0, 0.5), inset -5px -5px 15px rgba(255, 255, 255, 0.1)'
          }}>
            <div style={{
              fontSize: '18px',
              fontWeight: '900',
              color: '#ffffff',
              textShadow: '0 0 15px rgba(255, 0, 0, 0.5)',
              letterSpacing: '2px',
              textTransform: 'uppercase'
            }}>
              {state.workStatus === 'clocked-in' ? 'WORKING' : 'CLOCKED OUT'}
            </div>
          </div>
        </div>
      </div>
    </>
  )
}
