/**
 * WebSocket Service for real-time communication
 */
import { User, WebSocketMessage } from '../types'

export class WebSocketService {
  private ws: WebSocket | null = null
  private reconnectAttempts = 0
  private maxReconnectAttempts = 5
  private reconnectInterval = 3000
  private messageHandlers = new Map<string, Set<(data: any) => void>>()
  private isConnected = false
  private user: User | null = null

  constructor() {
    console.log('🔌 WebSocket Service initialized')
  }

  connect(user: User): Promise<void> {
    return new Promise((resolve, reject) => {
      if (this.ws && this.ws.readyState === WebSocket.OPEN) {
        console.log('🟢 WebSocket already connected')
        resolve()
        return
      }

      this.user = user
      const wsUrl = `ws://localhost:8002/ws?userId=${user.id}&role=${user.role}&department=${user.department}`
      
      console.log('🔌 Connecting to WebSocket:', wsUrl)
      
      this.ws = new WebSocket(wsUrl)

      this.ws.onopen = () => {
        console.log('🟢 WebSocket connected successfully')
        this.isConnected = true
        this.reconnectAttempts = 0
        resolve()
      }

      this.ws.onclose = (event) => {
        console.log('🔴 WebSocket disconnected:', event.code, event.reason)
        this.isConnected = false
        
        if (event.code !== 1000 && this.reconnectAttempts < this.maxReconnectAttempts) {
          console.log(`🔄 Attempting to reconnect... (${this.reconnectAttempts + 1}/${this.maxReconnectAttempts})`)
          setTimeout(() => {
            this.reconnectAttempts++
            if (this.user) {
              this.connect(this.user)
            }
          }, this.reconnectInterval)
        }
      }

      this.ws.onerror = (error) => {
        console.error('❌ WebSocket error:', error)
        this.isConnected = false
        reject(error)
      }

      this.ws.onmessage = (event) => {
        try {
          const message: WebSocketMessage = JSON.parse(event.data)
          this.handleMessage(message)
        } catch (error) {
          console.error('❌ Error parsing WebSocket message:', error)
        }
      }
    })
  }

  disconnect() {
    if (this.ws) {
      console.log('🔴 Disconnecting WebSocket')
      this.ws.close(1000, 'User disconnected')
      this.ws = null
      this.isConnected = false
      this.user = null
    }
  }

  send(message: any) {
    if (this.ws && this.isConnected) {
      try {
        this.ws.send(JSON.stringify(message))
        console.log('📤 Sent WebSocket message:', message)
      } catch (error) {
        console.error('❌ Error sending WebSocket message:', error)
      }
    } else {
      console.warn('⚠️ WebSocket not connected. Cannot send message:', message)
    }
  }

  subscribe(messageType: string, callback: (data: any) => void): () => void {
    if (!this.messageHandlers.has(messageType)) {
      this.messageHandlers.set(messageType, new Set())
    }
    
    this.messageHandlers.get(messageType)!.add(callback)
    
    // Return unsubscribe function
    return () => {
      const handlers = this.messageHandlers.get(messageType)
      if (handlers) {
        handlers.delete(callback)
        if (handlers.size === 0) {
          this.messageHandlers.delete(messageType)
        }
      }
    }
  }

  private handleMessage(message: WebSocketMessage) {
    console.log('📨 Received WebSocket message:', message)
    
    // Handle specific message types
    switch (message.type) {
      case 'connection_established':
        console.log('✅ Connection established:', message.data)
        break
      case 'task_update':
        console.log('📋 Task update:', message.data)
        break
      case 'project_update':
        console.log('📁 Project update:', message.data)
        break
      case 'notification':
        console.log('🔔 Notification:', message.data)
        break
      case 'error':
        console.error('❌ WebSocket error:', message.message)
        break
      default:
        console.log('📨 Unknown message type:', message.type)
    }

    // Call registered handlers
    const handlers = this.messageHandlers.get(message.type)
    if (handlers) {
      handlers.forEach(handler => {
        try {
          handler(message.data || message)
        } catch (error) {
          console.error('❌ Error in message handler:', error)
        }
      })
    }
  }

  // Convenience methods
  joinRoom(room: string) {
    this.send({ type: 'join_room', room })
  }

  leaveRoom(room: string) {
    this.send({ type: 'leave_room', room })
  }

  sendPing() {
    this.send({ type: 'ping', timestamp: Date.now() })
  }

  updateStatus(status: 'online' | 'away' | 'busy' | 'offline') {
    this.send({ type: 'update_status', status })
  }

  sendTypingIndicator(room: string, typing: boolean = true) {
    this.send({ type: 'user_typing', room, typing })
  }

  // Getters
  get connected(): boolean {
    return this.isConnected
  }

  get currentUser(): User | null {
    return this.user
  }
}

// Create singleton instance
export const websocketService = new WebSocketService()

// Export default
export default websocketService
