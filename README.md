# CTNL - AI Work Management System

A comprehensive AI-powered work management system with multi-role authentication, approval workflows, and intelligent automation.

## 🚀 Features

### Core System Features
- **Multi-Role Authentication**: Admin, Manager, Staff, Accountant, HR, Staff-Admin
- **AI Integration**: OpenAI GPT-4, Lang<PERSON>hain workflows, voice recognition
- **Approval Workflows**: Procurement, leave requests, project assignments
- **Real-time Updates**: Live notifications and collaboration
- **Mobile-First Design**: Progressive Web App with offline capabilities

### Role-Based Dashboards
- **Admin**: System oversight, user management, diagnostics
- **Manager**: Team management, project oversight, approvals
- **Staff**: Personal tasks, time tracking, leave requests
- **Accountant**: Financial data, invoice management, procurement
- **Staff-Admin**: Administrative support functions

### Advanced Features
- GPS-enabled time tracking
- AI document analysis
- Voice command interface
- Real-time collaboration
- Automated task assignment
- Financial workflow automation

## 🛠 Technology Stack

### Frontend
- **Framework**: Next.js 14 with App Router
- **Language**: TypeScript
- **Styling**: Tailwind CSS + Shadcn/ui
- **State Management**: Zustand
- **Real-time**: Socket.IO

### Backend
- **Framework**: FastAPI (Python)
- **Database**: MongoDB Atlas
- **Authentication**: JWT + bcrypt
- **AI**: OpenAI GPT-4, LangChain
- **File Storage**: AWS S3 / Cloudinary
- **Cache**: Redis

### Infrastructure
- **Frontend Deployment**: Vercel
- **Backend Deployment**: Railway/Render
- **Database**: MongoDB Atlas
- **File Storage**: AWS S3
- **Cache**: Redis Cloud
- **Monitoring**: Sentry
- **Analytics**: Vercel Analytics

## 📁 Project Structure

```
CTNL-AI/
├── frontend/                 # Next.js React application
│   ├── src/
│   │   ├── app/             # App router pages
│   │   │   ├── auth/        # Authentication pages
│   │   │   ├── dashboard/   # Role-based dashboards
│   │   │   └── api/         # API routes
│   │   ├── components/      # Reusable UI components
│   │   │   ├── ui/          # Base UI components
│   │   │   ├── auth/        # Authentication components
│   │   │   ├── dashboard/   # Dashboard components
│   │   │   └── ai/          # AI-related components
│   │   ├── lib/            # Utilities and configurations
│   │   ├── hooks/          # Custom React hooks
│   │   └── types/          # TypeScript type definitions
│   └── public/             # Static assets
├── backend/                 # FastAPI Python application
│   ├── app/
│   │   ├── api/            # API routes
│   │   ├── core/           # Core configurations
│   │   ├── models/         # Database models
│   │   ├── services/       # Business logic
│   │   └── utils/          # Utility functions
│   └── requirements.txt
├── database/               # Database migrations and schemas
└── docs/                  # Documentation
```

## 🚦 Getting Started

### Prerequisites
- Node.js 18+
- Python 3.11+
- MongoDB Atlas account (or local MongoDB)
- Redis (local or Redis Cloud)
- OpenAI API key
- AWS S3 bucket (for file storage)

### Installation

1. **Clone the repository**
```bash
git clone <repository-url>
cd CTNL-AI
```

2. **Setup Frontend**
```bash
cd frontend
npm install
cp .env.example .env.local
# Configure environment variables
npm run dev
```

3. **Setup Backend**
```bash
cd backend
python -m venv .venv
source .venv/bin/activate  # On Windows: .venv\Scripts\activate
pip install -r requirements.txt
cp .env.example .env
# Configure environment variables
uvicorn main:app --reload
```

### Environment Variables

#### Frontend (.env.local)
```env
NEXT_PUBLIC_API_URL=http://localhost:8000
NEXT_PUBLIC_SOCKET_URL=http://localhost:8000
NEXT_PUBLIC_AWS_S3_BUCKET=your_s3_bucket_name
```

#### Backend (.env)
```env
MONGODB_URL=mongodb+srv://username:<EMAIL>/ctnl_db
REDIS_URL=redis://localhost:6379
OPENAI_API_KEY=your_openai_api_key
JWT_SECRET=your_jwt_secret_key
JWT_ALGORITHM=HS256
JWT_EXPIRE_MINUTES=30
AWS_ACCESS_KEY_ID=your_aws_access_key
AWS_SECRET_ACCESS_KEY=your_aws_secret_key
AWS_S3_BUCKET=your_s3_bucket_name
AWS_REGION=us-east-1
```

## 📚 Documentation

- [API Documentation](./docs/api.md)
- [Component Library](./docs/components.md)
- [Database Schema](./docs/database.md)
- [Deployment Guide](./docs/deployment.md)

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🆘 Support

For support, email <EMAIL> or join our Slack channel.
