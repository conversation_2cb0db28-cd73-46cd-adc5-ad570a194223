/**
 * Common type definitions for the application
 * All interfaces and types used across components
 */

// ===== USER TYPES =====
export interface User {
  id: string
  username: string
  email: string
  role: 'staff-accountant' | 'manager' | 'admin-staff' | 'admin'
  department: string
  is_online?: boolean
  created_at?: string
  updated_at?: string
}

// ===== AUTHENTICATION TYPES =====
export interface AuthResponse {
  access_token: string
  token_type: string
  user: User
}

export interface LoginCredentials {
  username: string
  password: string
}

export interface RegisterData {
  username: string
  email: string
  password: string
  role?: string
  department?: string
}

// ===== TASK TYPES =====
export interface Task {
  id: string
  title: string
  description?: string
  status: string
  priority: string
  assigned_to?: string
  assignee_id?: string
  created_by?: string
  project_id?: string
  created_at: string
  updated_at?: string
  due_date?: string
}

export interface TasksResponse {
  tasks: Task[]
  total: number
  page: number
  limit: number
}

// ===== PROJECT TYPES =====
export interface Project {
  id: string
  name: string
  description?: string
  status: string
  start_date?: string
  end_date?: string
  created_by?: string
  created_at: string
  updated_at?: string
}

export interface ProjectsResponse {
  projects: Project[]
  total: number
  page: number
  limit: number
}

// ===== DASHBOARD TYPES =====
export interface DashboardStats {
  total_tasks: number
  completed_tasks: number
  pending_tasks: number
  total_projects: number
  active_projects: number
}

// ===== WEBSOCKET TYPES =====
export interface WebSocketMessage {
  type: string
  data?: any
  message?: string
  timestamp?: string
}

// ===== API TYPES =====
export interface ApiResponse<T = any> {
  success: boolean
  data?: T
  message?: string
  error?: string
}

export interface ApiError {
  message: string
  status?: number
  code?: string
}

// ===== NOTIFICATION TYPES =====
export interface Notification {
  id: string
  type: 'info' | 'success' | 'warning' | 'error'
  title: string
  message: string
  timestamp: string
  read?: boolean
}

// ===== ROLES & PERMISSIONS TYPES =====
export interface Role {
  id: string
  name: string
  description: string
  permissions: string[]
}

export interface Permission {
  id: string
  name: string
  category: string
}

// ===== MEMO TYPES =====
export interface Memo {
  id: string
  title: string
  content: string
  type: 'announcement' | 'approval_request' | 'general'
  priority: 'low' | 'medium' | 'high'
  status: 'pending' | 'approved' | 'rejected'
  author_id: string
  author_name: string
  recipients: 'all' | 'managers' | 'department'
  department?: string
  approved_by?: string
  created_at: string
  updated_at: string
}

// ===== TIME TRACKING TYPES =====
export interface TimeEntry {
  id: string
  user_id: string
  type: 'clock-in' | 'clock-out' | 'break-start' | 'break-end'
  timestamp: string
  location?: {
    latitude: number
    longitude: number
  }
}

// ===== REPORTS TYPES =====
export interface Report {
  id: string
  type: string
  data: any
  generated_at: string
}

// ===== COMPONENT PROPS TYPES =====
export interface LoadingState {
  isLoading: boolean
  error?: string | null
}

// Export all types as a namespace as well
export * as Types from './index'
