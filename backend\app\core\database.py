"""
MongoDB database connection and utilities
"""
from motor.motor_asyncio import AsyncIOMotorClient, AsyncIOMotorDatabase
from pymongo.errors import ConnectionFailure
from pymongo import ReturnDocument
from fastapi import HTTPException
import logging
from typing import Optional
from datetime import datetime
from .config import settings

logger = logging.getLogger(__name__)


class MongoDB:
    def __init__(self):
        self.client: Optional[AsyncIOMotorClient] = None
        self.database: Optional[AsyncIOMotorDatabase] = None
        self._connected = False

    @property
    def is_connected(self):
        return self._connected and self.client is not None and self.database is not None


mongodb = MongoDB()


async def connect_to_mongo():
    """Create database connection or use in-memory fallback"""
    # Skip if already connected
    if mongodb.is_connected:
        logger.info("MongoDB already connected, skipping...")
        return

    try:
        logger.info("Connecting to MongoDB...")

        # Simple connection for local MongoDB
        mongodb.client = AsyncIOMotorClient(
            settings.MONGODB_URL,
            serverSelectionTimeoutMS=5000
        )

        # Test the connection with a simple ping
        await mongodb.client.admin.command('ping')

        mongodb.database = mongodb.client[settings.DATABASE_NAME]
        mongodb._connected = True

        logger.info(f"✅ Connected to MongoDB successfully!")
        logger.info(f"📊 Database: {settings.DATABASE_NAME}")

        # Create indexes only once
        try:
            await create_indexes()
            logger.info("🎉 MongoDB setup completed successfully!")
        except Exception as index_error:
            logger.warning(f"Index creation failed: {index_error}")
            logger.info("Continuing with MongoDB connection without indexes")

    except Exception as e:
        logger.warning(f"❌ MongoDB connection failed: {e}")
        logger.info("🔄 Running in development mode with in-memory storage")

        # Set to None for in-memory fallback
        mongodb.client = None
        mongodb.database = None
        mongodb._connected = False

        logger.info("✅ In-memory storage initialized for development")


async def close_mongo_connection():
    """Close database connection"""
    if mongodb.client:
        mongodb.client.close()
        logger.info("Disconnected from MongoDB")


async def get_database() -> AsyncIOMotorDatabase:
    """Get database instance"""
    if mongodb.database is None:
        logger.warning("Database not connected, attempting to reconnect...")
        await connect_to_mongo()

    if mongodb.database is None:
        logger.error("Database connection failed, cannot proceed")
        raise HTTPException(
            status_code=500,
            detail="Database connection unavailable"
        )

    return mongodb.database


async def create_indexes():
    """Create database indexes for better performance"""
    if mongodb.database is None:
        logger.info("Skipping index creation - using in-memory storage")
        return

    try:
        db = mongodb.database

        # Users collection indexes
        await db.users.create_index("email", unique=True)
        await db.users.create_index("employee_id", unique=True)
        await db.users.create_index([("department", 1), ("role", 1)])
        await db.users.create_index("is_active")

        # Projects collection indexes
        await db.projects.create_index([("status", 1), ("created_at", -1)])
        await db.projects.create_index("manager_id")
        await db.projects.create_index("team_members")
        await db.projects.create_index([("department", 1), ("status", 1)])

        # Tasks collection indexes
        await db.tasks.create_index([("assignee_id", 1), ("status", 1)])
        await db.tasks.create_index([("project_id", 1), ("status", 1)])
        await db.tasks.create_index([("due_date", 1), ("priority", 1)])
        await db.tasks.create_index("created_by")

        # Time logs collection indexes
        await db.time_logs.create_index([("user_id", 1), ("date", -1)])
        await db.time_logs.create_index([("project_id", 1), ("date", -1)])
        await db.time_logs.create_index("is_approved")

        # Leave requests collection indexes
        await db.leave_requests.create_index([("user_id", 1), ("status", 1)])
        await db.leave_requests.create_index([("start_date", 1), ("end_date", 1)])
        await db.leave_requests.create_index("approver_id")

        logger.info("Database indexes created successfully")

    except Exception as e:
        logger.warning(f"Failed to create indexes: {e}")
        logger.info("Continuing without indexes - performance may be affected")
    
    # Procurement requests collection indexes
    await db.procurement_requests.create_index([("requester_id", 1), ("status", 1)])
    await db.procurement_requests.create_index([("department", 1), ("status", 1)])
    await db.procurement_requests.create_index("created_at")
    
    # Invoices collection indexes
    await db.invoices.create_index([("status", 1), ("due_date", 1)])
    await db.invoices.create_index("vendor")
    await db.invoices.create_index("department")
    
    # Documents collection indexes
    await db.documents.create_index([("uploaded_by", 1), ("created_at", -1)])
    await db.documents.create_index("document_type")
    await db.documents.create_index("tags")
    await db.documents.create_index([("department", 1), ("is_public", 1)])
    
    # Notifications collection indexes
    await db.notifications.create_index([("user_id", 1), ("is_read", 1)])
    await db.notifications.create_index([("created_at", -1)])
    await db.notifications.create_index("notification_type")
    
    # Audit logs collection indexes
    await db.audit_logs.create_index([("user_id", 1), ("timestamp", -1)])
    await db.audit_logs.create_index("action")
    await db.audit_logs.create_index("resource_type")
    
    # AI conversations collection indexes
    await db.ai_conversations.create_index([("user_id", 1), ("created_at", -1)])
    await db.ai_conversations.create_index("conversation_type")
    
    # Approvals collection indexes
    await db.approvals.create_index([("approver_id", 1), ("status", 1)])
    await db.approvals.create_index([("resource_type", 1), ("resource_id", 1)])
    await db.approvals.create_index("workflow_step")
    
    # Assets collection indexes
    await db.assets.create_index("asset_tag", unique=True)
    await db.assets.create_index([("department", 1), ("status", 1)])
    await db.assets.create_index("assigned_to")
    
    # Fleet collection indexes
    await db.fleet.create_index("vehicle_number", unique=True)
    await db.fleet.create_index([("status", 1), ("department", 1)])
    await db.fleet.create_index("assigned_driver")
    
    # Construction projects collection indexes
    await db.construction_projects.create_index([("status", 1), ("start_date", 1)])
    await db.construction_projects.create_index("project_manager")
    await db.construction_projects.create_index("location")
    
    # Battery inventory collection indexes
    await db.battery_inventory.create_index("battery_id", unique=True)
    await db.battery_inventory.create_index([("site_id", 1), ("status", 1)])
    await db.battery_inventory.create_index("installation_date")
    
    # Telecom sites collection indexes
    await db.telecom_sites.create_index("site_id", unique=True)
    await db.telecom_sites.create_index([("region", 1), ("status", 1)])
    await db.telecom_sites.create_index("coordinates", "2dsphere")  # Geospatial index
    
    logger.info("Database indexes created successfully")


# Database utility functions
async def get_next_sequence(collection_name: str, field_name: str = "id") -> int:
    """Get next sequence number for auto-incrementing fields"""
    db = mongodb.database
    
    result = await db.counters.find_one_and_update(
        {"_id": f"{collection_name}_{field_name}"},
        {"$inc": {"sequence": 1}},
        upsert=True,
        return_document=ReturnDocument.AFTER
    )
    
    return result["sequence"]


async def create_audit_log(user_id: str, action: str, resource_type: str, 
                          resource_id: str, details: dict = None):
    """Create an audit log entry"""
    db = mongodb.database
    
    audit_entry = {
        "user_id": user_id,
        "action": action,
        "resource_type": resource_type,
        "resource_id": resource_id,
        "details": details or {},
        "timestamp": datetime.utcnow(),
        "ip_address": None,  # Will be set by middleware
        "user_agent": None   # Will be set by middleware
    }
    
    await db.audit_logs.insert_one(audit_entry)


# Health check function
async def check_database_health() -> dict:
    """Check database connection health"""
    try:
        # Ping the database
        await mongodb.client.admin.command('ping')
        
        # Get database stats
        stats = await mongodb.database.command("dbStats")
        
        return {
            "status": "healthy",
            "database": settings.DATABASE_NAME,
            "collections": stats.get("collections", 0),
            "data_size": stats.get("dataSize", 0),
            "storage_size": stats.get("storageSize", 0)
        }
    except Exception as e:
        return {
            "status": "unhealthy",
            "error": str(e)
        }
